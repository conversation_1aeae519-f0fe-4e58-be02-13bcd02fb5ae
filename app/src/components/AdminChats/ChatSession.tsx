"use client";
import { db } from "@/services/firebase.config";
import useDashboardStore from "@/stores/dashboard.store";
import { collection, onSnapshot, orderBy, query } from "firebase/firestore";
import React, { useEffect, useRef } from "react";
import { ScrollArea } from "../ui/scroll-area";
import Bubble from "../chat/bubble";

type Message = {
  id: string;
  message: string;
  role: "user" | "assistant";
  createdAt: any;
};

interface FirestoreDate {
  seconds: number;
  nanoseconds: number;
}

export default function ChatSession({
  chatSessionId,
}: {
  chatSessionId: string;
}) {
  const selectedClient = useDashboardStore((state) => state.selectedClient);
  const [messages, setMessages] = React.useState([] as Message[]);

  const loadDocuments = async () => {
    const unsubscribe = onSnapshot(
      query(
        collection(
          db,
          "tenants",
          selectedClient?.value,
          "chatSessions",
          chatSessionId,
          "messages"
        ),
        orderBy("createdAt", "asc")
      ),
      (querySnapshot) => {
        const messages = [] as any;
        querySnapshot.forEach((doc: any) => {
          messages.push({
            id: doc.id,
            ...doc.data(),
          });
        });
        setMessages(messages);
        return messages;
      }
    );

    return [unsubscribe];
  };

  React.useEffect(() => {
    if (!(selectedClient && selectedClient?.value)) {
      return;
    }

    let unsubscribes = [] as any;

    const processEffect = async () => {
      if (selectedClient.value) {
        unsubscribes = await loadDocuments();
      }
    };

    processEffect();

    return () => {
      unsubscribes.forEach((unsubscribe: any) => {
        unsubscribe();
      });
    };
  }, [selectedClient]);

  const scrollAreaRef = useRef<null | HTMLDivElement>(null);

  useEffect(() => {
    // Scroll to the bottom when the messages change
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({
        top: scrollAreaRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  }, [messages]);

  const convertDate = (date: FirestoreDate) => {
    return new Date(date.seconds * 1000);
  };

  const orderMessagesByDate = (messages: Message[]) => {
    return messages.sort((a, b) => {
      const aDate = convertDate(a.createdAt as FirestoreDate);
      const bDate = convertDate(b.createdAt as FirestoreDate);
      return aDate.getTime() - bDate.getTime();
    });
  };

  return (
    <div>
      <ScrollArea
        ref={scrollAreaRef}
        className="h-[90vh] overflow-y-auto w-full spacy-y-4 pr-4"
      >
        {orderMessagesByDate(messages).map((message, index) => (
          <Bubble
            admin={true}
            key={`message-${index}`}
            message={message}
            chatSessionId={chatSessionId}
            tenantId={selectedClient?.value}
            previousMessage={messages[index - 1]}
          />
        ))}
      </ScrollArea>
    </div>
  );
}
