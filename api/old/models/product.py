class Product:
    def __init__(
        self,
        id: int,
        name: str,
        description: str,
        short_description: str,
        price: str,
        regular_price: str,
        sale_price: str,
        sku: str,
        stock_status: str,
        url: str,
        updated_at: str,
    ):
        self.id = id
        self.name = name
        self.description = description
        self.short_description = short_description
        self.price = price
        self.regular_price = regular_price
        self.sale_price = sale_price
        self.sku = sku
        self.stock_status = stock_status
        self.url = url
        self.updated_at = updated_at
