from marqo import Client

# Create marqo client
mq = Client("http://ns3164501.ip-51-89-234.eu:8882")

# Name your index
index_name = "text-search"

# mq.delete_index(index_name) 
# # Create the movie index
# mq.create_index(index_name, model="flax-sentence-embeddings/all_datasets_v4_mpnet-base")


# Add documents (movie descriptions) to the index
# mq.index(index_name).add_documents(
#     [
#         {
#             "Title": "Inception",
#             "Description": "A mind-bending thriller about dream invasion and manipulation.",
#         },
#         {
#             "Title": "Shrek",
#             "Description": "An ogre's peaceful life is disrupted by a horde of fairy tale characters who need his help.",
#         },
#         {
#             "Title": "Interstellar",
#             "Description": "A team of explorers travel through a wormhole in space to ensure humanity's survival.",
#         },
#         {
#             "Title": "The Martian",
#             "Description": "An astronaut becomes stranded on Mars and must find a way to survive.",
#         },
#     ],
#     tensor_fields=["Description"],
# )



# Perform a search query on the index
results = mq.index(index_name).search(q="I need a fantasy movie")

# Print the search results
for result in results["hits"]:
    print(
        f"Title: {result['Title']}, Description: {result['Description']}. Score: {result['_score']}"
    )
    