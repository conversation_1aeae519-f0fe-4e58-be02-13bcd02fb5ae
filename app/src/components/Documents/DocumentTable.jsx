import React, { useEffect } from "react";
import { addDoc, deleteDoc, doc, collection } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { db } from "@/services/firebase.config";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { AiOutlinePlusCircle } from "react-icons/ai";
import { Button } from "../ui/button";
import Link from "next/link";
import { Skeleton } from "../ui/skeleton";

export default function DocumentTable({ clientName, documents, actions = [] }) {
  const router = useRouter();

  const [loaded, setLoaded] = React.useState(false);

  const createNewDocument = async () => {
    const docRef = await addDoc(
      collection(db, "tenants", clientName, "documents"),
      {
        title: "New Document",
        data: "# Welcome to your new document",
        lastModified: new Date().toISOString(),
      }
    );
    router.push(`/hub/documents/${clientName}/document/${docRef.id}`);
  };

  const deleteDocument = async (docId) => {
    await deleteDoc(doc(db, "tenants", clientName, "documents", docId));
  };

  useEffect(() => {
    if (documents) {
      setLoaded(true);
    }
  }, [documents]);

  return (
    <>
      {loaded && (
        <Table className="rounded-lg">
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Last modified</TableHead>
              {actions.length > 0 && actions.map((ActionComponent) => <TableHead></TableHead>)}
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            {documents &&
              documents
                .sort((a, b) => {
                  const aDate = new Date(a.lastModified);
                  const bDate = new Date(b.lastModified);
                  return bDate - aDate;
                })
                .map((doc) => (
                  <TableRow key={doc.id}>
                    <TableCell>
                      <Link
                        href={`/hub/documents/${clientName}/document/${doc.id}`}
                        className="font-bold hover:underline"
                      >
                        {doc.title}
                      </Link>
                    </TableCell>
                    <TableCell>
                      {new Date(doc.lastModified).toLocaleDateString("en-GB", {
                        day: "numeric",
                        month: "numeric",
                        year: "numeric",
                        hour: "numeric",
                        minute: "numeric",
                      })}
                    </TableCell>
                    {/* Render additional actions */}
                    {actions.map((ActionComponent, index) => (
                      <TableCell>
                        <ActionComponent key={index} document={doc} id={doc.id} />
                      </TableCell>
                    ))}
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          onClick={() => {
                            if (
                              window.confirm(
                                "Are you sure you wish to delete this document?"
                              )
                            )
                              deleteDocument(doc.id);
                          }}
                          variant="destructive"
                        >
                          Delete
                        </Button>

                      </div>
                    </TableCell>
                  </TableRow>
                ))}
            {/* Now add actions */}
            <TableRow>
              <TableCell>
                <Button onClick={createNewDocument} className="">
                  <AiOutlinePlusCircle className="inline-block mr-2" />
                  New document
                </Button>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      )}
    </>
  );
}
