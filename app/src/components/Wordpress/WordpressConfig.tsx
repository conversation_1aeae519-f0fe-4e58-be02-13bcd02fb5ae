"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Suspense, useEffect, useState } from "react";

import { doc, getDoc, updateDoc } from "firebase/firestore";
import { db } from "@/services/firebase.config";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import useDashboardStore from "@/stores/dashboard.store";

const apiKeysFormSchema = z.object({
  url: z.string().url({ message: "Please enter a valid URL." }),
  username: z.string(),
  password: z.string(),
  consumerKey: z.string(),
  consumerSecret: z.string(),
  openAIKey: z.string(),
});

type ApiKeysFormValues = z.infer<typeof apiKeysFormSchema>;

const fieldDetails = {
  url: { label: "URL", description: "Your website URL" },
  username: { label: "Username", description: "Your username" },
  password: { label: "Password", description: "Your password" },
  consumerKey: { label: "Consumer Key", description: "Your consumer key" },
  consumerSecret: {
    label: "Consumer Secret",
    description: "Your consumer secret",
  },
  openAIKey: { label: "OpenAI Key", description: "Your OpenAI key" },
};

export default function WordpressConfig() {
  const [config, setConfig] = useState<ApiKeysFormValues | null>(null);
  const clientName = useDashboardStore((state) => state.selectedClient)?.value;
  const [loaded, setLoaded] = useState(false);
  const form = useForm<ApiKeysFormValues>({
    resolver: zodResolver(apiKeysFormSchema),
    defaultValues: {
      url: "",
      username: "",
      password: "",
      consumerKey: "",
      consumerSecret: "",
      openAIKey: "",
    },
    mode: "onChange",
  });

  useEffect(() => {
    const fetchConfig = async () => {
      const docRef = doc(db, "tenants", clientName);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const fetchedConfig = docSnap.data().wordpressConfig;
        form.reset(fetchedConfig); // update form with fetched data
        setConfig(fetchedConfig);
      }
      setLoaded(true);
    };

    fetchConfig();
  }, [clientName]);

  const onSubmit = async (data: ApiKeysFormValues) => {
    const docRef = doc(db, "tenants", clientName);
    await updateDoc(docRef, { wordpressConfig: data });
    toast({
      title: "Success",
      description: "API keys updated successfully.",
    });
  };

  return (
    <>
      {!loaded && <div>Loading...</div>}
      {loaded && config && (
        <Form {...form}>
          <form className="space-y-8" onSubmit={form.handleSubmit(onSubmit)}>
            {Object.keys(apiKeysFormSchema.shape).map((key) => (
              <FormField
                key={key}
                control={form.control}
                name={key as keyof ApiKeysFormValues}
                render={({ field }: any) => (
                  <FormItem>
                    <FormLabel>
                      {fieldDetails[key as keyof typeof fieldDetails].label}
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormDescription>
                      {
                        fieldDetails[key as keyof typeof fieldDetails]
                          .description
                      }
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ))}
            <Button type="submit">Update API Keys</Button>
          </form>
        </Form>
      )}
    </>
  );
}
