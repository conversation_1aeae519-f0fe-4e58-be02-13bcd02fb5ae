from typing import List, Dict, Union, Optional, Any
from llama_index.core.readers.base import BaseReader
from llama_index.core.schema import Document
import requests
import re

from models.tenant import WordPressConfig

def strip_html_tags(text: str) -> str:
    clean = re.compile('<.*?>')
    return re.sub(clean, '', text).strip()

class WordpressReader(BaseReader):

    def __init__(self, config: WordPressConfig):
        self.url = config.url
        self.username = config.username
        self.password = config.password

    def load_data(self, *args: Any, **load_kwargs: Any) -> List[Document]:
        results = []
        articles = self.get_all_posts()

        for article in articles:
            body = strip_html_tags(article.get('content', {}).get('rendered', ''))
            title = article.get('title', {}).get('rendered', '')

            results.append(
                Document(
                    doc_id = article.get('id', ''),
                    doc_hash = article.get('id', ''),
                    text = body,
                    metadata = {
                        'title': title,
                        'url': article.get('link', ''),
                        'updated_at': article.get('modified', '')
                    }
                )
            )
        return results

    def get_all_posts(self) -> List[Dict]:
        posts = []
        next_page = 1

        while next_page:
            response, next_page = self.get_posts_page(next_page)
            if response:
                posts.extend(response)

        return posts

    def get_posts_page(self, current_page: int = 1) -> Union[List[Dict], int]:
        url = f"{self.url}/wp-json/wp/v2/pages?per_page=100&page={current_page}"
        response = requests.get(url)
        data = response.json()
        num_pages = int(response.headers.get('X-WP-TotalPages', 1))
        next_page = current_page + 1 if num_pages > current_page else None

        return data, next_page
