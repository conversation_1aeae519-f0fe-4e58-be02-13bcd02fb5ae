lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  '@blocknote/core':
    specifier: ^0.8.2
    version: 0.8.2(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2)
  '@blocknote/react':
    specifier: ^0.8.2
    version: 0.8.2(@tiptap/core@2.2.4)(@tiptap/pm@2.2.4)(@types/react@18.2.14)(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2)(react-dom@18.2.0)(react@18.2.0)
  '@emotion/react':
    specifier: ^11.11.1
    version: 11.11.1(@types/react@18.2.14)(react@18.2.0)
  '@emotion/styled':
    specifier: ^11.11.0
    version: 11.11.0(@emotion/react@11.11.1)(@types/react@18.2.14)(react@18.2.0)
  '@hookform/resolvers':
    specifier: ^3.1.1
    version: 3.1.1(react-hook-form@7.45.2)
  '@mdxeditor/editor':
    specifier: ^0.22.5
    version: 0.22.5(@lezer/common@1.2.1)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)(yjs@13.6.14)
  '@radix-ui/react-avatar':
    specifier: 1.0.3
    version: 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-context-menu':
    specifier: ^2.2.2
    version: 2.2.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-dialog':
    specifier: 1.0.5
    version: 1.0.5(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-dropdown-menu':
    specifier: 2.0.5
    version: 2.0.5(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-hover-card':
    specifier: 1.0.6
    version: 1.0.6(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-icons':
    specifier: 1.3.0
    version: 1.3.0(react@18.2.0)
  '@radix-ui/react-label':
    specifier: 2.0.2
    version: 2.0.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-navigation-menu':
    specifier: 1.1.3
    version: 1.1.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-popover':
    specifier: 1.0.6
    version: 1.0.6(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-scroll-area':
    specifier: 1.0.4
    version: 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-select':
    specifier: 1.2.2
    version: 1.2.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-separator':
    specifier: 1.0.3
    version: 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-slot':
    specifier: 1.0.2
    version: 1.0.2(@types/react@18.2.14)(react@18.2.0)
  '@radix-ui/react-switch':
    specifier: 1.0.3
    version: 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-tabs':
    specifier: 1.0.4
    version: 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-toast':
    specifier: 1.1.4
    version: 1.1.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@radix-ui/react-tooltip':
    specifier: 1.0.7
    version: 1.0.7(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@remirror/extension-markdown':
    specifier: ^2.0.13
    version: 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
  '@remirror/pm':
    specifier: ^2.0.8
    version: 2.0.8
  '@remirror/react':
    specifier: ^2.0.35
    version: 2.0.35(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  '@remirror/react-editors':
    specifier: ^1.0.38
    version: 1.0.38(@emotion/react@11.11.1)(@emotion/styled@11.11.0)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)(styled-components@5.3.11)
  '@tanstack/react-table':
    specifier: ^8.9.3
    version: 8.9.3(react-dom@18.2.0)(react@18.2.0)
  '@types/node':
    specifier: 20.3.3
    version: 20.3.3
  '@types/react':
    specifier: 18.2.14
    version: 18.2.14
  '@types/react-dom':
    specifier: 18.2.6
    version: 18.2.6
  '@types/uuid':
    specifier: ^9.0.2
    version: 9.0.2
  '@xyflow/react':
    specifier: ^12.3.1
    version: 12.3.1(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  autoprefixer:
    specifier: 10.4.14
    version: 10.4.14(postcss@8.4.24)
  axios:
    specifier: ^1.4.0
    version: 1.4.0
  beautiful-dom:
    specifier: ^1.0.9
    version: 1.0.9
  cheerio:
    specifier: 1.0.0-rc.12
    version: 1.0.0-rc.12
  class-variance-authority:
    specifier: ^0.7.0
    version: 0.7.0
  clsx:
    specifier: ^2.0.0
    version: 2.0.0
  cmdk:
    specifier: ^0.2.0
    version: 0.2.0(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
  date-fns:
    specifier: ^2.30.0
    version: 2.30.0
  encoding:
    specifier: ^0.1.13
    version: 0.1.13
  firebase:
    specifier: ^9.23.0
    version: 9.23.0(encoding@0.1.13)
  firebase-admin:
    specifier: ^11.10.1
    version: 11.10.1(encoding@0.1.13)
  formik:
    specifier: ^2.4.3
    version: 2.4.3(react@18.2.0)
  image-size:
    specifier: ^1.1.1
    version: 1.1.1
  javascript-obfuscator:
    specifier: ^4.0.2
    version: 4.0.2
  lodash.debounce:
    specifier: ^4.0.8
    version: 4.0.8
  lucide-react:
    specifier: ^0.263.1
    version: 0.263.1(react@18.2.0)
  multiply-llamaindex:
    specifier: ^0.0.25
    version: 0.0.25
  next:
    specifier: 13.4.7
    version: 13.4.7(@babel/core@7.24.3)(react-dom@18.2.0)(react@18.2.0)(sass@1.63.6)
  next-themes:
    specifier: ^0.2.1
    version: 0.2.1(next@13.4.7)(react-dom@18.2.0)(react@18.2.0)
  node-loader:
    specifier: ^2.0.0
    version: 2.0.0(webpack@5.91.0)
  postcss:
    specifier: 8.4.24
    version: 8.4.24
  prosemirror-commands:
    specifier: ^1.5.2
    version: 1.5.2
  react:
    specifier: 18.2.0
    version: 18.2.0
  react-color:
    specifier: ^2.19.3
    version: 2.19.3(react@18.2.0)
  react-daisyui:
    specifier: ^4.0.0
    version: 4.0.0(daisyui@3.1.7)(react-dom@18.2.0)(react@18.2.0)(tailwindcss@3.3.2)
  react-day-picker:
    specifier: ^8.8.1
    version: 8.8.1(date-fns@2.30.0)(react@18.2.0)
  react-dnd:
    specifier: ^16.0.1
    version: 16.0.1(@types/node@20.3.3)(@types/react@18.2.14)(react@18.2.0)
  react-dnd-html5-backend:
    specifier: ^16.0.1
    version: 16.0.1
  react-dom:
    specifier: 18.2.0
    version: 18.2.0(react@18.2.0)
  react-firebase-hooks:
    specifier: ^5.1.1
    version: 5.1.1(firebase@9.23.0)(react@18.2.0)
  react-hook-form:
    specifier: ^7.45.2
    version: 7.45.2(react@18.2.0)
  react-icons:
    specifier: ^4.10.1
    version: 4.10.1(react@18.2.0)
  react-loader-spinner:
    specifier: ^5.3.4
    version: 5.3.4(@babel/core@7.24.3)(react-dom@18.2.0)(react@18.2.0)
  react-toastify:
    specifier: ^9.1.3
    version: 9.1.3(react-dom@18.2.0)(react@18.2.0)
  remirror:
    specifier: ^2.0.36
    version: 2.0.36(@remirror/pm@2.0.8)(@types/node@20.3.3)
  sass:
    specifier: ^1.63.6
    version: 1.63.6
  tailwind-merge:
    specifier: ^1.14.0
    version: 1.14.0
  tailwindcss:
    specifier: 3.3.2
    version: 3.3.2
  tailwindcss-animate:
    specifier: ^1.0.6
    version: 1.0.6(tailwindcss@3.3.2)
  typescript:
    specifier: 5.1.6
    version: 5.1.6
  uuid:
    specifier: ^9.0.0
    version: 9.0.0
  xml2js:
    specifier: ^0.6.2
    version: 0.6.2
  zod:
    specifier: ^3.21.4
    version: 3.21.4
  zustand:
    specifier: ^4.4.0
    version: 4.4.0(@types/react@18.2.14)(react@18.2.0)
  zustand-computed:
    specifier: ^1.3.5
    version: 1.3.5(react@18.2.0)(zustand@4.4.0)

devDependencies:
  '@tailwindcss/typography':
    specifier: ^0.5.9
    version: 0.5.9(tailwindcss@3.3.2)
  '@types/react-color':
    specifier: ^3.0.6
    version: 3.0.6
  '@types/xml2js':
    specifier: ^0.4.14
    version: 0.4.14
  daisyui:
    specifier: ^3.1.7
    version: 3.1.7(postcss@8.4.24)

packages:

  /@alloc/quick-lru@5.2.0:
    resolution: {integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==}
    engines: {node: '>=10'}

  /@ampproject/remapping@2.2.1:
    resolution: {integrity: sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.19
    dev: false

  /@ampproject/remapping@2.3.0:
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
    dev: false

  /@anthropic-ai/sdk@0.6.2(encoding@0.1.13):
    resolution: {integrity: sha512-fB9PUj9RFT+XjkL+E9Ol864ZIJi+1P8WnbHspN3N3/GK2uSzjd0cbVIKTGgf4v3N8MwaQu+UWnU7C4BG/fap/g==}
    dependencies:
      '@types/node': 18.17.12
      '@types/node-fetch': 2.6.4
      abort-controller: 3.0.0
      agentkeepalive: 4.5.0
      digest-fetch: 1.3.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.6.12(encoding@0.1.13)
    transitivePeerDependencies:
      - encoding
    dev: false

  /@babel/code-frame@7.22.10:
    resolution: {integrity: sha512-/KKIMG4UEL35WmI9OlvMhurwtytjvXoFcGNrOvyG9zIzA8YmPjVtIZUf7b05+TPO7G7/GEmLHDaoCgACHl9hhA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.22.10
      chalk: 2.4.2
    dev: false

  /@babel/code-frame@7.24.2:
    resolution: {integrity: sha512-y5+tLQyV8pg3fsiln67BVLD1P13Eg4lh5RW9mF0zUuvLrv9uIQ4MCL+CRT+FTsBlBjcIan6PGsLcBN0m3ClUyQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.24.2
      picocolors: 1.0.0
    dev: false

  /@babel/compat-data@7.22.9:
    resolution: {integrity: sha512-5UamI7xkUcJ3i9qVDS+KFDEK8/7oJ55/sJMB1Ge7IEapr7KfdfV/HErR+koZwOfd+SgtFKOKRhRakdg++DcJpQ==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/compat-data@7.24.1:
    resolution: {integrity: sha512-Pc65opHDliVpRHuKfzI+gSA4zcgr65O4cl64fFJIWEEh8JoHIHh0Oez1Eo8Arz8zq/JhgKodQaxEwUPRtZylVA==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/core@7.22.10:
    resolution: {integrity: sha512-fTmqbbUBAwCcre6zPzNngvsI0aNrPZe77AeqvDxWM9Nm+04RrJ3CAmGHA9f7lJQY6ZMhRztNemy4uslDxTX4Qw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.2.1
      '@babel/code-frame': 7.22.10
      '@babel/generator': 7.22.10
      '@babel/helper-compilation-targets': 7.22.10
      '@babel/helper-module-transforms': 7.22.9(@babel/core@7.22.10)
      '@babel/helpers': 7.22.10
      '@babel/parser': 7.22.10
      '@babel/template': 7.22.5
      '@babel/traverse': 7.22.10(supports-color@5.5.0)
      '@babel/types': 7.22.10
      convert-source-map: 1.9.0
      debug: 4.3.4(supports-color@5.5.0)
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/core@7.24.3:
    resolution: {integrity: sha512-5FcvN1JHw2sHJChotgx8Ek0lyuh4kCKelgMTTqhYJJtloNvUfpAFMeNQUtdlIaktwrSV9LtCdqwk48wL2wBacQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.24.2
      '@babel/generator': 7.24.1
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.24.3)
      '@babel/helpers': 7.24.1
      '@babel/parser': 7.24.1
      '@babel/template': 7.24.0
      '@babel/traverse': 7.24.1
      '@babel/types': 7.24.0
      convert-source-map: 2.0.0
      debug: 4.3.4(supports-color@5.5.0)
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/generator@7.22.10:
    resolution: {integrity: sha512-79KIf7YiWjjdZ81JnLujDRApWtl7BxTqWD88+FFdQEIOG8LJ0etDOM7CXuIgGJa55sGOwZVwuEsaLEm0PJ5/+A==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.19
      jsesc: 2.5.2
    dev: false

  /@babel/generator@7.24.1:
    resolution: {integrity: sha512-DfCRfZsBcrPEHUfuBMgbJ1Ut01Y/itOs+hY2nFLgqsqXd52/iSiVq5TITtUasIUgm+IIKdY2/1I7auiQOEeC9A==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.24.0
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2
    dev: false

  /@babel/helper-annotate-as-pure@7.22.5:
    resolution: {integrity: sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: false

  /@babel/helper-compilation-targets@7.22.10:
    resolution: {integrity: sha512-JMSwHD4J7SLod0idLq5PKgI+6g/hLD/iuWBq08ZX49xE14VpVEojJ5rHWptpirV2j020MvypRLAXAO50igCJ5Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': 7.22.9
      '@babel/helper-validator-option': 7.22.5
      browserslist: 4.21.10
      lru-cache: 5.1.1
      semver: 6.3.1
    dev: false

  /@babel/helper-compilation-targets@7.23.6:
    resolution: {integrity: sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': 7.24.1
      '@babel/helper-validator-option': 7.23.5
      browserslist: 4.23.0
      lru-cache: 5.1.1
      semver: 6.3.1
    dev: false

  /@babel/helper-environment-visitor@7.22.20:
    resolution: {integrity: sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-environment-visitor@7.22.5:
    resolution: {integrity: sha512-XGmhECfVA/5sAt+H+xpSg0mfrHq6FzNr9Oxh7PSEBBRUb/mL7Kz3NICXb194rCqAEdxkhPT1a88teizAFyvk8Q==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-function-name@7.22.5:
    resolution: {integrity: sha512-wtHSq6jMRE3uF2otvfuD3DIvVhOsSNshQl0Qrd7qC9oQJzHvOL4qQXlQn2916+CXGywIjpGuIkoyZRRxHPiNQQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.22.5
      '@babel/types': 7.22.10
    dev: false

  /@babel/helper-function-name@7.23.0:
    resolution: {integrity: sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.24.0
      '@babel/types': 7.24.0
    dev: false

  /@babel/helper-hoist-variables@7.22.5:
    resolution: {integrity: sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: false

  /@babel/helper-module-imports@7.22.5:
    resolution: {integrity: sha512-8Dl6+HD/cKifutF5qGd/8ZJi84QeAKh+CEe1sBzz8UayBBGg1dAIJrdHOcOM5b2MpzWL2yuotJTtGjETq0qjXg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: false

  /@babel/helper-module-imports@7.24.3:
    resolution: {integrity: sha512-viKb0F9f2s0BCS22QSF308z/+1YWKV/76mwt61NBzS5izMzDPwdq1pTrzf+Li3npBWX9KdQbkeCt1jSAM7lZqg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.24.0
    dev: false

  /@babel/helper-module-transforms@7.22.9(@babel/core@7.22.10):
    resolution: {integrity: sha512-t+WA2Xn5K+rTeGtC8jCsdAH52bjggG5TKRuRrAGNM/mjIbO4GxvlLMFOEz9wXY5I2XQ60PMFsAG2WIcG82dQMQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-module-imports': 7.22.5
      '@babel/helper-simple-access': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/helper-validator-identifier': 7.22.5
    dev: false

  /@babel/helper-module-transforms@7.23.3(@babel/core@7.24.3):
    resolution: {integrity: sha512-7bBs4ED9OmswdfDzpz4MpWgSrV7FXlc3zIagvLFjS5H+Mk7Snr21vQ6QwrsoCGMfNC4e4LQPdoULEt4ykz0SRQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.24.3
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-module-imports': 7.24.3
      '@babel/helper-simple-access': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/helper-validator-identifier': 7.22.20
    dev: false

  /@babel/helper-plugin-utils@7.22.5:
    resolution: {integrity: sha512-uLls06UVKgFG9QD4OeFYLEGteMIAa5kpTPcFL28yuCIIzsf6ZyKZMllKVOCZFhiZ5ptnwX4mtKdWCBE/uT4amg==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-simple-access@7.22.5:
    resolution: {integrity: sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: false

  /@babel/helper-split-export-declaration@7.22.6:
    resolution: {integrity: sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.10
    dev: false

  /@babel/helper-string-parser@7.22.5:
    resolution: {integrity: sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-string-parser@7.24.1:
    resolution: {integrity: sha512-2ofRCjnnA9y+wk8b9IAREroeUP02KHp431N2mhKniy2yKIDKpbrHv9eXwm8cBeWQYcJmzv5qKCu65P47eCF7CQ==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-validator-identifier@7.22.20:
    resolution: {integrity: sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-validator-identifier@7.22.5:
    resolution: {integrity: sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-validator-option@7.22.5:
    resolution: {integrity: sha512-R3oB6xlIVKUnxNUxbmgq7pKjxpru24zlimpE8WK47fACIlM0II/Hm1RS8IaOI7NgCr6LNS+jl5l75m20npAziw==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-validator-option@7.23.5:
    resolution: {integrity: sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helpers@7.22.10:
    resolution: {integrity: sha512-a41J4NW8HyZa1I1vAndrraTlPZ/eZoga2ZgS7fEr0tZJGVU4xqdE80CEm0CcNjha5EZ8fTBYLKHF0kqDUuAwQw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.22.5
      '@babel/traverse': 7.22.10(supports-color@5.5.0)
      '@babel/types': 7.22.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helpers@7.24.1:
    resolution: {integrity: sha512-BpU09QqEe6ZCHuIHFphEFgvNSrubve1FtyMton26ekZ85gRGi6LrTF7zArARp2YvyFxloeiRmtSCq5sjh1WqIg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.24.0
      '@babel/traverse': 7.24.1
      '@babel/types': 7.24.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/highlight@7.22.10:
    resolution: {integrity: sha512-78aUtVcT7MUscr0K5mIEnkwxPE0MaxkR5RxRwuHaQ+JuU5AmTPhY+do2mdzVTnIJJpyBglql2pehuBIWHug+WQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.22.5
      chalk: 2.4.2
      js-tokens: 4.0.0
    dev: false

  /@babel/highlight@7.24.2:
    resolution: {integrity: sha512-Yac1ao4flkTxTteCDZLEvdxg2fZfz1v8M4QpaGypq/WPDqg3ijHYbDfs+LG5hvzSoqaSZ9/Z9lKSP3CjZjv+pA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.22.20
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.0.0
    dev: false

  /@babel/parser@7.22.10:
    resolution: {integrity: sha512-lNbdGsQb9ekfsnjFGhEiF4hfFqGgfOP3H3d27re3n+CGhNuTSUEQdfWk556sTLNTloczcdM5TYF2LhzmDQKyvQ==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.22.10
    dev: false

  /@babel/parser@7.24.1:
    resolution: {integrity: sha512-Zo9c7N3xdOIQrNip7Lc9wvRPzlRtovHVE4lkz8WEDr7uYh/GMQhSiIgFxGIArRHYdJE5kxtZjAf8rT0xhdLCzg==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.24.0
    dev: false

  /@babel/plugin-proposal-export-namespace-from@7.18.9(@babel/core@7.22.10):
    resolution: {integrity: sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.22.10)
    dev: false

  /@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.22.10):
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: false

  /@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.22.10):
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-plugin-utils': 7.22.5
    dev: false

  /@babel/plugin-syntax-jsx@7.22.5(@babel/core@7.24.3):
    resolution: {integrity: sha512-gvyP4hZrgrs/wWMaocvxZ44Hw0b3W8Pe+cMxc8V1ULQ07oh8VNbIRaoD1LRZVTvD+0nieDKjfgKg89sD7rrKrg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.24.3
      '@babel/helper-plugin-utils': 7.22.5
    dev: false

  /@babel/plugin-transform-modules-commonjs@7.22.5(@babel/core@7.22.10):
    resolution: {integrity: sha512-B4pzOXj+ONRmuaQTg05b3y/4DuFz3WcCNAXPLb2Q0GT0TrGKGxNKV4jwsXts+StaM0LQczZbOpj8o1DLPDJIiA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.22.10
      '@babel/helper-module-transforms': 7.22.9(@babel/core@7.22.10)
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-simple-access': 7.22.5
    dev: false

  /@babel/runtime@7.22.10:
    resolution: {integrity: sha512-21t/fkKLMZI4pqP2wlmsQAWnYW1PDyKyyUV4vCi+B25ydmdaYTKXPwCj0BzSUnZf4seIiYvSA3jcZ3gdsMFkLQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.14.0
    dev: false

  /@babel/template@7.22.5:
    resolution: {integrity: sha512-X7yV7eiwAxdj9k94NEylvbVHLiVG1nvzCV2EAowhxLTwODV1jl9UzZ48leOC0sH7OnuHrIkllaBgneUykIcZaw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.22.10
      '@babel/parser': 7.22.10
      '@babel/types': 7.22.10
    dev: false

  /@babel/template@7.24.0:
    resolution: {integrity: sha512-Bkf2q8lMB0AFpX0NFEqSbx1OkTHf0f+0j82mkw+ZpzBnkk7e9Ql0891vlfgi+kHwOk8tQjiQHpqh4LaSa0fKEA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.24.2
      '@babel/parser': 7.24.1
      '@babel/types': 7.24.0
    dev: false

  /@babel/traverse@7.22.10(supports-color@5.5.0):
    resolution: {integrity: sha512-Q/urqV4pRByiNNpb/f5OSv28ZlGJiFiiTh+GAHktbIrkPhPbl90+uW6SmpoLyZqutrg9AEaEf3Q/ZBRHBXgxig==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.22.10
      '@babel/generator': 7.22.10
      '@babel/helper-environment-visitor': 7.22.5
      '@babel/helper-function-name': 7.22.5
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/parser': 7.22.10
      '@babel/types': 7.22.10
      debug: 4.3.4(supports-color@5.5.0)
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/traverse@7.24.1:
    resolution: {integrity: sha512-xuU6o9m68KeqZbQuDt2TcKSxUw/mrsvavlEqQ1leZ/B+C9tk6E4sRWy97WaXgvq5E+nU3cXMxv3WKOCanVMCmQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.24.2
      '@babel/generator': 7.24.1
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/parser': 7.24.1
      '@babel/types': 7.24.0
      debug: 4.3.4(supports-color@5.5.0)
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/types@7.22.10:
    resolution: {integrity: sha512-obaoigiLrlDZ7TUQln/8m4mSqIW2QFeOrCQc9r+xsaHGNoplVNYlRVpsfE8Vj35GEm2ZH4ZhrNYogs/3fj85kg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.22.5
      '@babel/helper-validator-identifier': 7.22.5
      to-fast-properties: 2.0.0
    dev: false

  /@babel/types@7.24.0:
    resolution: {integrity: sha512-+j7a5c253RfKh8iABBhywc8NSfP5LURe7Uh4qpsh6jc+aLJguvmIUBdjSdEMQv2bENrCR5MfRdjGo7vzS/ob7w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.24.1
      '@babel/helper-validator-identifier': 7.22.20
      to-fast-properties: 2.0.0
    dev: false

  /@blocknote/core@0.8.2(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2):
    resolution: {integrity: sha512-dJSUsv/6iZjMKAAL4Pib13yWhtFjHWkZr5EJ9w9KvyvNC+QEGQ7K9Teabw9Tu7M2+eif94qhpr76ryalfcdEmg==}
    dependencies:
      '@emotion/cache': 11.11.0
      '@emotion/serialize': 1.1.2
      '@emotion/utils': 1.2.1
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
      '@tiptap/extension-bold': 2.0.4(@tiptap/core@2.0.4)
      '@tiptap/extension-code': 2.0.4(@tiptap/core@2.0.4)
      '@tiptap/extension-collaboration': 2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4)(y-prosemirror@1.0.20)
      '@tiptap/extension-collaboration-cursor': 2.0.4(@tiptap/core@2.0.4)(y-prosemirror@1.0.20)
      '@tiptap/extension-dropcursor': 2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4)
      '@tiptap/extension-gapcursor': 2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4)
      '@tiptap/extension-hard-break': 2.0.4(@tiptap/core@2.0.4)
      '@tiptap/extension-history': 2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4)
      '@tiptap/extension-horizontal-rule': 2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4)
      '@tiptap/extension-italic': 2.0.4(@tiptap/core@2.0.4)
      '@tiptap/extension-link': 2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4)
      '@tiptap/extension-paragraph': 2.0.4(@tiptap/core@2.0.4)
      '@tiptap/extension-strike': 2.0.4(@tiptap/core@2.0.4)
      '@tiptap/extension-text': 2.0.4(@tiptap/core@2.0.4)
      '@tiptap/extension-underline': 2.0.4(@tiptap/core@2.0.4)
      '@tiptap/pm': 2.0.4(@tiptap/core@2.0.4)
      hast-util-from-dom: 4.2.0
      lodash: 4.17.21
      rehype-parse: 8.0.4
      rehype-remark: 9.1.2
      rehype-stringify: 9.0.3
      remark-gfm: 3.0.1
      remark-parse: 10.0.2
      remark-rehype: 10.1.0
      remark-stringify: 10.0.3
      unified: 10.1.2
      uuid: 8.3.2
      y-prosemirror: 1.0.20(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2)(y-protocols@1.0.5)(yjs@13.6.14)
      y-protocols: 1.0.5
      yjs: 13.6.7
    transitivePeerDependencies:
      - prosemirror-model
      - prosemirror-state
      - prosemirror-view
      - supports-color
    dev: false

  /@blocknote/react@0.8.2(@tiptap/core@2.2.4)(@tiptap/pm@2.2.4)(@types/react@18.2.14)(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-t/Ad/bS5MsieoKj0KEcDLTytj7p6bbJGTNgg6cjpUS/BC5LfbqU/UQZVtugeXHJ8+esnXw6BOz0CyC4HIjcJ1g==}
    peerDependencies:
      react: ^18
      react-dom: ^18
    dependencies:
      '@blocknote/core': 0.8.2(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2)
      '@emotion/react': 11.11.1(@types/react@18.2.14)(react@18.2.0)
      '@mantine/core': 5.10.5(@emotion/react@11.11.1)(@mantine/hooks@5.10.5)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@mantine/hooks': 5.10.5(react@18.2.0)
      '@mantine/utils': 6.0.18(react@18.2.0)
      '@tippyjs/react': 4.2.6(react-dom@18.2.0)(react@18.2.0)
      '@tiptap/react': 2.0.4(@tiptap/core@2.2.4)(@tiptap/pm@2.2.4)(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-icons: 4.10.1(react@18.2.0)
    transitivePeerDependencies:
      - '@tiptap/core'
      - '@tiptap/pm'
      - '@types/react'
      - prosemirror-model
      - prosemirror-state
      - prosemirror-view
      - supports-color
    dev: false

  /@codemirror/autocomplete@6.9.0(@codemirror/language@6.9.0)(@codemirror/state@6.2.1)(@codemirror/view@6.15.3)(@lezer/common@1.0.4):
    resolution: {integrity: sha512-Fbwm0V/Wn3BkEJZRhr0hi5BhCo5a7eBL6LYaliPjOSwCyfOpnjXY59HruSxOUNV+1OYer0Tgx1zRNQttjXyDog==}
    peerDependencies:
      '@codemirror/language': ^6.0.0
      '@codemirror/state': ^6.0.0
      '@codemirror/view': ^6.0.0
      '@lezer/common': ^1.0.0
    dependencies:
      '@codemirror/language': 6.9.0
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.15.3
      '@lezer/common': 1.0.4
    dev: false

  /@codemirror/autocomplete@6.9.0(@codemirror/language@6.9.0)(@codemirror/state@6.2.1)(@codemirror/view@6.15.3)(@lezer/common@1.2.1):
    resolution: {integrity: sha512-Fbwm0V/Wn3BkEJZRhr0hi5BhCo5a7eBL6LYaliPjOSwCyfOpnjXY59HruSxOUNV+1OYer0Tgx1zRNQttjXyDog==}
    peerDependencies:
      '@codemirror/language': ^6.0.0
      '@codemirror/state': ^6.0.0
      '@codemirror/view': ^6.0.0
      '@lezer/common': ^1.0.0
    dependencies:
      '@codemirror/language': 6.9.0
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.15.3
      '@lezer/common': 1.2.1
    dev: false

  /@codemirror/autocomplete@6.9.0(@codemirror/language@6.9.0)(@codemirror/state@6.2.1)(@codemirror/view@6.18.1)(@lezer/common@1.0.4):
    resolution: {integrity: sha512-Fbwm0V/Wn3BkEJZRhr0hi5BhCo5a7eBL6LYaliPjOSwCyfOpnjXY59HruSxOUNV+1OYer0Tgx1zRNQttjXyDog==}
    peerDependencies:
      '@codemirror/language': ^6.0.0
      '@codemirror/state': ^6.0.0
      '@codemirror/view': ^6.0.0
      '@lezer/common': ^1.0.0
    dependencies:
      '@codemirror/language': 6.9.0
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.18.1
      '@lezer/common': 1.0.4
    dev: false

  /@codemirror/commands@6.2.5:
    resolution: {integrity: sha512-dSi7ow2P2YgPBZflR9AJoaTHvqmeGIgkhignYMd5zK5y6DANTvxKxp6eMEpIDUJkRAaOY/TFZ4jP1ADIO/GLVA==}
    dependencies:
      '@codemirror/language': 6.9.0
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.15.3
      '@lezer/common': 1.0.4
    dev: false

  /@codemirror/lang-css@6.2.1(@codemirror/view@6.15.3):
    resolution: {integrity: sha512-/UNWDNV5Viwi/1lpr/dIXJNWiwDxpw13I4pTUAsNxZdg6E0mI2kTQb0P2iHczg1Tu+H4EBgJR+hYhKiHKko7qg==}
    dependencies:
      '@codemirror/autocomplete': 6.9.0(@codemirror/language@6.9.0)(@codemirror/state@6.2.1)(@codemirror/view@6.15.3)(@lezer/common@1.0.4)
      '@codemirror/language': 6.9.0
      '@codemirror/state': 6.2.1
      '@lezer/common': 1.0.4
      '@lezer/css': 1.1.3
    transitivePeerDependencies:
      - '@codemirror/view'
    dev: false

  /@codemirror/lang-css@6.2.1(@codemirror/view@6.18.1):
    resolution: {integrity: sha512-/UNWDNV5Viwi/1lpr/dIXJNWiwDxpw13I4pTUAsNxZdg6E0mI2kTQb0P2iHczg1Tu+H4EBgJR+hYhKiHKko7qg==}
    dependencies:
      '@codemirror/autocomplete': 6.9.0(@codemirror/language@6.9.0)(@codemirror/state@6.2.1)(@codemirror/view@6.18.1)(@lezer/common@1.0.4)
      '@codemirror/language': 6.9.0
      '@codemirror/state': 6.2.1
      '@lezer/common': 1.0.4
      '@lezer/css': 1.1.3
    transitivePeerDependencies:
      - '@codemirror/view'
    dev: false

  /@codemirror/lang-html@6.4.6:
    resolution: {integrity: sha512-E4C8CVupBksXvgLSme/zv31x91g06eZHSph7NczVxZW+/K+3XgJGWNT//2WLzaKSBoxpAjaOi5ZnPU1SHhjh3A==}
    dependencies:
      '@codemirror/autocomplete': 6.9.0(@codemirror/language@6.9.0)(@codemirror/state@6.2.1)(@codemirror/view@6.18.1)(@lezer/common@1.0.4)
      '@codemirror/lang-css': 6.2.1(@codemirror/view@6.18.1)
      '@codemirror/lang-javascript': 6.2.1
      '@codemirror/language': 6.9.0
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.18.1
      '@lezer/common': 1.0.4
      '@lezer/css': 1.1.3
      '@lezer/html': 1.3.6
    dev: false

  /@codemirror/lang-javascript@6.2.1:
    resolution: {integrity: sha512-jlFOXTejVyiQCW3EQwvKH0m99bUYIw40oPmFjSX2VS78yzfe0HELZ+NEo9Yfo1MkGRpGlj3Gnu4rdxV1EnAs5A==}
    dependencies:
      '@codemirror/autocomplete': 6.9.0(@codemirror/language@6.9.0)(@codemirror/state@6.2.1)(@codemirror/view@6.18.1)(@lezer/common@1.0.4)
      '@codemirror/language': 6.9.0
      '@codemirror/lint': 6.4.1
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.18.1
      '@lezer/common': 1.0.4
      '@lezer/javascript': 1.4.7
    dev: false

  /@codemirror/lang-markdown@6.2.0:
    resolution: {integrity: sha512-deKegEQVzfBAcLPqsJEa+IxotqPVwWZi90UOEvQbfa01NTAw8jNinrykuYPTULGUj+gha0ZG2HBsn4s5d64Qrg==}
    dependencies:
      '@codemirror/autocomplete': 6.9.0(@codemirror/language@6.9.0)(@codemirror/state@6.2.1)(@codemirror/view@6.15.3)(@lezer/common@1.0.4)
      '@codemirror/lang-html': 6.4.6
      '@codemirror/language': 6.9.0
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.15.3
      '@lezer/common': 1.0.4
      '@lezer/markdown': 1.1.0
    dev: false

  /@codemirror/language@6.9.0:
    resolution: {integrity: sha512-nFu311/0ne/qGuGCL3oKuktBgzVOaxCHZPZv1tLSZkNjPYxxvkjSbzno3MlErG2tgw1Yw1yF8BxMCegeMXqpiw==}
    dependencies:
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.15.3
      '@lezer/common': 1.0.4
      '@lezer/highlight': 1.1.6
      '@lezer/lr': 1.3.10
      style-mod: 4.1.0
    dev: false

  /@codemirror/lint@6.4.1:
    resolution: {integrity: sha512-2Hx945qKX7FBan5/gUdTM8fsMYrNG9clIgEcPXestbLVFAUyQYFAuju/5BMNf/PwgpVaX5pvRm4+ovjbp9D9gQ==}
    dependencies:
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.18.1
      crelt: 1.0.6
    dev: false

  /@codemirror/state@6.2.1:
    resolution: {integrity: sha512-RupHSZ8+OjNT38zU9fKH2sv+Dnlr8Eb8sl4NOnnqz95mCFTZUaiRP8Xv5MeeaG0px2b8Bnfe7YGwCV3nsBhbuw==}
    dev: false

  /@codemirror/view@6.15.3:
    resolution: {integrity: sha512-chNgR8H7Ipx7AZUt0+Kknk7BCow/ron3mHd1VZdM7hQXiI79+UlWqcxpCiexTxZQ+iSkqndk3HHAclJOcjSuog==}
    dependencies:
      '@codemirror/state': 6.2.1
      style-mod: 4.1.0
      w3c-keyname: 2.2.8
    dev: false

  /@codemirror/view@6.18.1:
    resolution: {integrity: sha512-xcsXcMkIMd7l3WZEWoc4ljteAiqzxb5gVerRxk5132p5cLix6rTydWTQjsj2oxORepfsrwy1fC4r20iMa9plrg==}
    dependencies:
      '@codemirror/state': 6.2.1
      style-mod: 4.1.0
      w3c-keyname: 2.2.8
    dev: false

  /@codesandbox/nodebox@0.1.8:
    resolution: {integrity: sha512-2VRS6JDSk+M+pg56GA6CryyUSGPjBEe8Pnae0QL3jJF1mJZJVMDKr93gJRtBbLkfZN6LD/DwMtf+2L0bpWrjqg==}
    dependencies:
      outvariant: 1.4.0
      strict-event-emitter: 0.4.6
    dev: false

  /@codesandbox/sandpack-client@2.7.0:
    resolution: {integrity: sha512-GC6Qc4qDnlcAXZGx+85ummW5Y8I9ARBoKoXqiXT8Em8Qutv2JgfuquILL1Qc8iv2US8Jpm/PLBo3hlE+2L8LzA==}
    dependencies:
      '@codesandbox/nodebox': 0.1.8
      buffer: 6.0.3
      dequal: 2.0.3
      outvariant: 1.4.0
      static-browser-server: 1.0.3
    dev: false

  /@codesandbox/sandpack-react@2.7.0(@lezer/common@1.2.1)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-dzQuRxcbY0fK5HbChc/mGDmBELm2MVfQHe14534HUnb5nXCghluSqfFPeu+a+Q3fYWa0HifyZMxLXfo+4VxGxA==}
    peerDependencies:
      react: ^16.8.0 || ^17 || ^18
      react-dom: ^16.8.0 || ^17 || ^18
    dependencies:
      '@codemirror/autocomplete': 6.9.0(@codemirror/language@6.9.0)(@codemirror/state@6.2.1)(@codemirror/view@6.15.3)(@lezer/common@1.2.1)
      '@codemirror/commands': 6.2.5
      '@codemirror/lang-css': 6.2.1(@codemirror/view@6.15.3)
      '@codemirror/lang-html': 6.4.6
      '@codemirror/lang-javascript': 6.2.1
      '@codemirror/language': 6.9.0
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.15.3
      '@codesandbox/sandpack-client': 2.7.0
      '@lezer/highlight': 1.1.6
      '@react-hook/intersection-observer': 3.1.1(react@18.2.0)
      '@stitches/core': 1.2.8
      anser: 2.1.1
      clean-set: 1.1.2
      codesandbox-import-util-types: 2.2.3
      dequal: 2.0.3
      escape-carriage: 1.3.1
      lz-string: 1.5.0
      react: 18.2.0
      react-devtools-inline: 4.4.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 17.0.2
    transitivePeerDependencies:
      - '@lezer/common'
    dev: false

  /@emotion/babel-plugin@11.11.0:
    resolution: {integrity: sha512-m4HEDZleaaCH+XgDDsPF15Ht6wTLsgDTeR3WYj9Q/k76JtWhrJjcP4+/XlG8LGT/Rol9qUfOIztXeA84ATpqPQ==}
    dependencies:
      '@babel/helper-module-imports': 7.22.5
      '@babel/runtime': 7.22.10
      '@emotion/hash': 0.9.1
      '@emotion/memoize': 0.8.1
      '@emotion/serialize': 1.1.2
      babel-plugin-macros: 3.1.0
      convert-source-map: 1.9.0
      escape-string-regexp: 4.0.0
      find-root: 1.1.0
      source-map: 0.5.7
      stylis: 4.2.0
    dev: false

  /@emotion/cache@11.11.0:
    resolution: {integrity: sha512-P34z9ssTCBi3e9EI1ZsWpNHcfY1r09ZO0rZbRO2ob3ZQMnFI35jB536qoXbkdesr5EUhYi22anuEJuyxifaqAQ==}
    dependencies:
      '@emotion/memoize': 0.8.1
      '@emotion/sheet': 1.2.2
      '@emotion/utils': 1.2.1
      '@emotion/weak-memoize': 0.3.1
      stylis: 4.2.0
    dev: false

  /@emotion/css@11.11.2:
    resolution: {integrity: sha512-VJxe1ucoMYMS7DkiMdC2T7PWNbrEI0a39YRiyDvK2qq4lXwjRbVP/z4lpG+odCsRzadlR+1ywwrTzhdm5HNdew==}
    dependencies:
      '@emotion/babel-plugin': 11.11.0
      '@emotion/cache': 11.11.0
      '@emotion/serialize': 1.1.2
      '@emotion/sheet': 1.2.2
      '@emotion/utils': 1.2.1
    dev: false

  /@emotion/hash@0.9.1:
    resolution: {integrity: sha512-gJB6HLm5rYwSLI6PQa+X1t5CFGrv1J1TWG+sOyMCeKz2ojaj6Fnl/rZEspogG+cvqbt4AE/2eIyD2QfLKTBNlQ==}
    dev: false

  /@emotion/is-prop-valid@1.2.1:
    resolution: {integrity: sha512-61Mf7Ufx4aDxx1xlDeOm8aFFigGHE4z+0sKCa+IHCeZKiyP9RLD0Mmx7m8b9/Cf37f7NAvQOOJAbQQGVr5uERw==}
    dependencies:
      '@emotion/memoize': 0.8.1
    dev: false

  /@emotion/memoize@0.8.1:
    resolution: {integrity: sha512-W2P2c/VRW1/1tLox0mVUalvnWXxavmv/Oum2aPsRcoDJuob75FC3Y8FbpfLwUegRcxINtGUMPq0tFCvYNTBXNA==}
    dev: false

  /@emotion/react@11.11.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-5mlW1DquU5HaxjLkfkGN1GA/fvVGdyHURRiX/0FHl2cfIfRxSOfmxEH5YS43edp0OldZrZ+dkBKbngxcNCdZvA==}
    peerDependencies:
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@emotion/babel-plugin': 11.11.0
      '@emotion/cache': 11.11.0
      '@emotion/serialize': 1.1.2
      '@emotion/use-insertion-effect-with-fallbacks': 1.0.1(react@18.2.0)
      '@emotion/utils': 1.2.1
      '@emotion/weak-memoize': 0.3.1
      '@types/react': 18.2.14
      hoist-non-react-statics: 3.3.2
      react: 18.2.0
    dev: false

  /@emotion/serialize@1.1.2:
    resolution: {integrity: sha512-zR6a/fkFP4EAcCMQtLOhIgpprZOwNmCldtpaISpvz348+DP4Mz8ZoKaGGCQpbzepNIUWbq4w6hNZkwDyKoS+HA==}
    dependencies:
      '@emotion/hash': 0.9.1
      '@emotion/memoize': 0.8.1
      '@emotion/unitless': 0.8.1
      '@emotion/utils': 1.2.1
      csstype: 3.1.2
    dev: false

  /@emotion/sheet@1.2.2:
    resolution: {integrity: sha512-0QBtGvaqtWi+nx6doRwDdBIzhNdZrXUppvTM4dtZZWEGTXL/XE/yJxLMGlDT1Gt+UHH5IX1n+jkXyytE/av7OA==}
    dev: false

  /@emotion/styled@11.11.0(@emotion/react@11.11.1)(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-hM5Nnvu9P3midq5aaXj4I+lnSfNi7Pmd4EWk1fOZ3pxookaQTNew6bp4JaCBYM4HVFZF9g7UjJmsUmC2JlxOng==}
    peerDependencies:
      '@emotion/react': ^11.0.0-rc.0
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@emotion/babel-plugin': 11.11.0
      '@emotion/is-prop-valid': 1.2.1
      '@emotion/react': 11.11.1(@types/react@18.2.14)(react@18.2.0)
      '@emotion/serialize': 1.1.2
      '@emotion/use-insertion-effect-with-fallbacks': 1.0.1(react@18.2.0)
      '@emotion/utils': 1.2.1
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@emotion/stylis@0.8.5:
    resolution: {integrity: sha512-h6KtPihKFn3T9fuIrwvXXUOwlx3rfUvfZIcP5a6rh8Y7zjE3O06hT5Ss4S/YI1AYhuZ1kjaE/5EaOOI2NqSylQ==}
    dev: false

  /@emotion/unitless@0.7.5:
    resolution: {integrity: sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg==}
    dev: false

  /@emotion/unitless@0.8.1:
    resolution: {integrity: sha512-KOEGMu6dmJZtpadb476IsZBclKvILjopjUii3V+7MnXIQCYh8W3NgNcgwo21n9LXZX6EDIKvqfjYxXebDwxKmQ==}
    dev: false

  /@emotion/use-insertion-effect-with-fallbacks@1.0.1(react@18.2.0):
    resolution: {integrity: sha512-jT/qyKZ9rzLErtrjGgdkMBn2OP8wl0G3sQlBb3YPryvKHsjvINUhVaPFfP+fpBcOkmrVOVEEHQFJ7nbj2TH2gw==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: 18.2.0
    dev: false

  /@emotion/utils@1.2.1:
    resolution: {integrity: sha512-Y2tGf3I+XVnajdItskUCn6LX+VUDmP6lTL4fcqsXAv43dnlbZiuW4MWQW38rW/BVWSE7Q/7+XQocmpnRYILUmg==}
    dev: false

  /@emotion/weak-memoize@0.3.1:
    resolution: {integrity: sha512-EsBwpc7hBUJWAsNPBmJy4hxWx12v6bshQsldrVmjxJoc3isbxhOrF2IcCpaXxfvq03NwkI7sbsOLXbYuqF/8Ww==}
    dev: false

  /@fastify/busboy@1.2.1:
    resolution: {integrity: sha512-7PQA7EH43S0CxcOa9OeAnaeA0oQ+e/DHNPZwSQM9CQHW76jle5+OvLdibRp/Aafs9KXbLhxyjOTkRjWUbQEd3Q==}
    engines: {node: '>=14'}
    dependencies:
      text-decoding: 1.0.0
    dev: false

  /@firebase/analytics-compat@0.2.6(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13):
    resolution: {integrity: sha512-4MqpVLFkGK7NJf/5wPEEP7ePBJatwYpyjgJ+wQHQGHfzaCDgntOnl9rL2vbVGGKCnRqWtZDIWhctB86UWXaX2Q==}
    peerDependencies:
      '@firebase/app-compat': 0.x
    dependencies:
      '@firebase/analytics': 0.10.0(@firebase/app@0.9.13)
      '@firebase/analytics-types': 0.8.0
      '@firebase/app-compat': 0.2.13
      '@firebase/component': 0.6.4
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    transitivePeerDependencies:
      - '@firebase/app'
    dev: false

  /@firebase/analytics-types@0.8.0:
    resolution: {integrity: sha512-iRP+QKI2+oz3UAh4nPEq14CsEjrjD6a5+fuypjScisAh9kXKFvdJOZJDwk7kikLvWVLGEs9+kIUS4LPQV7VZVw==}
    dev: false

  /@firebase/analytics@0.10.0(@firebase/app@0.9.13):
    resolution: {integrity: sha512-Locv8gAqx0e+GX/0SI3dzmBY5e9kjVDtD+3zCFLJ0tH2hJwuCAiL+5WkHuxKj92rqQj/rvkBUCfA1ewlX2hehg==}
    peerDependencies:
      '@firebase/app': 0.x
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/component': 0.6.4
      '@firebase/installations': 0.6.4(@firebase/app@0.9.13)
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    dev: false

  /@firebase/app-check-compat@0.3.7(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13):
    resolution: {integrity: sha512-cW682AxsyP1G+Z0/P7pO/WT2CzYlNxoNe5QejVarW2o5ZxeWSSPAiVEwpEpQR/bUlUmdeWThYTMvBWaopdBsqw==}
    peerDependencies:
      '@firebase/app-compat': 0.x
    dependencies:
      '@firebase/app-check': 0.8.0(@firebase/app@0.9.13)
      '@firebase/app-check-types': 0.5.0
      '@firebase/app-compat': 0.2.13
      '@firebase/component': 0.6.4
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    transitivePeerDependencies:
      - '@firebase/app'
    dev: false

  /@firebase/app-check-interop-types@0.3.0:
    resolution: {integrity: sha512-xAxHPZPIgFXnI+vb4sbBjZcde7ZluzPPaSK7Lx3/nmuVk4TjZvnL8ONnkd4ERQKL8WePQySU+pRcWkh8rDf5Sg==}
    dev: false

  /@firebase/app-check-types@0.5.0:
    resolution: {integrity: sha512-uwSUj32Mlubybw7tedRzR24RP8M8JUVR3NPiMk3/Z4bCmgEKTlQBwMXrehDAZ2wF+TsBq0SN1c6ema71U/JPyQ==}
    dev: false

  /@firebase/app-check@0.8.0(@firebase/app@0.9.13):
    resolution: {integrity: sha512-dRDnhkcaC2FspMiRK/Vbp+PfsOAEP6ZElGm9iGFJ9fDqHoPs0HOPn7dwpJ51lCFi1+2/7n5pRPGhqF/F03I97g==}
    peerDependencies:
      '@firebase/app': 0.x
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/component': 0.6.4
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    dev: false

  /@firebase/app-compat@0.2.13:
    resolution: {integrity: sha512-j6ANZaWjeVy5zg6X7uiqh6lM6o3n3LD1+/SJFNs9V781xyryyZWXe+tmnWNWPkP086QfJoNkWN9pMQRqSG4vMg==}
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/component': 0.6.4
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    dev: false

  /@firebase/app-types@0.9.0:
    resolution: {integrity: sha512-AeweANOIo0Mb8GiYm3xhTEBVCmPwTYAu9Hcd2qSkLuga/6+j9b1Jskl5bpiSQWy9eJ/j5pavxj6eYogmnuzm+Q==}
    dev: false

  /@firebase/app@0.9.13:
    resolution: {integrity: sha512-GfiI1JxJ7ecluEmDjPzseRXk/PX31hS7+tjgBopL7XjB2hLUdR+0FTMXy2Q3/hXezypDvU6or7gVFizDESrkXw==}
    dependencies:
      '@firebase/component': 0.6.4
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      idb: 7.1.1
      tslib: 2.6.1
    dev: false

  /@firebase/auth-compat@0.4.2(@firebase/app-compat@0.2.13)(@firebase/app-types@0.9.0)(@firebase/app@0.9.13)(encoding@0.1.13):
    resolution: {integrity: sha512-Q30e77DWXFmXEt5dg5JbqEDpjw9y3/PcP9LslDPR7fARmAOTIY9MM6HXzm9KC+dlrKH/+p6l8g9ifJiam9mc4A==}
    peerDependencies:
      '@firebase/app-compat': 0.x
    dependencies:
      '@firebase/app-compat': 0.2.13
      '@firebase/auth': 0.23.2(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/auth-types': 0.12.0(@firebase/app-types@0.9.0)(@firebase/util@1.9.3)
      '@firebase/component': 0.6.4
      '@firebase/util': 1.9.3
      node-fetch: 2.6.7(encoding@0.1.13)
      tslib: 2.6.1
    transitivePeerDependencies:
      - '@firebase/app'
      - '@firebase/app-types'
      - encoding
    dev: false

  /@firebase/auth-interop-types@0.2.1:
    resolution: {integrity: sha512-VOaGzKp65MY6P5FI84TfYKBXEPi6LmOCSMMzys6o2BN2LOsqy7pCuZCup7NYnfbk5OkkQKzvIfHOzTm0UDpkyg==}
    dev: false

  /@firebase/auth-types@0.12.0(@firebase/app-types@0.9.0)(@firebase/util@1.9.3):
    resolution: {integrity: sha512-pPwaZt+SPOshK8xNoiQlK5XIrS97kFYc3Rc7xmy373QsOJ9MmqXxLaYssP5Kcds4wd2qK//amx/c+A8O2fVeZA==}
    peerDependencies:
      '@firebase/app-types': 0.x
      '@firebase/util': 1.x
    dependencies:
      '@firebase/app-types': 0.9.0
      '@firebase/util': 1.9.3
    dev: false

  /@firebase/auth@0.23.2(@firebase/app@0.9.13)(encoding@0.1.13):
    resolution: {integrity: sha512-dM9iJ0R6tI1JczuGSxXmQbXAgtYie0K4WvKcuyuSTCu9V8eEDiz4tfa1sO3txsfvwg7nOY3AjoCyMYEdqZ8hdg==}
    peerDependencies:
      '@firebase/app': 0.x
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/component': 0.6.4
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      node-fetch: 2.6.7(encoding@0.1.13)
      tslib: 2.6.1
    transitivePeerDependencies:
      - encoding
    dev: false

  /@firebase/component@0.6.4:
    resolution: {integrity: sha512-rLMyrXuO9jcAUCaQXCMjCMUsWrba5fzHlNK24xz5j2W6A/SRmK8mZJ/hn7V0fViLbxC0lPMtrK1eYzk6Fg03jA==}
    dependencies:
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    dev: false

  /@firebase/database-compat@0.3.4:
    resolution: {integrity: sha512-kuAW+l+sLMUKBThnvxvUZ+Q1ZrF/vFJ58iUY9kAcbX48U03nVzIF6Tmkf0p3WVQwMqiXguSgtOPIB6ZCeF+5Gg==}
    dependencies:
      '@firebase/component': 0.6.4
      '@firebase/database': 0.14.4
      '@firebase/database-types': 0.10.4
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    dev: false

  /@firebase/database-types@0.10.4:
    resolution: {integrity: sha512-dPySn0vJ/89ZeBac70T+2tWWPiJXWbmRygYv0smT5TfE3hDrQ09eKMF3Y+vMlTdrMWq7mUdYW5REWPSGH4kAZQ==}
    dependencies:
      '@firebase/app-types': 0.9.0
      '@firebase/util': 1.9.3
    dev: false

  /@firebase/database@0.14.4:
    resolution: {integrity: sha512-+Ea/IKGwh42jwdjCyzTmeZeLM3oy1h0mFPsTy6OqCWzcu/KFqRAr5Tt1HRCOBlNOdbh84JPZC47WLU18n2VbxQ==}
    dependencies:
      '@firebase/auth-interop-types': 0.2.1
      '@firebase/component': 0.6.4
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      faye-websocket: 0.11.4
      tslib: 2.6.1
    dev: false

  /@firebase/firestore-compat@0.3.12(@firebase/app-compat@0.2.13)(@firebase/app-types@0.9.0)(@firebase/app@0.9.13)(encoding@0.1.13):
    resolution: {integrity: sha512-mazuNGAx5Kt9Nph0pm6ULJFp/+j7GSsx+Ncw1GrnKl+ft1CQ4q2LcUssXnjqkX2Ry0fNGqUzC1mfIUrk9bYtjQ==}
    peerDependencies:
      '@firebase/app-compat': 0.x
    dependencies:
      '@firebase/app-compat': 0.2.13
      '@firebase/component': 0.6.4
      '@firebase/firestore': 3.13.0(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/firestore-types': 2.5.1(@firebase/app-types@0.9.0)(@firebase/util@1.9.3)
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    transitivePeerDependencies:
      - '@firebase/app'
      - '@firebase/app-types'
      - encoding
    dev: false

  /@firebase/firestore-types@2.5.1(@firebase/app-types@0.9.0)(@firebase/util@1.9.3):
    resolution: {integrity: sha512-xG0CA6EMfYo8YeUxC8FeDzf6W3FX1cLlcAGBYV6Cku12sZRI81oWcu61RSKM66K6kUENP+78Qm8mvroBcm1whw==}
    peerDependencies:
      '@firebase/app-types': 0.x
      '@firebase/util': 1.x
    dependencies:
      '@firebase/app-types': 0.9.0
      '@firebase/util': 1.9.3
    dev: false

  /@firebase/firestore@3.13.0(@firebase/app@0.9.13)(encoding@0.1.13):
    resolution: {integrity: sha512-NwcnU+madJXQ4fbLkGx1bWvL612IJN/qO6bZ6dlPmyf7QRyu5azUosijdAN675r+bOOJxMtP1Bv981bHBXAbUg==}
    engines: {node: '>=10.10.0'}
    peerDependencies:
      '@firebase/app': 0.x
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/component': 0.6.4
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      '@firebase/webchannel-wrapper': 0.10.1
      '@grpc/grpc-js': 1.7.3
      '@grpc/proto-loader': 0.6.13
      node-fetch: 2.6.7(encoding@0.1.13)
      tslib: 2.6.1
    transitivePeerDependencies:
      - encoding
    dev: false

  /@firebase/functions-compat@0.3.5(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13)(encoding@0.1.13):
    resolution: {integrity: sha512-uD4jwgwVqdWf6uc3NRKF8cSZ0JwGqSlyhPgackyUPe+GAtnERpS4+Vr66g0b3Gge0ezG4iyHo/EXW/Hjx7QhHw==}
    peerDependencies:
      '@firebase/app-compat': 0.x
    dependencies:
      '@firebase/app-compat': 0.2.13
      '@firebase/component': 0.6.4
      '@firebase/functions': 0.10.0(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/functions-types': 0.6.0
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    transitivePeerDependencies:
      - '@firebase/app'
      - encoding
    dev: false

  /@firebase/functions-types@0.6.0:
    resolution: {integrity: sha512-hfEw5VJtgWXIRf92ImLkgENqpL6IWpYaXVYiRkFY1jJ9+6tIhWM7IzzwbevwIIud/jaxKVdRzD7QBWfPmkwCYw==}
    dev: false

  /@firebase/functions@0.10.0(@firebase/app@0.9.13)(encoding@0.1.13):
    resolution: {integrity: sha512-2U+fMNxTYhtwSpkkR6WbBcuNMOVaI7MaH3cZ6UAeNfj7AgEwHwMIFLPpC13YNZhno219F0lfxzTAA0N62ndWzA==}
    peerDependencies:
      '@firebase/app': 0.x
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/app-check-interop-types': 0.3.0
      '@firebase/auth-interop-types': 0.2.1
      '@firebase/component': 0.6.4
      '@firebase/messaging-interop-types': 0.2.0
      '@firebase/util': 1.9.3
      node-fetch: 2.6.7(encoding@0.1.13)
      tslib: 2.6.1
    transitivePeerDependencies:
      - encoding
    dev: false

  /@firebase/installations-compat@0.2.4(@firebase/app-compat@0.2.13)(@firebase/app-types@0.9.0)(@firebase/app@0.9.13):
    resolution: {integrity: sha512-LI9dYjp0aT9Njkn9U4JRrDqQ6KXeAmFbRC0E7jI7+hxl5YmRWysq5qgQl22hcWpTk+cm3es66d/apoDU/A9n6Q==}
    peerDependencies:
      '@firebase/app-compat': 0.x
    dependencies:
      '@firebase/app-compat': 0.2.13
      '@firebase/component': 0.6.4
      '@firebase/installations': 0.6.4(@firebase/app@0.9.13)
      '@firebase/installations-types': 0.5.0(@firebase/app-types@0.9.0)
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    transitivePeerDependencies:
      - '@firebase/app'
      - '@firebase/app-types'
    dev: false

  /@firebase/installations-types@0.5.0(@firebase/app-types@0.9.0):
    resolution: {integrity: sha512-9DP+RGfzoI2jH7gY4SlzqvZ+hr7gYzPODrbzVD82Y12kScZ6ZpRg/i3j6rleto8vTFC8n6Len4560FnV1w2IRg==}
    peerDependencies:
      '@firebase/app-types': 0.x
    dependencies:
      '@firebase/app-types': 0.9.0
    dev: false

  /@firebase/installations@0.6.4(@firebase/app@0.9.13):
    resolution: {integrity: sha512-u5y88rtsp7NYkCHC3ElbFBrPtieUybZluXyzl7+4BsIz4sqb4vSAuwHEUgCgCeaQhvsnxDEU6icly8U9zsJigA==}
    peerDependencies:
      '@firebase/app': 0.x
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/component': 0.6.4
      '@firebase/util': 1.9.3
      idb: 7.0.1
      tslib: 2.6.1
    dev: false

  /@firebase/logger@0.4.0:
    resolution: {integrity: sha512-eRKSeykumZ5+cJPdxxJRgAC3G5NknY2GwEbKfymdnXtnT0Ucm4pspfR6GT4MUQEDuJwRVbVcSx85kgJulMoFFA==}
    dependencies:
      tslib: 2.6.1
    dev: false

  /@firebase/messaging-compat@0.2.4(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13):
    resolution: {integrity: sha512-lyFjeUhIsPRYDPNIkYX1LcZMpoVbBWXX4rPl7c/rqc7G+EUea7IEtSt4MxTvh6fDfPuzLn7+FZADfscC+tNMfg==}
    peerDependencies:
      '@firebase/app-compat': 0.x
    dependencies:
      '@firebase/app-compat': 0.2.13
      '@firebase/component': 0.6.4
      '@firebase/messaging': 0.12.4(@firebase/app@0.9.13)
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    transitivePeerDependencies:
      - '@firebase/app'
    dev: false

  /@firebase/messaging-interop-types@0.2.0:
    resolution: {integrity: sha512-ujA8dcRuVeBixGR9CtegfpU4YmZf3Lt7QYkcj693FFannwNuZgfAYaTmbJ40dtjB81SAu6tbFPL9YLNT15KmOQ==}
    dev: false

  /@firebase/messaging@0.12.4(@firebase/app@0.9.13):
    resolution: {integrity: sha512-6JLZct6zUaex4g7HI3QbzeUrg9xcnmDAPTWpkoMpd/GoSVWH98zDoWXMGrcvHeCAIsLpFMe4MPoZkJbrPhaASw==}
    peerDependencies:
      '@firebase/app': 0.x
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/component': 0.6.4
      '@firebase/installations': 0.6.4(@firebase/app@0.9.13)
      '@firebase/messaging-interop-types': 0.2.0
      '@firebase/util': 1.9.3
      idb: 7.0.1
      tslib: 2.6.1
    dev: false

  /@firebase/performance-compat@0.2.4(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13):
    resolution: {integrity: sha512-nnHUb8uP9G8islzcld/k6Bg5RhX62VpbAb/Anj7IXs/hp32Eb2LqFPZK4sy3pKkBUO5wcrlRWQa6wKOxqlUqsg==}
    peerDependencies:
      '@firebase/app-compat': 0.x
    dependencies:
      '@firebase/app-compat': 0.2.13
      '@firebase/component': 0.6.4
      '@firebase/logger': 0.4.0
      '@firebase/performance': 0.6.4(@firebase/app@0.9.13)
      '@firebase/performance-types': 0.2.0
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    transitivePeerDependencies:
      - '@firebase/app'
    dev: false

  /@firebase/performance-types@0.2.0:
    resolution: {integrity: sha512-kYrbr8e/CYr1KLrLYZZt2noNnf+pRwDq2KK9Au9jHrBMnb0/C9X9yWSXmZkFt4UIdsQknBq8uBB7fsybZdOBTA==}
    dev: false

  /@firebase/performance@0.6.4(@firebase/app@0.9.13):
    resolution: {integrity: sha512-HfTn/bd8mfy/61vEqaBelNiNnvAbUtME2S25A67Nb34zVuCSCRIX4SseXY6zBnOFj3oLisaEqhVcJmVPAej67g==}
    peerDependencies:
      '@firebase/app': 0.x
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/component': 0.6.4
      '@firebase/installations': 0.6.4(@firebase/app@0.9.13)
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    dev: false

  /@firebase/remote-config-compat@0.2.4(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13):
    resolution: {integrity: sha512-FKiki53jZirrDFkBHglB3C07j5wBpitAaj8kLME6g8Mx+aq7u9P7qfmuSRytiOItADhWUj7O1JIv7n9q87SuwA==}
    peerDependencies:
      '@firebase/app-compat': 0.x
    dependencies:
      '@firebase/app-compat': 0.2.13
      '@firebase/component': 0.6.4
      '@firebase/logger': 0.4.0
      '@firebase/remote-config': 0.4.4(@firebase/app@0.9.13)
      '@firebase/remote-config-types': 0.3.0
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    transitivePeerDependencies:
      - '@firebase/app'
    dev: false

  /@firebase/remote-config-types@0.3.0:
    resolution: {integrity: sha512-RtEH4vdcbXZuZWRZbIRmQVBNsE7VDQpet2qFvq6vwKLBIQRQR5Kh58M4ok3A3US8Sr3rubYnaGqZSurCwI8uMA==}
    dev: false

  /@firebase/remote-config@0.4.4(@firebase/app@0.9.13):
    resolution: {integrity: sha512-x1ioTHGX8ZwDSTOVp8PBLv2/wfwKzb4pxi0gFezS5GCJwbLlloUH4YYZHHS83IPxnua8b6l0IXUaWd0RgbWwzQ==}
    peerDependencies:
      '@firebase/app': 0.x
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/component': 0.6.4
      '@firebase/installations': 0.6.4(@firebase/app@0.9.13)
      '@firebase/logger': 0.4.0
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    dev: false

  /@firebase/storage-compat@0.3.2(@firebase/app-compat@0.2.13)(@firebase/app-types@0.9.0)(@firebase/app@0.9.13)(encoding@0.1.13):
    resolution: {integrity: sha512-wvsXlLa9DVOMQJckbDNhXKKxRNNewyUhhbXev3t8kSgoCotd1v3MmqhKKz93ePhDnhHnDs7bYHy+Qa8dRY6BXw==}
    peerDependencies:
      '@firebase/app-compat': 0.x
    dependencies:
      '@firebase/app-compat': 0.2.13
      '@firebase/component': 0.6.4
      '@firebase/storage': 0.11.2(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/storage-types': 0.8.0(@firebase/app-types@0.9.0)(@firebase/util@1.9.3)
      '@firebase/util': 1.9.3
      tslib: 2.6.1
    transitivePeerDependencies:
      - '@firebase/app'
      - '@firebase/app-types'
      - encoding
    dev: false

  /@firebase/storage-types@0.8.0(@firebase/app-types@0.9.0)(@firebase/util@1.9.3):
    resolution: {integrity: sha512-isRHcGrTs9kITJC0AVehHfpraWFui39MPaU7Eo8QfWlqW7YPymBmRgjDrlOgFdURh6Cdeg07zmkLP5tzTKRSpg==}
    peerDependencies:
      '@firebase/app-types': 0.x
      '@firebase/util': 1.x
    dependencies:
      '@firebase/app-types': 0.9.0
      '@firebase/util': 1.9.3
    dev: false

  /@firebase/storage@0.11.2(@firebase/app@0.9.13)(encoding@0.1.13):
    resolution: {integrity: sha512-CtvoFaBI4hGXlXbaCHf8humajkbXhs39Nbh6MbNxtwJiCqxPy9iH3D3CCfXAvP0QvAAwmJUTK3+z9a++Kc4nkA==}
    peerDependencies:
      '@firebase/app': 0.x
    dependencies:
      '@firebase/app': 0.9.13
      '@firebase/component': 0.6.4
      '@firebase/util': 1.9.3
      node-fetch: 2.6.7(encoding@0.1.13)
      tslib: 2.6.1
    transitivePeerDependencies:
      - encoding
    dev: false

  /@firebase/util@1.9.3:
    resolution: {integrity: sha512-DY02CRhOZwpzO36fHpuVysz6JZrscPiBXD0fXp6qSrL9oNOx5KWICKdR95C0lSITzxp0TZosVyHqzatE8JbcjA==}
    dependencies:
      tslib: 2.6.1
    dev: false

  /@firebase/webchannel-wrapper@0.10.1:
    resolution: {integrity: sha512-Dq5rYfEpdeel0bLVN+nfD1VWmzCkK+pJbSjIawGE+RY4+NIJqhbUDDQjvV0NUK84fMfwxvtFoCtEe70HfZjFcw==}
    dev: false

  /@floating-ui/core@1.4.1:
    resolution: {integrity: sha512-jk3WqquEJRlcyu7997NtR5PibI+y5bi+LS3hPmguVClypenMsCY3CBa3LAQnozRCtCrYWSEtAdiskpamuJRFOQ==}
    dependencies:
      '@floating-ui/utils': 0.1.1
    dev: false

  /@floating-ui/dom@1.5.1:
    resolution: {integrity: sha512-KwvVcPSXg6mQygvA1TjbN/gh///36kKtllIF8SUm0qpFj8+rvYrpvlYdL1JoA71SHpDqgSSdGOSoQ0Mp3uY5aw==}
    dependencies:
      '@floating-ui/core': 1.4.1
      '@floating-ui/utils': 0.1.1
    dev: false

  /@floating-ui/react-dom@1.3.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-htwHm67Ji5E/pROEAr7f8IKFShuiCKHwUC/UY4vC3I5jiSvGFAYnSYiZO5MlGmads+QqvUkR9ANHEguGrDv72g==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@floating-ui/dom': 1.5.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@floating-ui/react-dom@2.0.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-rZtAmSht4Lry6gdhAJDrCp/6rKN7++JnL1/Anbr/DdeyYXQPxvg/ivrbYvJulbRf4vL8b212suwMM2lxbv+RQA==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@floating-ui/dom': 1.5.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@floating-ui/react@0.19.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-JyNk4A0Ezirq8FlXECvRtQOX/iBe5Ize0W/pLkrZjfHW9GUV7Xnq6zm6fyZuQzaHHqEnVizmvlA96e1/CkZv+w==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@floating-ui/react-dom': 1.3.0(react-dom@18.2.0)(react@18.2.0)
      aria-hidden: 1.2.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      tabbable: 6.2.0
    dev: false

  /@floating-ui/react@0.24.8(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-AuYeDoaR8jtUlUXtZ1IJ/6jtBkGnSpJXbGNzokBL87VDJ8opMq1Bgrc0szhK482ReQY6KZsMoZCVSb4xwalkBA==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@floating-ui/react-dom': 2.0.1(react-dom@18.2.0)(react@18.2.0)
      aria-hidden: 1.2.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      tabbable: 6.2.0
    dev: false

  /@floating-ui/utils@0.1.1:
    resolution: {integrity: sha512-m0G6wlnhm/AX0H12IOWtK8gASEMffnX08RtKkCgTdHb9JpHKGloI7icFfLg9ZmQeavcvR0PKmzxClyuFPSjKWw==}
    dev: false

  /@google-cloud/firestore@6.8.0(encoding@0.1.13):
    resolution: {integrity: sha512-JRpk06SmZXLGz0pNx1x7yU3YhkUXheKgH5hbDZ4kMsdhtfV5qPLJLRI4wv69K0cZorIk+zTMOwptue7hizo0eA==}
    engines: {node: '>=12.0.0'}
    requiresBuild: true
    dependencies:
      fast-deep-equal: 3.1.3
      functional-red-black-tree: 1.0.1
      google-gax: 3.6.1(encoding@0.1.13)
      protobufjs: 7.2.6
    transitivePeerDependencies:
      - encoding
      - supports-color
    dev: false
    optional: true

  /@google-cloud/paginator@3.0.7:
    resolution: {integrity: sha512-jJNutk0arIQhmpUUQJPJErsojqo834KcyB6X7a1mxuic8i1tKXxde8E69IZxNZawRIlZdIK2QY4WALvlK5MzYQ==}
    engines: {node: '>=10'}
    requiresBuild: true
    dependencies:
      arrify: 2.0.1
      extend: 3.0.2
    dev: false
    optional: true

  /@google-cloud/projectify@3.0.0:
    resolution: {integrity: sha512-HRkZsNmjScY6Li8/kb70wjGlDDyLkVk3KvoEo9uIoxSjYLJasGiCch9+PqRVDOCGUFvEIqyogl+BeqILL4OJHA==}
    engines: {node: '>=12.0.0'}
    requiresBuild: true
    dev: false
    optional: true

  /@google-cloud/promisify@3.0.1:
    resolution: {integrity: sha512-z1CjRjtQyBOYL+5Qr9DdYIfrdLBe746jRTYfaYU6MeXkqp7UfYs/jX16lFFVzZ7PGEJvqZNqYUEtb1mvDww4pA==}
    engines: {node: '>=12'}
    requiresBuild: true
    dev: false
    optional: true

  /@google-cloud/storage@6.12.0(encoding@0.1.13):
    resolution: {integrity: sha512-78nNAY7iiZ4O/BouWMWTD/oSF2YtYgYB3GZirn0To6eBOugjXVoK+GXgUXOl+HlqbAOyHxAVXOlsj3snfbQ1dw==}
    engines: {node: '>=12'}
    requiresBuild: true
    dependencies:
      '@google-cloud/paginator': 3.0.7
      '@google-cloud/projectify': 3.0.0
      '@google-cloud/promisify': 3.0.1
      abort-controller: 3.0.0
      async-retry: 1.3.3
      compressible: 2.0.18
      duplexify: 4.1.2
      ent: 2.2.0
      extend: 3.0.2
      fast-xml-parser: 4.2.7
      gaxios: 5.1.3(encoding@0.1.13)
      google-auth-library: 8.9.0(encoding@0.1.13)
      mime: 3.0.0
      mime-types: 2.1.35
      p-limit: 3.1.0
      retry-request: 5.0.2
      teeny-request: 8.0.3(encoding@0.1.13)
      uuid: 8.3.2
    transitivePeerDependencies:
      - encoding
      - supports-color
    dev: false
    optional: true

  /@grpc/grpc-js@1.7.3:
    resolution: {integrity: sha512-H9l79u4kJ2PVSxUNA08HMYAnUBLj9v6KjYQ7SQ71hOZcEXhShE/y5iQCesP8+6/Ik/7i2O0a10bPquIcYfufog==}
    engines: {node: ^8.13.0 || >=10.10.0}
    dependencies:
      '@grpc/proto-loader': 0.7.8
      '@types/node': 20.3.3
    dev: false

  /@grpc/grpc-js@1.8.21:
    resolution: {integrity: sha512-KeyQeZpxeEBSqFVTi3q2K7PiPXmgBfECc4updA1ejCLjYmoAlvvM3ZMp5ztTDUCUQmoY3CpDxvchjO1+rFkoHg==}
    engines: {node: ^8.13.0 || >=10.10.0}
    requiresBuild: true
    dependencies:
      '@grpc/proto-loader': 0.7.8
      '@types/node': 20.3.3
    dev: false
    optional: true

  /@grpc/proto-loader@0.6.13:
    resolution: {integrity: sha512-FjxPYDRTn6Ec3V0arm1FtSpmP6V50wuph2yILpyvTKzjc76oDdoihXqM1DzOW5ubvCC8GivfCnNtfaRE8myJ7g==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      '@types/long': 4.0.2
      lodash.camelcase: 4.3.0
      long: 4.0.0
      protobufjs: 6.11.3
      yargs: 16.2.0
    dev: false

  /@grpc/proto-loader@0.7.8:
    resolution: {integrity: sha512-GU12e2c8dmdXb7XUlOgYWZ2o2i+z9/VeACkxTA/zzAe2IjclC5PnVL0lpgjhrqfpDYHzM8B1TF6pqWegMYAzlA==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      '@types/long': 4.0.2
      lodash.camelcase: 4.3.0
      long: 4.0.0
      protobufjs: 7.2.4
      yargs: 17.7.2
    dev: false

  /@hookform/resolvers@3.1.1(react-hook-form@7.45.2):
    resolution: {integrity: sha512-tS16bAUkqjITNSvbJuO1x7MXbn7Oe8ZziDTJdA9mMvsoYthnOOiznOTGBYwbdlYBgU+tgpI/BtTU3paRbCuSlg==}
    peerDependencies:
      react-hook-form: ^7.0.0
    dependencies:
      react-hook-form: 7.45.2(react@18.2.0)
    dev: false

  /@icons/material@0.2.4(react@18.2.0):
    resolution: {integrity: sha512-QPcGmICAPbGLGb6F/yNf/KzKqvFx8z5qx3D1yFqVAjoFmXK35EgyW+cJ57Te3CNsmzblwtzakLGFqHPqrfb4Tw==}
    peerDependencies:
      react: '*'
    dependencies:
      react: 18.2.0
    dev: false

  /@javascript-obfuscator/escodegen@2.3.0:
    resolution: {integrity: sha512-QVXwMIKqYMl3KwtTirYIA6gOCiJ0ZDtptXqAv/8KWLG9uQU2fZqTVy7a/A5RvcoZhbDoFfveTxuGxJ5ibzQtkw==}
    engines: {node: '>=6.0'}
    dependencies:
      '@javascript-obfuscator/estraverse': 5.4.0
      esprima: 4.0.1
      esutils: 2.0.3
      optionator: 0.8.3
    optionalDependencies:
      source-map: 0.6.1
    dev: false

  /@javascript-obfuscator/estraverse@5.4.0:
    resolution: {integrity: sha512-CZFX7UZVN9VopGbjTx4UXaXsi9ewoM1buL0kY7j1ftYdSs7p2spv9opxFjHlQ/QGTgh4UqufYqJJ0WKLml7b6w==}
    engines: {node: '>=4.0'}
    dev: false

  /@jridgewell/gen-mapping@0.3.3:
    resolution: {integrity: sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.19

  /@jridgewell/gen-mapping@0.3.5:
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.25
    dev: false

  /@jridgewell/resolve-uri@3.1.1:
    resolution: {integrity: sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/resolve-uri@3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}
    dev: false

  /@jridgewell/set-array@1.1.2:
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/set-array@1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}
    dev: false

  /@jridgewell/source-map@0.3.6:
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
    dev: false

  /@jridgewell/sourcemap-codec@1.4.15:
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  /@jridgewell/trace-mapping@0.3.19:
    resolution: {integrity: sha512-kf37QtfW+Hwx/buWGMPcR60iF9ziHa6r/CZJIHbmcm4+0qrXiVdxegAH0F6yddEVQ7zdkjcGCgCzUu+BcbhQxw==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.1
      '@jridgewell/sourcemap-codec': 1.4.15

  /@jridgewell/trace-mapping@0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.4.15
    dev: false

  /@jsdoc/salty@0.2.5:
    resolution: {integrity: sha512-TfRP53RqunNe2HBobVBJ0VLhK1HbfvBYeTC1ahnN64PWvyYyGebmMiPkuwvD9fpw2ZbkoPb8Q7mwy0aR8Z9rvw==}
    engines: {node: '>=v12.0.0'}
    requiresBuild: true
    dependencies:
      lodash: 4.17.21
    dev: false
    optional: true

  /@lexical/clipboard@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-6xggT8b0hd4OQy25mBH+yiJsr3Bm8APHjDOd3yINCGeiiHXIC+2qKQn3MG70euxQQuyzq++tYHcSsFq42g8Jyw==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/html': 0.11.3(lexical@0.11.3)
      '@lexical/list': 0.11.3(lexical@0.11.3)
      '@lexical/selection': 0.11.3(lexical@0.11.3)
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/code@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-BIMPd2op65iP4N9SkKIUVodZoWeSsnk6skNJ8UHBO/Rg0ZxyAqxLpnBhEgHq2QOoTBbEW6OEFtkc7/+f9LINZg==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
      prismjs: 1.27.0
    dev: false

  /@lexical/dragon@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-S18uwqOOpV2yIAFVWqSvBdhZ5BGadPQO4ejZF15wP8LUuqkxCs+0I/MjLovQ7tx0Cx34KdDaOXtM6XeG74ixYw==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      lexical: 0.11.3
    dev: false

  /@lexical/hashtag@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-7auoaWp2QhsX9/Bq0SxLXatUaSwqoT9HlWNTH2vKsw8tdeUBYacTHLuBNncTGrznXLG0/B5+FWoLuM6Pzqq4Ig==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/history@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-QLJQRH2rbadRwXd4c/U4TqjLWDQna6Q43nCocIZF+SdVG9TlASp7m6dS7hiHfPtV1pkxJUxPhZY6EsB/Ok5WGA==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/html@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-+8AYnxxml9PneZLkGfdTenqDjE2yD1ZfCmQLrD/L1TEn22OjZh4uvKVHb13wEhgUZTuLKF0PNdnuecko9ON/aQ==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/selection': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/link@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-stAjIrDrF18dPKK25ExPwMCcMe0KKD0FWVzo3F7ejh9DvrQcLFeBPcs8ze71chS3D5fQDB/CzdwvMjEViKmq2A==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/list@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-Cs9071wDfqi4j1VgodceiR1jTHj13eCoEJDhr3e/FW0x5we7vfbTMtWlOWbveIoryAh+rQNgiD5e8SrAm6Zs3g==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/mark@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-0wAtufmaA0rMVFXoiJ0sY/tiJsQbHuDpgywb1Qa8qnZZcg7ZTrQMz9Go0fEWYcbSp8OH2o0cjbDTz3ACS1qCUA==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/markdown@0.11.3(@lexical/clipboard@0.11.3)(@lexical/selection@0.11.3)(lexical@0.11.3):
    resolution: {integrity: sha512-sF8ow32BDme3UvxaKpf+j+vMc4T/XvDEzteZHmvvP7NX/iUtK3yUkTyT7rKuGwiKLYfMBwQaKMGjU3/nlIOzUg==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/code': 0.11.3(lexical@0.11.3)
      '@lexical/link': 0.11.3(lexical@0.11.3)
      '@lexical/list': 0.11.3(lexical@0.11.3)
      '@lexical/rich-text': 0.11.3(@lexical/clipboard@0.11.3)(@lexical/selection@0.11.3)(@lexical/utils@0.11.3)(lexical@0.11.3)
      '@lexical/text': 0.11.3(lexical@0.11.3)
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    transitivePeerDependencies:
      - '@lexical/clipboard'
      - '@lexical/selection'
    dev: false

  /@lexical/offset@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-3H9X8iqDSk0LrMOHZuqYuqX4EYGb78TIhtjrFbLJi/OgKmHaSeLx59xcMZdgd5kBdRitzQYMmvbRDvbLfMgWrA==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      lexical: 0.11.3
    dev: false

  /@lexical/overflow@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-ShjCG8lICShOBKwrpP+9PjRFKEBCSUUMjbIGZfLnoL//3hyRtGv5aRgRyfJlRgDhCve0ROt5znLJV88EXzGRyA==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      lexical: 0.11.3
    dev: false

  /@lexical/plain-text@0.11.3(@lexical/clipboard@0.11.3)(@lexical/selection@0.11.3)(@lexical/utils@0.11.3)(lexical@0.11.3):
    resolution: {integrity: sha512-cQ5Us+GNzShyjjgRqWTnYv0rC+jHJ96LvBA1aSieM77H8/Im5BeoLl6TgBK2NqPkp8fGpj8JnDEdT8h9Qh1jtA==}
    peerDependencies:
      '@lexical/clipboard': 0.11.3
      '@lexical/selection': 0.11.3
      '@lexical/utils': 0.11.3
      lexical: 0.11.3
    dependencies:
      '@lexical/clipboard': 0.11.3(lexical@0.11.3)
      '@lexical/selection': 0.11.3(lexical@0.11.3)
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/react@0.11.3(lexical@0.11.3)(react-dom@18.2.0)(react@18.2.0)(yjs@13.6.14):
    resolution: {integrity: sha512-Rn0Agnrz3uLIWbNyS9PRlkxOxcIDl2kxaVfgBacqQtYKR0ZVB2Hnoi89Cq6VmWPovauPyryx4Q3FC8Y11X7Otg==}
    peerDependencies:
      lexical: 0.11.3
      react: '>=17.x'
      react-dom: '>=17.x'
    dependencies:
      '@lexical/clipboard': 0.11.3(lexical@0.11.3)
      '@lexical/code': 0.11.3(lexical@0.11.3)
      '@lexical/dragon': 0.11.3(lexical@0.11.3)
      '@lexical/hashtag': 0.11.3(lexical@0.11.3)
      '@lexical/history': 0.11.3(lexical@0.11.3)
      '@lexical/link': 0.11.3(lexical@0.11.3)
      '@lexical/list': 0.11.3(lexical@0.11.3)
      '@lexical/mark': 0.11.3(lexical@0.11.3)
      '@lexical/markdown': 0.11.3(@lexical/clipboard@0.11.3)(@lexical/selection@0.11.3)(lexical@0.11.3)
      '@lexical/overflow': 0.11.3(lexical@0.11.3)
      '@lexical/plain-text': 0.11.3(@lexical/clipboard@0.11.3)(@lexical/selection@0.11.3)(@lexical/utils@0.11.3)(lexical@0.11.3)
      '@lexical/rich-text': 0.11.3(@lexical/clipboard@0.11.3)(@lexical/selection@0.11.3)(@lexical/utils@0.11.3)(lexical@0.11.3)
      '@lexical/selection': 0.11.3(lexical@0.11.3)
      '@lexical/table': 0.11.3(lexical@0.11.3)
      '@lexical/text': 0.11.3(lexical@0.11.3)
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      '@lexical/yjs': 0.11.3(lexical@0.11.3)(yjs@13.6.14)
      lexical: 0.11.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-error-boundary: 3.1.4(react@18.2.0)
    transitivePeerDependencies:
      - yjs
    dev: false

  /@lexical/rich-text@0.11.3(@lexical/clipboard@0.11.3)(@lexical/selection@0.11.3)(@lexical/utils@0.11.3)(lexical@0.11.3):
    resolution: {integrity: sha512-fBFs6wMS7GFLbk+mzIWtwpP+EmnTZZ5bHpveuQ5wXONBuUuLcsYF5KO7UhLxXNLmiViV6lxatZPavEzgZdW7oQ==}
    peerDependencies:
      '@lexical/clipboard': 0.11.3
      '@lexical/selection': 0.11.3
      '@lexical/utils': 0.11.3
      lexical: 0.11.3
    dependencies:
      '@lexical/clipboard': 0.11.3(lexical@0.11.3)
      '@lexical/selection': 0.11.3(lexical@0.11.3)
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/selection@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-15lQpcKT/vd7XZ5pnF1nb+kpKb72e9Yi1dVqieSxTeXkzt1cAZFKP3NB4RlhOKCv1N+glSBnjSxRwgsFfbD+NQ==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      lexical: 0.11.3
    dev: false

  /@lexical/table@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-EyRnN39CSPsMceADBR7Kf+sBHNpNQlPEkn/52epeDSnakR6s80woyrA3kIzKo6mLB4afvoqdYc7RfR96M9JLIA==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/text@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-gCEN8lJyR6b+yaOwKWGj79pbOfCQPWU/PHWyoNFUkEJXn3KydCzr2EYb6ta2cvQWRQU4G2BClKCR56jL4NS+qg==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      lexical: 0.11.3
    dev: false

  /@lexical/utils@0.11.3(lexical@0.11.3):
    resolution: {integrity: sha512-vC4saCrlcmyIJnvrYKw1uYxZojlD1DCIBsFlgmO8kXyRYXjj+o/8PBdn2dsgSQ3rADrC2mUloOm/maekDcYe9Q==}
    peerDependencies:
      lexical: 0.11.3
    dependencies:
      '@lexical/list': 0.11.3(lexical@0.11.3)
      '@lexical/selection': 0.11.3(lexical@0.11.3)
      '@lexical/table': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
    dev: false

  /@lexical/yjs@0.11.3(lexical@0.11.3)(yjs@13.6.14):
    resolution: {integrity: sha512-TLDQG2FSEw/aOfppEBb0wRlIuzJ57W//8ImfzyZvckSC12tvU0YKQQX8nQz/rybXdyfRy5eN+8gX5K2EyZx+pQ==}
    peerDependencies:
      lexical: 0.11.3
      yjs: '>=13.5.22'
    dependencies:
      '@lexical/offset': 0.11.3(lexical@0.11.3)
      lexical: 0.11.3
      yjs: 13.6.14
    dev: false

  /@lezer/common@1.0.4:
    resolution: {integrity: sha512-lZHlk8p67x4aIDtJl6UQrXSOP6oi7dQR3W/geFVrENdA1JDaAJWldnVqVjPMJupbTKbzDfFcePfKttqVidS/dg==}
    dev: false

  /@lezer/common@1.2.1:
    resolution: {integrity: sha512-yemX0ZD2xS/73llMZIK6KplkjIjf2EvAHcinDi/TfJ9hS25G0388+ClHt6/3but0oOxinTcQHJLDXh6w1crzFQ==}
    dev: false

  /@lezer/css@1.1.3:
    resolution: {integrity: sha512-SjSM4pkQnQdJDVc80LYzEaMiNy9txsFbI7HsMgeVF28NdLaAdHNtQ+kB/QqDUzRBV/75NTXjJ/R5IdC8QQGxMg==}
    dependencies:
      '@lezer/highlight': 1.1.6
      '@lezer/lr': 1.3.10
    dev: false

  /@lezer/highlight@1.1.6:
    resolution: {integrity: sha512-cmSJYa2us+r3SePpRCjN5ymCqCPv+zyXmDl0ciWtVaNiORT/MxM7ZgOMQZADD0o51qOaOg24qc/zBViOIwAjJg==}
    dependencies:
      '@lezer/common': 1.0.4
    dev: false

  /@lezer/html@1.3.6:
    resolution: {integrity: sha512-Kk9HJARZTc0bAnMQUqbtuhFVsB4AnteR2BFUWfZV7L/x1H0aAKz6YabrfJ2gk/BEgjh9L3hg5O4y2IDZRBdzuQ==}
    dependencies:
      '@lezer/common': 1.0.4
      '@lezer/highlight': 1.1.6
      '@lezer/lr': 1.3.10
    dev: false

  /@lezer/javascript@1.4.7:
    resolution: {integrity: sha512-OVWlK0YEi7HM+9JRWtRkir8qvcg0/kVYg2TAMHlVtl6DU1C9yK1waEOLBMztZsV/axRJxsqfJKhzYz+bxZme5g==}
    dependencies:
      '@lezer/highlight': 1.1.6
      '@lezer/lr': 1.3.10
    dev: false

  /@lezer/lr@1.3.10:
    resolution: {integrity: sha512-BZfVvf7Re5BIwJHlZXbJn9L8lus5EonxQghyn+ih8Wl36XMFBPTXC0KM0IdUtj9w/diPHsKlXVgL+AlX2jYJ0Q==}
    dependencies:
      '@lezer/common': 1.0.4
    dev: false

  /@lezer/markdown@1.1.0:
    resolution: {integrity: sha512-JYOI6Lkqbl83semCANkO3CKbKc0pONwinyagBufWBm+k4yhIcqfCF8B8fpEpvJLmIy7CAfwiq7dQ/PzUZA340g==}
    dependencies:
      '@lezer/common': 1.0.4
      '@lezer/highlight': 1.1.6
    dev: false

  /@linaria/core@4.2.10:
    resolution: {integrity: sha512-S1W01W7L4SQnGpWzp8awyCpPIYUOEJ+OLjjXqKpIXOU+ozPwBt86Mjjdas9aZccVhNBWDja74cMCUAVp8yUpDQ==}
    engines: {node: ^12.16.0 || >=13.7.0}
    dependencies:
      '@linaria/logger': 4.5.0
      '@linaria/tags': 4.5.4
      '@linaria/utils': 4.5.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@linaria/logger@4.5.0:
    resolution: {integrity: sha512-XdQLk242Cpcsc9a3Cz1ktOE5ysTo2TpxdeFQEPwMm8Z/+F/S6ZxBDdHYJL09srXWz3hkJr3oS2FPuMZNH1HIxw==}
    engines: {node: ^12.16.0 || >=13.7.0}
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
      picocolors: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@linaria/tags@4.5.4:
    resolution: {integrity: sha512-HPxLB6HlJWLi6o8+8lTLegOmDnbMbuzEE+zzunaPZEGSoIIYx8HAv5VbY/sG/zNyxDElk6laiAwEVWN8h5/zxg==}
    engines: {node: ^12.16.0 || >=13.7.0}
    dependencies:
      '@babel/generator': 7.22.10
      '@linaria/logger': 4.5.0
      '@linaria/utils': 4.5.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@linaria/utils@4.5.3:
    resolution: {integrity: sha512-tSpxA3Zn0DKJ2n/YBnYAgiDY+MNvkmzAHrD8R9PKrpGaZ+wz1jQEmE1vGn1cqh8dJyWK0NzPAA8sf1cqa+RmAg==}
    engines: {node: ^12.16.0 || >=13.7.0}
    dependencies:
      '@babel/core': 7.22.10
      '@babel/generator': 7.22.10
      '@babel/plugin-proposal-export-namespace-from': 7.18.9(@babel/core@7.22.10)
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.22.10)
      '@babel/plugin-transform-modules-commonjs': 7.22.5(@babel/core@7.22.10)
      '@babel/template': 7.22.5
      '@babel/traverse': 7.22.10(supports-color@5.5.0)
      '@babel/types': 7.22.10
      '@linaria/logger': 4.5.0
      babel-merge: 3.0.0(@babel/core@7.22.10)
      find-up: 5.0.0
      minimatch: 9.0.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@lingui/core@4.4.0:
    resolution: {integrity: sha512-0ngEP+g4bt6f3cNqEzkU5796VkEEamxNXF/JD/QV9Ftxp8QBw91WqAoHjNYs3aYZOctCsRBR7FlvRQ6o2fDWDg==}
    engines: {node: '>=16.0.0'}
    dependencies:
      '@babel/runtime': 7.22.10
      '@lingui/message-utils': 4.4.0
      unraw: 2.0.1
    dev: false

  /@lingui/detect-locale@4.4.0:
    resolution: {integrity: sha512-Mh4oLJ4KHSwcyWw7+bhQ8ErUCZjXDrZyILyHCcgq2S+HE2NG8M8OD/VBFajLW3dEUJPC9sRE8L2XTxYRNKlK6g==}
    engines: {node: '>=16.0.0'}
    dev: false

  /@lingui/message-utils@4.4.0:
    resolution: {integrity: sha512-SScnNuemsyHx2vyLvLsHgmAaCBHwnaAxUg3LkKoulqXe2Po8CmLBh1/28oNQ20ZhjwadUmy0unGalp9qqEBOkw==}
    engines: {node: '>=16.0.0'}
    dependencies:
      '@messageformat/parser': 5.1.0
    dev: false

  /@mantine/core@5.10.5(@emotion/react@11.11.1)(@mantine/hooks@5.10.5)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-F4tqHSEVM9D6/iSqHfPda+Xl5XgSEPHAAkT01Zwzj4Jnbd10qGrlqr/SFUop2CIcuKYnmra9XltUahUPXBC2BQ==}
    peerDependencies:
      '@mantine/hooks': 5.10.5
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@floating-ui/react': 0.19.2(react-dom@18.2.0)(react@18.2.0)
      '@mantine/hooks': 5.10.5(react@18.2.0)
      '@mantine/styles': 5.10.5(@emotion/react@11.11.1)(react-dom@18.2.0)(react@18.2.0)
      '@mantine/utils': 5.10.5(react@18.2.0)
      '@radix-ui/react-scroll-area': 1.0.2(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-textarea-autosize: 8.3.4(@types/react@18.2.14)(react@18.2.0)
    transitivePeerDependencies:
      - '@emotion/react'
      - '@types/react'
    dev: false

  /@mantine/hooks@5.10.5(react@18.2.0):
    resolution: {integrity: sha512-hFQp71QZDfivPzfIUOQZfMKLiOL/Cn2EnzacRlbUr55myteTfzYN8YMt+nzniE/6c4IRopFHEAdbKEtfyQc6kg==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: 18.2.0
    dev: false

  /@mantine/styles@5.10.5(@emotion/react@11.11.1)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-0NXk8c/XGzuTUkZc6KceF2NaTCMEu5mHR4ru0x+ttb9DGnLpHuGWduTHjSfr4hl6eAJgedD0zauO+VAhDzO9zA==}
    peerDependencies:
      '@emotion/react': '>=11.9.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@emotion/react': 11.11.1(@types/react@18.2.14)(react@18.2.0)
      clsx: 1.1.1
      csstype: 3.0.9
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@mantine/utils@5.10.5(react@18.2.0):
    resolution: {integrity: sha512-FGMq4dGs5HhDAtI0z46uzxzKKPmZ3h5uKUyKg1ZHoFR1mBtcUMbB6FylFmHqKFRWlJ5IXqX9dwmiVrLYUOfTmA==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: 18.2.0
    dev: false

  /@mantine/utils@6.0.18(react@18.2.0):
    resolution: {integrity: sha512-xvTnAUUHsdpsBm7OrcBueGEPdBwDm7wzUBsHweqSZMjT/HQOf4w4iirefNrFhMD2wNVetNL42kEngEBe6t63/w==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: 18.2.0
    dev: false

  /@mdxeditor/editor@0.22.5(@lezer/common@1.2.1)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)(yjs@13.6.14):
    resolution: {integrity: sha512-/7kKmrmag2NCYAmPm0Bu+zNZVl7Kr28KDIGD/5WNfCvHijc4ULtDrXv7f3OSkPiEefDAA+c/R/B6EhN/xwbSqA==}
    engines: {node: '>=16'}
    peerDependencies:
      react: ^18.2.0
      react-dom: ^18.2.0
    dependencies:
      '@codemirror/lang-markdown': 6.2.0
      '@codemirror/state': 6.2.1
      '@codemirror/view': 6.15.3
      '@codesandbox/sandpack-react': 2.7.0(@lezer/common@1.2.1)(react-dom@18.2.0)(react@18.2.0)
      '@lexical/clipboard': 0.11.3(lexical@0.11.3)
      '@lexical/code': 0.11.3(lexical@0.11.3)
      '@lexical/link': 0.11.3(lexical@0.11.3)
      '@lexical/list': 0.11.3(lexical@0.11.3)
      '@lexical/markdown': 0.11.3(@lexical/clipboard@0.11.3)(@lexical/selection@0.11.3)(lexical@0.11.3)
      '@lexical/plain-text': 0.11.3(@lexical/clipboard@0.11.3)(@lexical/selection@0.11.3)(@lexical/utils@0.11.3)(lexical@0.11.3)
      '@lexical/react': 0.11.3(lexical@0.11.3)(react-dom@18.2.0)(react@18.2.0)(yjs@13.6.14)
      '@lexical/rich-text': 0.11.3(@lexical/clipboard@0.11.3)(@lexical/selection@0.11.3)(@lexical/utils@0.11.3)(lexical@0.11.3)
      '@lexical/selection': 0.11.3(lexical@0.11.3)
      '@lexical/utils': 0.11.3(lexical@0.11.3)
      '@radix-ui/colors': 0.1.9
      '@radix-ui/react-dialog': 1.0.5(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-icons': 1.3.0(react@18.2.0)
      '@radix-ui/react-popover': 1.0.6(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-select': 1.2.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-toggle-group': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-toolbar': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-tooltip': 1.0.7(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.3.2
      downshift: 7.6.2(react@18.2.0)
      hast-util-raw: 8.0.0
      hast-util-sanitize: 4.1.0
      hast-util-to-html: 8.0.4
      js-yaml: 4.1.0
      lexical: 0.11.3
      mdast-util-directive: 2.2.4
      mdast-util-from-markdown: 1.3.1
      mdast-util-frontmatter: 1.0.1
      mdast-util-gfm-table: 1.0.7
      mdast-util-mdx: 2.0.1
      mdast-util-mdx-jsx: 2.1.4
      mdast-util-to-hast: 12.3.0
      mdast-util-to-markdown: 1.5.0
      micromark-extension-directive: 2.2.0
      micromark-extension-frontmatter: 1.1.0
      micromark-extension-gfm-table: 1.0.7
      micromark-extension-mdxjs: 1.0.1
      react: 18.2.0
      react-diff-view: 3.1.0(react@18.2.0)
      react-dom: 18.2.0(react@18.2.0)
      react-hook-form: 7.45.2(react@18.2.0)
      unidiff: 1.0.4
    transitivePeerDependencies:
      - '@lezer/common'
      - '@types/react'
      - '@types/react-dom'
      - supports-color
      - yjs
    dev: false

  /@messageformat/parser@5.1.0:
    resolution: {integrity: sha512-jKlkls3Gewgw6qMjKZ9SFfHUpdzEVdovKFtW1qRhJ3WI4FW5R/NnGDqr8SDGz+krWDO3ki94boMmQvGke1HwUQ==}
    dependencies:
      moo: 0.5.2
    dev: false

  /@mui/base@5.0.0-beta.11(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-FdKZGPd8qmC3ZNke7CNhzcEgToc02M6WYZc9hcBsNQ17bgAd3s9F//1bDDYgMVBYxDM71V0sv/hBHlOY4I1ZVA==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
      react-dom: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@emotion/is-prop-valid': 1.2.1
      '@mui/types': 7.2.4(@types/react@18.2.14)
      '@mui/utils': 5.14.5(react@18.2.0)
      '@popperjs/core': 2.11.8
      '@types/react': 18.2.14
      clsx: 2.0.0
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 18.2.0
    dev: false

  /@mui/core-downloads-tracker@5.14.5:
    resolution: {integrity: sha512-+wpGH1USwPcKMFPMvXqYPC6fEvhxM3FzxC8lyDiNK/imLyyJ6y2DPb1Oue7OGIKJWBmYBqrWWtfovrxd1aJHTA==}
    dev: false

  /@mui/material@5.14.5(@emotion/react@11.11.1)(@emotion/styled@11.11.0)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-4qa4GMfuZH0Ai3mttk5ccXP8a3sf7aPlAJwyMrUSz6h9hPri6BPou94zeu3rENhhmKLby9S/W1y+pmficy8JKA==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@emotion/react': ^11.5.0
      '@emotion/styled': ^11.3.0
      '@types/react': ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
      react-dom: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@emotion/react': 11.11.1(@types/react@18.2.14)(react@18.2.0)
      '@emotion/styled': 11.11.0(@emotion/react@11.11.1)(@types/react@18.2.14)(react@18.2.0)
      '@mui/base': 5.0.0-beta.11(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@mui/core-downloads-tracker': 5.14.5
      '@mui/system': 5.14.5(@emotion/react@11.11.1)(@emotion/styled@11.11.0)(@types/react@18.2.14)(react@18.2.0)
      '@mui/types': 7.2.4(@types/react@18.2.14)
      '@mui/utils': 5.14.5(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-transition-group': 4.4.6
      clsx: 2.0.0
      csstype: 3.1.2
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 18.2.0
      react-transition-group: 4.4.5(react-dom@18.2.0)(react@18.2.0)
    dev: false

  /@mui/private-theming@5.14.5(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-cC4C5RrpXpDaaZyH9QwmPhRLgz+f2SYbOty3cPkk4qPSOSfif2ZEcDD9HTENKDDd9deB+xkPKzzZhi8cxIx8Ig==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@mui/utils': 5.14.5(react@18.2.0)
      '@types/react': 18.2.14
      prop-types: 15.8.1
      react: 18.2.0
    dev: false

  /@mui/styled-engine@5.13.2(@emotion/react@11.11.1)(@emotion/styled@11.11.0)(react@18.2.0):
    resolution: {integrity: sha512-VCYCU6xVtXOrIN8lcbuPmoG+u7FYuOERG++fpY74hPpEWkyFQG97F+/XfTQVYzlR2m7nPjnwVUgATcTCMEaMvw==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@emotion/react': ^11.4.1
      '@emotion/styled': ^11.3.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@emotion/cache': 11.11.0
      '@emotion/react': 11.11.1(@types/react@18.2.14)(react@18.2.0)
      '@emotion/styled': 11.11.0(@emotion/react@11.11.1)(@types/react@18.2.14)(react@18.2.0)
      csstype: 3.1.2
      prop-types: 15.8.1
      react: 18.2.0
    dev: false

  /@mui/system@5.14.5(@emotion/react@11.11.1)(@emotion/styled@11.11.0)(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-mextXZHDeGcR7E1kx43TRARrVXy+gI4wzpUgNv7MqZs1dvTVXQGVeAT6ydj9d6FUqHBPMNLGV/21vJOrpqsL+w==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@emotion/react': ^11.5.0
      '@emotion/styled': ^11.3.0
      '@types/react': ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@emotion/react': 11.11.1(@types/react@18.2.14)(react@18.2.0)
      '@emotion/styled': 11.11.0(@emotion/react@11.11.1)(@types/react@18.2.14)(react@18.2.0)
      '@mui/private-theming': 5.14.5(@types/react@18.2.14)(react@18.2.0)
      '@mui/styled-engine': 5.13.2(@emotion/react@11.11.1)(@emotion/styled@11.11.0)(react@18.2.0)
      '@mui/types': 7.2.4(@types/react@18.2.14)
      '@mui/utils': 5.14.5(react@18.2.0)
      '@types/react': 18.2.14
      clsx: 2.0.0
      csstype: 3.1.2
      prop-types: 15.8.1
      react: 18.2.0
    dev: false

  /@mui/types@7.2.4(@types/react@18.2.14):
    resolution: {integrity: sha512-LBcwa8rN84bKF+f5sDyku42w1NTxaPgPyYKODsh01U1fVstTClbUoSA96oyRBnSNyEiAVjKm6Gwx9vjR+xyqHA==}
    peerDependencies:
      '@types/react': '*'
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
    dev: false

  /@mui/utils@5.14.5(react@18.2.0):
    resolution: {integrity: sha512-6Hzw63VR9C5xYv+CbjndoRLU6Gntal8rJ5W+GUzkyHrGWIyYPWZPa6AevnyGioySNETATe1H9oXS8f/7qgIHJA==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      react: ^17.0.0 || ^18.0.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@types/prop-types': 15.7.5
      '@types/react-is': 18.2.1
      prop-types: 15.8.1
      react: 18.2.0
      react-is: 18.2.0
    dev: false

  /@next/env@13.4.7:
    resolution: {integrity: sha512-ZlbiFulnwiFsW9UV1ku1OvX/oyIPLtMk9p/nnvDSwI0s7vSoZdRtxXNsaO+ZXrLv/pMbXVGq4lL8TbY9iuGmVw==}
    dev: false

  /@next/swc-darwin-arm64@13.4.7:
    resolution: {integrity: sha512-VZTxPv1b59KGiv/pZHTO5Gbsdeoxcj2rU2cqJu03btMhHpn3vwzEK0gUSVC/XW96aeGO67X+cMahhwHzef24/w==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-darwin-x64@13.4.7:
    resolution: {integrity: sha512-gO2bw+2Ymmga+QYujjvDz9955xvYGrWofmxTq7m70b9pDPvl7aDFABJOZ2a8SRCuSNB5mXU8eTOmVVwyp/nAew==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-arm64-gnu@13.4.7:
    resolution: {integrity: sha512-6cqp3vf1eHxjIDhEOc7Mh/s8z1cwc/l5B6ZNkOofmZVyu1zsbEM5Hmx64s12Rd9AYgGoiCz4OJ4M/oRnkE16/Q==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-arm64-musl@13.4.7:
    resolution: {integrity: sha512-T1kD2FWOEy5WPidOn1si0rYmWORNch4a/NR52Ghyp4q7KyxOCuiOfZzyhVC5tsLIBDH3+cNdB5DkD9afpNDaOw==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-x64-gnu@13.4.7:
    resolution: {integrity: sha512-zaEC+iEiAHNdhl6fuwl0H0shnTzQoAoJiDYBUze8QTntE/GNPfTYpYboxF5LRYIjBwETUatvE0T64W6SKDipvg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-x64-musl@13.4.7:
    resolution: {integrity: sha512-X6r12F8d8SKAtYJqLZBBMIwEqcTRvUdVm+xIq+l6pJqlgT2tNsLLf2i5Cl88xSsIytBICGsCNNHd+siD2fbWBA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-win32-arm64-msvc@13.4.7:
    resolution: {integrity: sha512-NPnmnV+vEIxnu6SUvjnuaWRglZzw4ox5n/MQTxeUhb5iwVWFedolPFebMNwgrWu4AELwvTdGtWjqof53AiWHcw==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-win32-ia32-msvc@13.4.7:
    resolution: {integrity: sha512-6Hxijm6/a8XqLQpOOf/XuwWRhcuc/g4rBB2oxjgCMuV9Xlr2bLs5+lXyh8w9YbAUMYR3iC9mgOlXbHa79elmXw==}
    engines: {node: '>= 10'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-win32-x64-msvc@13.4.7:
    resolution: {integrity: sha512-sW9Yt36Db1nXJL+mTr2Wo0y+VkPWeYhygvcHj1FF0srVtV+VoDjxleKtny21QHaG05zdeZnw2fCtf2+dEqgwqA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@nodelib/fs.scandir@2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  /@nodelib/fs.stat@2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  /@nodelib/fs.walk@1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.15.0

  /@ocavue/svgmoji-cjs@0.1.1:
    resolution: {integrity: sha512-tCP6ggbtgIL4hPM5goVFSjL51jH/BLl/yBLy98wAV9a2L/Sn9iS3abfprPeQw6/nan5lLaz4Vz8ZP37LKh+xfQ==}
    dependencies:
      svgmoji: 3.2.0
    dev: false

  /@open-draft/deferred-promise@2.2.0:
    resolution: {integrity: sha512-CecwLWx3rhxVQF6V4bAgPS5t+So2sTbPgAzafKkVizyi7tlwpcFpdFqq+wqF2OwNBmqFuu6tOyouTuxgpMfzmA==}
    dev: false

  /@popperjs/core@2.11.8:
    resolution: {integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==}
    dev: false

  /@protobufjs/aspromise@1.1.2:
    resolution: {integrity: sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==}
    dev: false

  /@protobufjs/base64@1.1.2:
    resolution: {integrity: sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==}
    dev: false

  /@protobufjs/codegen@2.0.4:
    resolution: {integrity: sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==}
    dev: false

  /@protobufjs/eventemitter@1.1.0:
    resolution: {integrity: sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==}
    dev: false

  /@protobufjs/fetch@1.1.0:
    resolution: {integrity: sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==}
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/inquire': 1.1.0
    dev: false

  /@protobufjs/float@1.0.2:
    resolution: {integrity: sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==}
    dev: false

  /@protobufjs/inquire@1.1.0:
    resolution: {integrity: sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==}
    dev: false

  /@protobufjs/path@1.1.2:
    resolution: {integrity: sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==}
    dev: false

  /@protobufjs/pool@1.1.0:
    resolution: {integrity: sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==}
    dev: false

  /@protobufjs/utf8@1.1.0:
    resolution: {integrity: sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==}
    dev: false

  /@radix-ui/colors@0.1.9:
    resolution: {integrity: sha512-Vxq944ErPJsdVepjEUhOLO9ApUVOocA63knc+V2TkJ09D/AVOjiMIgkca/7VoYgODcla0qbSIBjje0SMfZMbAw==}
    dev: false

  /@radix-ui/number@1.0.0:
    resolution: {integrity: sha512-Ofwh/1HX69ZfJRiRBMTy7rgjAzHmwe4kW9C9Y99HTRUcYLUuVT0KESFj15rPjRgKJs20GPq8Bm5aEDJ8DuA3vA==}
    dependencies:
      '@babel/runtime': 7.22.10
    dev: false

  /@radix-ui/number@1.0.1:
    resolution: {integrity: sha512-T5gIdVO2mmPW3NNhjNgEP3cqMXjXL9UbO0BzWcXfvdBs+BohbQxvd/K5hSVKmn9/lbTdsQVKbUcP5WLCwvUbBg==}
    dependencies:
      '@babel/runtime': 7.22.10
    dev: false

  /@radix-ui/primitive@1.0.0:
    resolution: {integrity: sha512-3e7rn8FDMin4CgeL7Z/49smCA3rFYY3Ha2rUQ7HRWFadS5iCRw08ZgVT1LaNTCNqgvrUiyczLflrVrF0SRQtNA==}
    dependencies:
      '@babel/runtime': 7.22.10
    dev: false

  /@radix-ui/primitive@1.0.1:
    resolution: {integrity: sha512-yQ8oGX2GVsEYMWGxcovu1uGWPCxV5BFfeeYxqPmuAzUyLT9qmaMXSAhXpb0WrspIeqYzdJpkh2vHModJPgRIaw==}
    dependencies:
      '@babel/runtime': 7.22.10
    dev: false

  /@radix-ui/primitive@1.1.0:
    resolution: {integrity: sha512-4Z8dn6Upk0qk4P74xBhZ6Hd/w0mPEzOOLxy4xiPXOXqjF7jZS0VAKk7/x/H6FyY2zCkYJqePf1G5KmkmNJ4RBA==}
    dev: false

  /@radix-ui/react-arrow@1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-wSP+pHsB/jQRaL6voubsQ/ZlrGBHHrOjmBnr19hxYgtS0WvAFwZhK2WP/YY5yF9uKECCEEDGxuLxq1NBK51wFA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-arrow@1.1.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-FmlW1rCg7hBpEBwFbjHwCW6AmWLQM6g/v0Sn8XbP9NvmSZ2San1FpQeyPtufzOMSIx7Y4dzjlHoifhp+7NkZhw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-avatar@1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-9ToF7YNex3Ste45LrAeTlKtONI9yVRt/zOS158iilIkW5K/Apeyb/TUQlcEFTEFvWr8Kzdi2ZYrm1/suiXPajQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-collection@1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-3SzW+0PW7yBBoQlT8wNcGtaxaD0XSu0uLUFgrtHY08Acx05TaHaOmVLR73c0j/cqpDy53KBMO7s0dx2wmOIDIA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-slot': 1.0.2(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-collection@1.1.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-GZsZslMJEyo1VKm5L1ZJY8tGDxZNPAoUeQUIbKeJfoi7Q4kmig5AsgLMYYuyYbfjd8fBmFORAIwYAkXMnXZgZw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-slot': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-compose-refs@1.0.0(react@18.2.0):
    resolution: {integrity: sha512-0KaSv6sx787/hK3eF53iOkiSLwAGlFMx5lotrqD2pTjB18KbybKoEIgkNZTKC60YECDQTKGTRcDBILwZVqVKvA==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      react: 18.2.0
    dev: false

  /@radix-ui/react-compose-refs@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-fDSBgd44FKHa1FRMU59qBMPFcl2PZE+2nmqunj+BWFyYYjnhIDWL2ItDs3rrbJDQOtzt5nIebLCQc4QRfz6LJw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-compose-refs@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-b4inOtiaOnYf9KWyO3jAeeCG6FeyfY6ldiEPanbUjWd+xIk5wZeHa8yVwmrJ2vderhu/BQvzCrJI0lHd+wIiqw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-context-menu@2.2.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-99EatSTpW+hRYHt7m8wdDlLtkmTovEe8Z/hnxUPV+SKuuNL5HWNhQI4QSdjZqNSgXHay2z4M3Dym73j9p2Gx5Q==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.0
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-menu': 2.1.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-context@1.0.0(react@18.2.0):
    resolution: {integrity: sha512-1pVM9RfOQ+n/N5PJK33kRSKsr1glNxomxONs5c49MliinBY6Yw2Q995qfBUUo0/Mbg05B/sGA0gkgPI7kmSHBg==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      react: 18.2.0
    dev: false

  /@radix-ui/react-context@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-ebbrdFoYTcuZ0v4wG5tedGnp9tzcV8awzsxYph7gXUyvnNLuTIcCk1q17JEbnVhXAKG9oX3KtchwiMIAYp9NLg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-context@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-OKrckBy+sMEgYM/sMmqmErVn0kZqrHPJze+Ql3DzYsDDp0hl0L62nx/2122/Bvps1qz645jlcu2tD9lrRSdf8A==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-context@1.1.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-dialog@1.0.0(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Yn9YU+QlHYLWwV1XfKiqnGVpWYWk6MeBVM6x/bcoyPvxgjQGoeT35482viLPctTMWoMw0PoHgqfSox7Ig+957Q==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.0
      '@radix-ui/react-compose-refs': 1.0.0(react@18.2.0)
      '@radix-ui/react-context': 1.0.0(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.0.0(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-focus-guards': 1.0.0(react@18.2.0)
      '@radix-ui/react-focus-scope': 1.0.0(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-id': 1.0.0(react@18.2.0)
      '@radix-ui/react-portal': 1.0.0(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-presence': 1.0.0(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.0(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-slot': 1.0.0(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.0(react@18.2.0)
      aria-hidden: 1.2.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.5.4(@types/react@18.2.14)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /@radix-ui/react-dialog@1.0.5(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-GjWJX/AUpB703eEBanuBnIWdIXg6NvJFCXcNlSZk4xdszCdhrJgBoUd1cGk67vFO+WdA2pfI/plOpqz/5GUP6Q==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.0.5(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-focus-guards': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-focus-scope': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-id': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-portal': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-presence': 1.0.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-slot': 1.0.2(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      aria-hidden: 1.2.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.5.5(@types/react@18.2.14)(react@18.2.0)
    dev: false

  /@radix-ui/react-direction@1.0.0(react@18.2.0):
    resolution: {integrity: sha512-2HV05lGUgYcA6xgLQ4BKPDmtL+QbIZYH5fCOTAOOcJ5O0QbWS3i9lKaurLzliYUDhORI2Qr3pyjhJh44lKA3rQ==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      react: 18.2.0
    dev: false

  /@radix-ui/react-direction@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-RXcvnXgyvYvBEOhCBuddKecVkoMiI10Jcm5cTI7abJRAHYfFxeu+FBQs/DvdxSYucxR5mna0dNsL6QFlds5TMA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-direction@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-BUuBvgThEiAXh2DWu93XsT+a3aWrGqolGlqqw5VU1kG7p/ZH2cuDlM1sRLNnY3QcBS69UIz2mcKhMxDsdewhjg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-dismissable-layer@1.0.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-n7kDRfx+LB1zLueRDvZ1Pd0bxdJWDUZNQ/GWoxDn2prnuJKRdxsjulejX/ePkOsLi2tTm6P24mDqlMSgQpsT6g==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.0
      '@radix-ui/react-compose-refs': 1.0.0(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.0(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.0(react@18.2.0)
      '@radix-ui/react-use-escape-keydown': 1.0.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-dismissable-layer@1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-7UpBa/RKMoHJYjie1gkF1DlK8l1fdU/VKDpoS3rCCo8YBJR294GwcEHyxHw72yvphJ7ld0AXEcSLAzY2F/WyCg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-escape-keydown': 1.0.3(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-dismissable-layer@1.0.5(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-aJeDjQhywg9LBu2t/At58hCvr7pEm0o2Ke1x33B+MhjNmmZ17sy4KImo0KPLgsnc/zN7GPdce8Cnn0SWvwZO7g==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-escape-keydown': 1.0.3(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-dismissable-layer@1.1.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-QSxg29lfr/xcev6kSz7MAlmDnzbP1eI/Dwn3Tp1ip0KT5CUELsxkekFEMVBEoykI3oV39hKT4TKZzBNMbcTZYQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.0
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-escape-keydown': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-dropdown-menu@2.0.5(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-xdOrZzOTocqqkCkYo8yRPCib5OkTkqN7lqNCdxwPOdE466DOaNl4N8PkUIlsXthQvW5Wwkd+aEmWpfWlBoDPEw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-id': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-menu': 2.0.5(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-focus-guards@1.0.0(react@18.2.0):
    resolution: {integrity: sha512-UagjDk4ijOAnGu4WMUPj9ahi7/zJJqNZ9ZAiGPp7waUWJO0O1aWXi/udPphI0IUjvrhBsZJGSN66dR2dsueLWQ==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      react: 18.2.0
    dev: false

  /@radix-ui/react-focus-guards@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-Rect2dWbQ8waGzhMavsIbmSVCgYxkXLxxR3ZvCX79JOglzdEy4JXMb98lq4hPxUbLr77nP0UOGf4rcMU+s1pUA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-focus-guards@1.1.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-pSIwfrT1a6sIoDASCSpFwOasEwKTZWDw/iBdtnqKO7v6FeOzYJ7U53cPzYFVR3geGGXgVHaH+CdngrrAzqUGxg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-focus-scope@1.0.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-C4SWtsULLGf/2L4oGeIHlvWQx7Rf+7cX/vKOAD2dXW0A1b5QXwi3wWeaEgW+wn+SEVrraMUk05vLU9fZZz5HbQ==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-compose-refs': 1.0.0(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.0(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-focus-scope@1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-upXdPfqI4islj2CslyfUBNlaJCPybbqRHAi1KER7Isel9Q2AtSJ0zRBZv8mWQiFXD2nyAJ4BhC3yXgZ6kMBSrQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-focus-scope@1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-sL04Mgvf+FmyvZeYfNu1EPAaaxD+aw7cYeIB9L9Fvq8+urhltTRaEo5ysKOpHuKPclsZcSUMKlN05x4u+CINpA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-focus-scope@1.1.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-200UD8zylvEyL8Bx+z76RJnASR2gRMuxlgFCPAe/Q/679a/r0eK3MBVYMb7vZODZcffZBdob1EGnky78xmVvcA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-hover-card@1.0.6(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-2K3ToJuMk9wjwBOa+jdg2oPma+AmLdcEyTNsG/iC4BDVG3E0/mGCjbY8PEDSLxJcUi+nJi2QII+ec/4kWd88DA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-popper': 1.1.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-portal': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-presence': 1.0.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-icons@1.3.0(react@18.2.0):
    resolution: {integrity: sha512-jQxj/0LKgp+j9BiTXz3O3sgs26RNet2iLWmsPyRz2SIcR4q/4SbazXfnYwbAr+vLYKSfc7qxzyGQA1HLlYiuNw==}
    peerDependencies:
      react: ^16.x || ^17.x || ^18.x
    dependencies:
      react: 18.2.0
    dev: false

  /@radix-ui/react-id@1.0.0(react@18.2.0):
    resolution: {integrity: sha512-Q6iAB/U7Tq3NTolBBQbHTgclPmGWE3OlktGGqrClPozSw4vkQ1DfQAOtzgRPecKsMdJINE05iaoDUG8tRzCBjw==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-use-layout-effect': 1.0.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@radix-ui/react-id@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-tI7sT/kqYp8p96yGWY1OAnLHrqDgzHefRBKQ2YAkBS5ja7QLcZ9Z/uY7bEjPUatf8RomoXM8/1sMj1IJaE5UzQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-id@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-EJUrI8yYh7WOjNOqpoJaf1jlFIH2LvtgAl+YcFqNCa+4hj64ZXmPkAKOFs/ukjz3byN6bdb/AVUqHkI8/uWWMA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-label@2.0.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-N5ehvlM7qoTLx7nWPodsPYPgMzA5WM8zZChQg8nyFJKnDO5WHdba1vv5/H6IO5LtJMfD2Q3wh1qHFGNtK0w3bQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-menu@2.0.5(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Gw4f9pwdH+w5w+49k0gLjN0PfRDHvxmAgG16AbyJZ7zhwZ6PBHKtWohvnSwfusfnK3L68dpBREHpVkj8wEM7ZA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-collection': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-direction': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-focus-guards': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-focus-scope': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-id': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-popper': 1.1.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-portal': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-presence': 1.0.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-roving-focus': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-slot': 1.0.2(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      aria-hidden: 1.2.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.5.5(@types/react@18.2.14)(react@18.2.0)
    dev: false

  /@radix-ui/react-menu@2.1.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-lZ0R4qR2Al6fZ4yCCZzu/ReTFrylHFxIqy7OezIpWF4bL0o9biKo0pFIvkaew3TyZ9Fy5gYVrR5zCGZBVbO1zg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.0
      '@radix-ui/react-collection': 1.1.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.1.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.1.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-focus-guards': 1.1.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-focus-scope': 1.1.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-popper': 1.2.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-portal': 1.1.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-presence': 1.1.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-roving-focus': 1.1.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-slot': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      aria-hidden: 1.2.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.6.0(@types/react@18.2.14)(react@18.2.0)
    dev: false

  /@radix-ui/react-navigation-menu@1.1.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-x4Uv0N47ABx3/frJazYXxvMpZeKJe0qmRIgQ2o3lhTqnTVg+CaZfVVO4nQLn3QJcDkTz8icElKffhFng47XIBA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-collection': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-direction': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-id': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-presence': 1.0.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-previous': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-visually-hidden': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-popover@1.0.6(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-cZ4defGpkZ0qTRtlIBzJLSzL6ht7ofhhW4i1+pkemjV1IKXm0wgCRnee154qlV6r9Ttunmh2TNZhMfV2bavUyA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-focus-guards': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-focus-scope': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-id': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-popper': 1.1.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-portal': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-presence': 1.0.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-slot': 1.0.2(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      aria-hidden: 1.2.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.5.5(@types/react@18.2.14)(react@18.2.0)
    dev: false

  /@radix-ui/react-popper@1.1.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-1CnGGfFi/bbqtJZZ0P/NQY20xdG3E0LALJaLUEoKwPLwl6PPPfbeiCqMVQnhoFRAxjJj4RpBRJzDmUgsex2tSg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@floating-ui/react-dom': 2.0.1(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-arrow': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-rect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-size': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/rect': 1.0.1
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-popper@1.1.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-cKpopj/5RHZWjrbF2846jBNacjQVwkP068DfmgrNJXpvVWrOvlAmE9xSiy5OqeE+Gi8D9fP+oDhUnPqNMY8/5w==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@floating-ui/react-dom': 2.0.1(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-arrow': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-rect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-size': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/rect': 1.0.1
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-popper@1.2.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-ZnRMshKF43aBxVWPWvbj21+7TQCvhuULWJ4gNIKYpRlQt5xGRhLx66tMp8pya2UkGHTSlhpXwmjqltDYHhw7Vg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@floating-ui/react-dom': 2.0.1(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-arrow': 1.1.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-rect': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-size': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/rect': 1.1.0
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-portal@1.0.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-a8qyFO/Xb99d8wQdu4o7qnigNjTPG123uADNecz0eX4usnQEj7o+cG4ZX4zkqq98NYekT7UoEQIjxBNWIFuqTA==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-primitive': 1.0.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-portal@1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-xLYZeHrWoPmA5mEKEfZZevoVRK/Q43GfzRXkWV6qawIWWK8t6ifIiLQdd7rmQ4Vk1bmI21XhqF9BN3jWf+phpA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-portal@1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Qki+C/EuGUVCQTOTD5vzJzJuMUlewbzuKyUy+/iHM2uwGiru9gZeBJtHAPKAEkB5KWGi9mP/CHKcY0wt1aW45Q==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-portal@1.1.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-WeDYLGPxJb/5EGBoedyJbT0MpoULmwnIPMJMSldkuiMsBAv7N1cRdsTWZWht9vpPOiN3qyiGAtbK2is47/uMFg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-presence@1.0.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-A+6XEvN01NfVWiKu38ybawfHsBjWum42MRPnEuqPsBZ4eV7e/7K321B5VgYMPv3Xx5An6o1/l9ZuDBgmcmWK3w==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-compose-refs': 1.0.0(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.0.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-presence@1.0.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-UXLW4UAbIY5ZjcvzjfRFo5gxva8QirC9hF7wRE4U5gz+TP0DbRk+//qyuAQ1McDxBt1xNMBTaciFGvEmJvAZCg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-presence@1.1.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-IeFXVi4YS1K0wVZzXNrbaaUvIJ3qdY+/Ih4eHFhWA9SwGR9UDX7Ck8abvL57C4cv3wwMvUE0OG69Qc3NCcTe/A==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-primitive@1.0.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-EyXe6mnRlHZ8b6f4ilTDrXmkLShICIuOTTj0GX4w1rp+wSxf3+TD05u1UOITC8VsJ2a9nwHvdXtOXEOl0Cw/zQ==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-slot': 1.0.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-primitive@1.0.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-fHbmislWVkZaIdeF6GZxF0A/NH/3BjrGIYj+Ae6eTmTCr7EB0RQAAVEiqsXK6p3/JcRqVSBQoceZroj30Jj3XA==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-slot': 1.0.1(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-primitive@1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-yi58uVyoAcK/Nq1inRY56ZSjKypBNKTa/1mcL8qdl6oJeEaDbOldlzrGn7P6Q3Id5d+SYNGc5AJgc4vGhjs5+g==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-slot': 1.0.2(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-primitive@2.0.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-ZSpFm0/uHa8zTvKBDjLFWLo8dkr4MBsiDLz0g3gMUwqgLHz9rTaRRGYDgvZPtBJgYCBKXkS9fzmoySgr8CO6Cw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-slot': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-roving-focus@1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-2mUg5Mgcu001VkGy+FfzZyzbmuUWzgWkj3rvv4yu+mLw03+mTzbxZHvfcGyFp2b8EkQeMkpRQ5FiA2Vr2O6TeQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-collection': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-direction': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-id': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-roving-focus@1.1.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-EA6AMGeq9AEeQDeSH0aZgG198qkfHSbvWTf1HvoDmOB5bBG/qTxjYMWUKMnYiV6J/iP/J8MEFSuB2zRU2n7ODA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.0
      '@radix-ui/react-collection': 1.1.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-id': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-scroll-area@1.0.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-k8VseTxI26kcKJaX0HPwkvlNBPTs56JRdYzcZ/vzrNUkDlvXBy8sMc7WvCpYzZkHgb+hd72VW9MqkqecGtuNgg==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/number': 1.0.0
      '@radix-ui/primitive': 1.0.0
      '@radix-ui/react-compose-refs': 1.0.0(react@18.2.0)
      '@radix-ui/react-context': 1.0.0(react@18.2.0)
      '@radix-ui/react-direction': 1.0.0(react@18.2.0)
      '@radix-ui/react-presence': 1.0.0(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.1(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.0(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.0.0(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-scroll-area@1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-OIClwBkwPG+FKvC4OMTRaa/3cfD069nkKFFL/TQzRzaO42Ce5ivKU9VMKgT7UU6UIkjcQqKBrDOIzWtPGw6e6w==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/number': 1.0.1
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-direction': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-presence': 1.0.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-select@1.2.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-zI7McXr8fNaSrUY9mZe4x/HC0jTLY9fWNhO1oLWYMQGDXuV4UCivIGTxwioSzO0ZCYX9iSLyWmAh/1TOmX3Cnw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/number': 1.0.1
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-collection': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-direction': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-focus-guards': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-focus-scope': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-id': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-popper': 1.1.2(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-portal': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-slot': 1.0.2(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-previous': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-visually-hidden': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      aria-hidden: 1.2.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-remove-scroll: 2.5.5(@types/react@18.2.14)(react@18.2.0)
    dev: false

  /@radix-ui/react-separator@1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-itYmTy/kokS21aiV5+Z56MZB54KrhPgn6eHDKkFeOLR34HMN2s8PaN47qZZAGnvupcjxHaFZnW4pQEh0BvvVuw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-slot@1.0.0(react@18.2.0):
    resolution: {integrity: sha512-3mrKauI/tWXo1Ll+gN5dHcxDPdm/Df1ufcDLCecn+pnCIVcdWE7CujXo8QaXOWRJyZyQWWbpB8eFwHzWXlv5mQ==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-compose-refs': 1.0.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@radix-ui/react-slot@1.0.1(react@18.2.0):
    resolution: {integrity: sha512-avutXAFL1ehGvAXtPquu0YK5oz6ctS474iM3vNGQIkswrVhdrS52e3uoMQBzZhNRAIE0jBnUyXWNmSjGHhCFcw==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-compose-refs': 1.0.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@radix-ui/react-slot@1.0.2(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-YeTpuq4deV+6DusvVUW4ivBgnkHwECUu0BiN43L5UCDFgdhsRUWAghhTF5MbvNTPzmiFOx90asDSUjWuCNapwg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-slot@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-FUCf5XMfmW4dtYl69pdS4DbxKy8nj4M7SafBgPllysxmdachynNflAdp/gCsnYWNDnge6tI9onzMp5ARYc1KNw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-switch@1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-mxm87F88HyHztsI7N+ZUmEoARGkC22YVW5CaC+Byc+HRpuvCrOBPTAnXgf+tZ/7i0Sg/eOePGdMhUKhPaQEqow==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-previous': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-size': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-tabs@1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-egZfYY/+wRNCflXNHx+dePvnz9FbmssDTJBtgRfDY7e8SE5oIo3Py2eCB1ckAbh1Q7cQ/6yJZThJ++sgbxibog==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-direction': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-id': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-presence': 1.0.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-roving-focus': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-toast@1.1.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-wf+fc8DOywrpRK3jlPlWVe+ELYGHdKDaaARJZNuUTWyWYq7+ANCFLp4rTjZ/mcGkJJQ/vZ949Zis9xxEpfq9OA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-collection': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-portal': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-presence': 1.0.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-visually-hidden': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-toggle-group@1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Uaj/M/cMyiyT9Bx6fOZO0SAG4Cls0GptBWiBmBxofmDbNVnYYoyRWj/2M/6VCi/7qcXFWnHhRUfdfZFvvkuu8A==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-direction': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-roving-focus': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-toggle': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-toggle@1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Pkqg3+Bc98ftZGsl60CLANXQBBQ4W3mTFS9EJvNxKMZ7magklKV69/id1mlAlOFDDfHvlCms0fx8fA4CMKDJHg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-toolbar@1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-tBgmM/O7a07xbaEkYJWYTXkIdU/1pW4/KZORR43toC/4XWyBCURK0ei9kMUdp+gTPPKBgYLxXmRSH1EVcIDp8Q==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-direction': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-roving-focus': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-separator': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-toggle-group': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-tooltip@1.0.7(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-lPh5iKNFVQ/jav/j6ZrWq3blfDJ0OH9R6FlNUHPMqdLuQ9vwDgFsRxvl8b7Asuy5c8xmoojHUxKHQSOAvMHxyw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-context': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-dismissable-layer': 1.0.5(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-id': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-popper': 1.1.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-portal': 1.0.4(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-presence': 1.0.1(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@radix-ui/react-slot': 1.0.2(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@radix-ui/react-visually-hidden': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/react-use-callback-ref@1.0.0(react@18.2.0):
    resolution: {integrity: sha512-GZtyzoHz95Rhs6S63D2t/eqvdFCm7I+yHMLVQheKM7nBD8mbZIt+ct1jz4536MDnaOGKIxynJ8eHTkVGVVkoTg==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-callback-ref@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-D94LjX4Sp0xJFVaoQOd3OO9k7tpBYNOXdVhkltUbGv2Qb9OXdrg/CpsjlZv7ia14Sylv398LswWBVVu5nqKzAQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-callback-ref@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-controllable-state@1.0.0(react@18.2.0):
    resolution: {integrity: sha512-FohDoZvk3mEXh9AWAVyRTYR4Sq7/gavuofglmiXB2g1aKyboUD4YtgWxKj8O5n+Uak52gXQ4wKz5IFST4vtJHg==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-use-callback-ref': 1.0.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-controllable-state@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-Svl5GY5FQeN758fWKrjM6Qb7asvXeiZltlT4U2gVfl8Gx5UAv2sMR0LWo8yhsIZh2oQ0eFdZ59aoOOMV7b47VA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-controllable-state@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-MtfMVJiSr2NjzS0Aa90NPTnvTSg6C/JLCV7ma0W6+OMV78vd8OyRpID+Ng9LxzsPbLeuBnWBA1Nq30AtBIDChw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-escape-keydown@1.0.0(react@18.2.0):
    resolution: {integrity: sha512-JwfBCUIfhXRxKExgIqGa4CQsiMemo1Xt0W/B4ei3fpzpvPENKpMKQ8mZSB6Acj3ebrAEgi2xiQvcI1PAAodvyg==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-use-callback-ref': 1.0.0(react@18.2.0)
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-escape-keydown@1.0.3(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-vyL82j40hcFicA+M4Ex7hVkB9vHgSse1ZWomAqV2Je3RleKGO5iM8KMOEtfoSB0PnIelMd2lATjTGMYqN5ylTg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-escape-keydown@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-layout-effect@1.0.0(react@18.2.0):
    resolution: {integrity: sha512-6Tpkq+R6LOlmQb1R5NNETLG0B4YP0wc+klfXafpUCj6JGyaUc8il7/kUZ7m59rGbXGczE9Bs+iz2qloqsZBduQ==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@babel/runtime': 7.22.10
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-layout-effect@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-v/5RegiJWYdoCvMnITBkNNx6bCj20fiaJnWtRkU18yITptraXjffz5Qbn05uOiQnOvi+dbkznkoaMltz1GnszQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-layout-effect@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-+FPE0rOdziWSrH9athwI1R0HDVbWlEhd+FR+aSDk4uWGmSJ9Z54sdZVDQPZAinJhJXwfT+qnj969mCsT2gfm5w==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-previous@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-cV5La9DPwiQ7S0gf/0qiD6YgNqM5Fk97Kdrlc5yBcrF3jyEZQwm7vYFqMo4IfeHgJXsRaMvLABFtd0OVEmZhDw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-rect@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-Cq5DLuSiuYVKNU8orzJMbl15TXilTnJKUCltMVQg53BQOF1/C5toAaGrowkgksdBQ9H+SRL23g0HDmg9tvmxXw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/rect': 1.0.1
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-rect@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-0Fmkebhr6PiseyZlYAOtLS+nb7jLmpqTrJyv61Pe68MKYW6OWdRE2kI70TaYY27u7H0lajqM3hSMMLFq18Z7nQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/rect': 1.1.0
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-size@1.0.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-ibay+VqrgcaI6veAojjofPATwledXiSmX+C0KrBk/xgpX9rBzPV3OsfwlhQdUOFbh+LKQorLYT+xTXW9V8yd0g==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-size@1.1.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-XW3/vWuIXHa+2Uwcc2ABSfcCledmXhhQPlGbfcRXbiUQI5Icjcg19BGCZVKKInYbvUCut/ufbbLLPFC5cbb1hw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /@radix-ui/react-visually-hidden@1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-D4w41yN5YRKtu464TLnByKzMDG/JlMPHtfZgQAu9v6mNakUqGUI9vUrfQKz8NK41VMm/xbZbh76NUTVtIYqOMA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@radix-ui/rect@1.0.1:
    resolution: {integrity: sha512-fyrgCaedtvMg9NK3en0pnOYJdtfwxUcNolezkNPUsoX57X8oQk+NkqcvzHXD2uKNij6GXmWU9NDru2IWjrO4BQ==}
    dependencies:
      '@babel/runtime': 7.22.10
    dev: false

  /@radix-ui/rect@1.1.0:
    resolution: {integrity: sha512-A9+lCBZoaMJlVKcRBz2YByCG+Cp2t6nAnMnNba+XiWxnj6r4JUFqfsgwocMBZU9LPtdxC6wB56ySYpc7LQIoJg==}
    dev: false

  /@react-dnd/asap@5.0.2:
    resolution: {integrity: sha512-WLyfoHvxhs0V9U+GTsGilGgf2QsPl6ZZ44fnv0/b8T3nQyvzxidxsg/ZltbWssbsRDlYW8UKSQMTGotuTotZ6A==}
    dev: false

  /@react-dnd/invariant@4.0.2:
    resolution: {integrity: sha512-xKCTqAK/FFauOM9Ta2pswIyT3D8AQlfrYdOi/toTPEhqCuAs1v5tcJ3Y08Izh1cJ5Jchwy9SeAXmMg6zrKs2iw==}
    dev: false

  /@react-dnd/shallowequal@4.0.2:
    resolution: {integrity: sha512-/RVXdLvJxLg4QKvMoM5WlwNR9ViO9z8B/qPcc+C0Sa/teJY7QG7kJ441DwzOjMYEY7GmU4dj5EcGHIkKZiQZCA==}
    dev: false

  /@react-hook/intersection-observer@3.1.1(react@18.2.0):
    resolution: {integrity: sha512-OTDx8/wFaRvzFtKl1dEUEXSOqK2zVJHporiTTdC2xO++0e9FEx9wIrPis5q3lqtXeZH9zYGLbk+aB75qNFbbuw==}
    peerDependencies:
      react: '>=16.8'
    dependencies:
      '@react-hook/passive-layout-effect': 1.2.1(react@18.2.0)
      intersection-observer: 0.10.0
      react: 18.2.0
    dev: false

  /@react-hook/passive-layout-effect@1.2.1(react@18.2.0):
    resolution: {integrity: sha512-IwEphTD75liO8g+6taS+4oqz+nnroocNfWVHWz7j+N+ZO2vYrc6PV1q7GQhuahL0IOR7JccFTsFKQ/mb6iZWAg==}
    peerDependencies:
      react: '>=16.8'
    dependencies:
      react: 18.2.0
    dev: false

  /@remirror/core-constants@2.0.2:
    resolution: {integrity: sha512-dyHY+sMF0ihPus3O27ODd4+agdHMEmuRdyiZJ2CCWjPV5UFmn17ZbElvk6WOGVE4rdCJKZQCrPV2BcikOMLUGQ==}
    dev: false

  /@remirror/core-helpers@3.0.0:
    resolution: {integrity: sha512-tusEgQJIqg4qKj6HSBUFcyRnWnziw3neh4T9wOmsPGHFC3w9kl5KSrDb9UAgE8uX6y32FnS7vJ955mWOl3n50A==}
    dependencies:
      '@remirror/core-constants': 2.0.2
      '@remirror/types': 1.0.1
      '@types/object.omit': 3.0.0
      '@types/object.pick': 1.3.2
      '@types/throttle-debounce': 2.1.0
      case-anything: 2.1.13
      dash-get: 1.0.2
      deepmerge: 4.3.1
      fast-deep-equal: 3.1.3
      make-error: 1.3.6
      object.omit: 3.0.0
      object.pick: 1.3.0
      throttle-debounce: 3.0.1
    dev: false

  /@remirror/core-types@2.0.5(@remirror/pm@2.0.8):
    resolution: {integrity: sha512-K4pd23sQnBGjdzow7qvdkDfTM8klUxBgdOvo2qBOmd0XsjrtIy7t41CcBR3B5lQM9GsCA2J+XeMUN2bxyJFyEg==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@remirror/core-constants': 2.0.2
      '@remirror/pm': 2.0.8
      '@remirror/types': 1.0.1
    dev: false

  /@remirror/core-utils@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-5UggNc6Z2d7M8SVkstsVitID8DAHSKPrqet7Hfn4/dY+p4iMCOdwf9cLqcHMg3467k5/5/RvJPMTr9GQOEx7Hg==}
    peerDependencies:
      '@remirror/pm': ^2.0.7
      '@types/node': '*'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core-constants': 2.0.2
      '@remirror/core-helpers': 3.0.0
      '@remirror/core-types': 2.0.5(@remirror/pm@2.0.8)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@types/min-document': 2.19.0
      '@types/node': 20.3.3
      css-in-js-utils: 3.1.0
      get-dom-document: 0.1.3
      min-document: 2.19.0
      parenthesis: 3.1.8
    transitivePeerDependencies:
      - jsdom
    dev: false

  /@remirror/core@2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-TGvDPUdKYqOiDQmt3+58GNBi4PX6QhBhII1qk9btZ/uFvG2/LLHEe+KN/BfBdvykGAu8CK9codLzg8NZd2fDEg==}
    peerDependencies:
      '@remirror/pm': ^2.0.7
    dependencies:
      '@babel/runtime': 7.22.10
      '@linaria/core': 4.2.10
      '@remirror/core-constants': 2.0.2
      '@remirror/core-helpers': 3.0.0
      '@remirror/core-types': 2.0.5(@remirror/pm@2.0.8)
      '@remirror/core-utils': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/i18n': 2.0.5
      '@remirror/icons': 2.0.3
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      nanoevents: 5.1.13
      tiny-warning: 1.0.3
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/dom@2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-DxjEFgQc24O93WN/YL7nHUanER/a1FIGQGRgsFoN9Px2t+Op39i2PZ0hm1ZE7ivp9/gE7obaMbgsXnjEJ8RX+A==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/pm': 2.0.8
      '@remirror/preset-core': 2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-annotation@2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-jtLfWaNWQFfsE3lguQbYds4W3HJGLM0Ezrsc1q1w17EQxr5aJnjA2tNMjZL+6IdFzqEjuxd7Y9aRln3rU4WbnA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-positioner': 2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-bidi@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-tM0Ov4UpwIAs665T6kcsqb8sCZ2OwDcesGTHbBNI+NAxf9gpr/IALFA/xhzzQbpSj44yli1EYzVjQgcbKYfsKA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@types/direction': 1.0.0
      direction: 1.0.4
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-blockquote@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-jWko6/spZ49OvZVZVFUSPhOIZR8VY9aUFiMKJQfclkL3fESJPSPshkm6rANrnY24sk7VKfpXgpwCSqmJVJ8EiA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-bold@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-b4yaQcU0m/tXxUVAlDSHcm3Z4dVnWKXfUmlpP8QDjISQ0F0vloRWSK2U9yvcldhmnTNJ/il0kn9PmJeoRbqkeA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-callout@2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-qn7o1JCy7k0rsybh57EtC8qRoZyEDkncxAtr4Rq1Z2PH/axuOJwwdIgeDadEGITpviRsqrh2L4ddogpDVWVImg==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-code-block@2.0.18(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-Qu51glo0xQMUlSYiFR20HmYEnOJF1OcbZYLTcF32oa8Uq1JWStv1DacQaACUQEhZ6DKgxFZxFBWbPLonzZ1bmw==}
    peerDependencies:
      '@remirror/pm': ^2.0.8
      '@types/prettier': ^2.7.2
      prettier: ^2.8.8
    peerDependenciesMeta:
      '@types/prettier':
        optional: true
      '@types/refractor':
        optional: true
      prettier:
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
      '@types/refractor': 3.0.2
      refractor: 3.6.0
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-code@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-uf4mkIHcw5RIscw1YcOMwikMMu+x5cqUkFdo5jjA3cssirh/87xDQJAeXLB4weN/ZNexbaJ2tSslWpMFrNBW8A==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-codemirror5@2.0.13(@remirror/pm@2.0.8)(@types/codemirror@5.60.8)(@types/node@20.3.3)(codemirror@5.65.14):
    resolution: {integrity: sha512-9p9kYtBJLIC7kIQJOCa+xIg3IdaH2gb9OBDRo1AmjDAN68f8x2Ta8VAJD/h+wYhdSS96Q+4LMUBzxgZwssRDpw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
      '@types/codemirror': '*'
      codemirror: ^5.65.12
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@types/codemirror': 5.60.8
      codemirror: 5.65.14
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-collaboration@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-sqkUjsPYQnuBIbyMW/FsSkQSyQ9GbQcawrigcCOxwlQp3f1snzKzfEcGRMPrUAG06UfDRedoGv/HVb+K4uIiIw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-columns@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-0WROpbsdCsuoHFTJB5daAIwjO4tGy9hVWx5kQk4P6bkLOW/qo5hSN6iQLi2mQsAG8pi7tn/NZtMNc1awWrRWhQ==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-diff@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-6IxewTYkiFTOqEB7W3uZHBCS6pTw76qJVceWziFnSzwlf0JPqs80C4y6BLOdC6rIa3nB9USSlDkf8y1HDubVZw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-doc@2.1.5(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-LwXEVWytMUKldjniQ9Xet1kQ97aIubmh8ECK0I3mp7wwxx8Uotg1oYHRruWZHDBK9zH/H2yLaRWrQPF/FOYjmw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-drop-cursor@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-To6YtaBZ4v4XWaoD1oMd/EGsKMw2plLizZHrH70q7voP5rJjjzQ1O0Z8N/qgeM6Ai7ZM1O8PAozUW1OpqQQJJA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-embed@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-O0UzrmMWBNWZvaWPpc9ZafhJx4QgzpPqJ3p+7jDgEbtaB5VArTuKsYReSpyyGU8a0PcPOx7s9aFFbh+i7IkL7A==}
    peerDependencies:
      '@remirror/pm': ^2.0.8
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@types/querystringify': 2.0.0
      prosemirror-resizable-view: 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      querystringify: 2.2.0
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-emoji@2.0.17(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-JFAx2/xDf0Pg7lUn4/HneU/Rq3alXa5uwdVXcAiCF7jqiryO/qdgH0qQ7DIiL9lvBvfpBvJYXTeuScnofvDkvw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@ocavue/svgmoji-cjs': 0.1.1
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
      emojibase: 6.1.0
      emojibase-data: 6.2.0(emojibase@6.1.0)
      emojibase-regex: 6.0.1
      escape-string-regexp: 4.0.0
      svgmoji: 3.2.0
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-entity-reference@2.2.6(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-rhmGchMv5jxaaLAvj3gPRjPxzAP8b0QzKToxGDBxQJkNRpLzik3h5Z3JDg3EdMa3fg75X1/+9UFdS1g/LDlJhQ==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-events': 2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-positioner': 2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-epic-mode@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-KDHF0Uyr0RevWtp1ca4VOL+4iD6SXlPpEbRKz7Mlg5Fnl9IGTEsraHwg9OthoTN1Kc8fzYMzOxv6qwyTsvMuVw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-events@2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-QHmYyLqKSaYnxsbZOVI3cx543lrTOzooHdH3FKjh5KBiZ84vmqBdngKvZIMWj6/2iPMIE6y+kp4VOXqm8qrLKA==}
    peerDependencies:
      '@remirror/pm': ^2.0.7
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-find@0.1.6(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-WWcA6B4HXUU6kj4SP3OxKByFk07JQj99qQOw8nTFSTZ1AtUD4N2T/D5kSK2Uzmkreh/lCI5VJvTFwVEjft+3qg==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/pm': 2.0.8
      '@types/string.prototype.matchall': 4.0.1
      escape-string-regexp: 4.0.0
      string.prototype.matchall: 4.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-font-family@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-sGq1ChvxlwlfKnrYlKtlynG8/vbQhYznf3FlrMHycpzZTn4DW16SOlJMZlYD/bPbCFIY/5T6ZSOdUVeDOv6zoQ==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-font-size@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-j7wXaOrIVdj4PapC7g4yqpPVULUqCIiUOqt2lwKVXFbET/sXMD2RujR8zSng+VFTLFQyem53jHOkJmD+B5QJKw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      round: 2.0.1
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-gap-cursor@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-oaLrh/6cRhAdEksDNmrEcqnVixRjzARDr+cq2jJfPdVG0316xgNi7CkTnWJJCWSqQ4D6lrriMY5pQO3Qwlv5Kg==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-hard-break@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-86T5bWl7O6feaXYIUylAuzq6lbpWhF6cM7CY7IW5c9+l8qpUMSktylfgAVAajjAtIfIB+g83m0D2AjL5VXksvg==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-heading@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-jBBQhLSbEvR/IbSyzBLPN6P69zGoYrW/lxclMPccBxiqr/Rzc1ynJqrbXPQOzj3P43bRLdjXBKiy2VJWzqqO8Q==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-history@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-LjW60KVpniRawttyhrWxM6ieE8sTYprqpCdMRbc7RuxBhdHaxlcQJGAjJvnBM7078qV/55S616P620YGYaBU4w==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-horizontal-rule@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-CV1lIWZ0n8btANKIP4xiUzTFbWRhOiBfdncePZvoRSYTa4UwfAQ6KapUVkcUHEL20UtDkDX8fGpF6kjtEGRCCQ==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-image@2.1.10(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-x2Sa/cXVcavOks6k5CkTrGGj0lDnDrc7PhRBbOqQCIzKXB36idRZWGBIzBkjvZrifqfxY2UUZ73BuABc0HHg6Q==}
    peerDependencies:
      '@remirror/pm': ^2.0.8
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
      prosemirror-resizable-view: 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-italic@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-DZ7BoIxQqwTWoTYgc7GpK+SCBKfDoJjh4xKQIrC/45tcK+WdPaaxNRhdtGUzwOhHL2psYT6HtDOyN/DpO5hjWQ==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-link@2.0.17(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-LZguN6F/3DyXqPdtCZn/WPXErhe5jWLoMNEsNDttKpX9kZRgqHYFuGBjOdIWP/lRa5Fh0tMk9c34+6RsWH1zdg==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-events': 2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      extract-domain: 2.2.1
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-list@2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-nqRvlQotHubZxpWxwdt+3FyV6AmEifx5aytKtz0i/jub9cM8AUKHqkRrsQchvw1Y5jaSAjHJr3J7I2sLBiH78w==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-events': 2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-markdown@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-f++5Sc9yfUFlt6YHPXId+KuzfK0gQSWYY67Vv4oz2pzP92AINKa+YULpySCxkKKj1gqvpkFlmDAUIsG5AGA+Aw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@types/marked': 4.3.1
      '@types/turndown': 5.0.1
      marked: 4.3.0
      turndown: 7.1.2
      turndown-plugin-gfm: 1.0.2
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-mention-atom@2.0.17(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-RTHmhCrz8YVcMaifpKH8NB1aNxtgLRBG6ETyZgaPN2l9xcaRvtN5YHUb5myZsw0U4rUBXePI57/91j7QkxXw0Q==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-events': 2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-mention@2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-6w+859yva48+Kg6ELIoj9+MHKljdKuXdSKU3X7USZNNiH+qb2zxdL3AHw0ifMC9ltzC3zg/ePYM5mE9SYPBt3w==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-events': 2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      escape-string-regexp: 4.0.0
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-node-formatting@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-yL1Jk1Qjs2VmruGltV++D4QenmpDdAiOa4KQvRx0cgTvIuQ/rxQBV19IXkAj+rTzYfZmtI6W1DdScVY9bqCZ9w==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-paragraph@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-/iEYz6Sn/RLqTefa/vjjyfhMQAGcpNQY7hdW0rSox+IiwDKeeY+uR2TCi3hs/D9IMzNERZXMKD5Wk8ver1kYCg==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-placeholder@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-YyrDs1Plyrpy5N6ehG3QOZNxpsQpDdI34yFQst+g2t/amnY6ScCnAJJ4GE666P4xnPF2V9gY0478Re09JmJ1KQ==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-positioner@2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-7svqCFayauNSFCXejvitFqkTUhxk74FCCoWNI2S4p8m0y80TzJDVrASjc2m8/kZALjVeIE4Byg1gE58zj3r9SA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-events': 2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
      nanoevents: 5.1.13
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-react-component@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-xKzfgrmhncUQXkTjGqlYUxjG/QmxWjtEzpXRpND3YYULTPcxlVK4dNXtPvk0aczD6cvZVZohOeRSl8ESSLSo6A==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
      '@types/react': ^16.14.0 || ^17 || ^18
      '@types/react-dom': ^16.9.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
      react-dom: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      nanoevents: 5.1.13
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-react-tables@2.2.18(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-hv4edyYEBzZ0VhAHbzcfFxbLGbHhw1jGb20jU+qx1zPrgpMakdAl5qESMdy+1yyB7kHPRPGOpzpH62pkvm9HgQ==}
    peerDependencies:
      '@remirror/pm': ^2.0.7
      react: ^16.14.0 || ^17 || ^18
      react-dom: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      react:
        optional: true
      react-dom:
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@emotion/css': 11.11.2
      '@linaria/core': 4.2.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/core-utils': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-positioner': 2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-tables': 2.3.1(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/icons': 2.0.3
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/preset-core': 2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/react-components': 2.1.17(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/react-core': 2.0.21(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/react-hooks': 2.0.25(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
      jsx-dom-cjs: 8.0.7
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/node'
      - '@types/prettier'
      - '@types/react'
      - '@types/react-dom'
      - jsdom
      - prettier
      - supports-color
    dev: false

  /@remirror/extension-search@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-PZrf20bqr8n+E1kgeb6uQJG5ZeD8nHTzzICZRe01ltwmO4vqnWMXr7Tm5Z6Byh+ZqwLbMWkIeUzoaV7RdBQ94Q==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      escape-string-regexp: 4.0.0
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-shortcuts@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-LHHn7lnbicL9CQet9w9Aayw25SqsA5s9ysGWO9R9DbrYKJWWL1EijvZ3rz+N6nlb9/ItFWA6eNSSUSausnv+iA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-strike@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-42aEM12xux3RoxTZHJ6OBPpHux96btiMC7btPz7lJ/mqcAtVvIP3RHb8D01esU7KEpqIaI6H6n2WKc/JmvCdMQ==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-sub@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-eeEdHBWynyoU6YXc6Xt+n93nK2JJ0ry2TGqr8nzomssm2gddASPJKXFoPELuwME/iqzz64SxU/RcI7BRc/lbdw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-sup@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-8Pz+K8aSlxVCeqk9ZSX+JiSBNeCau8EbGkfTg1wAatq1jCrNhFHOHoazYOqA+nJjYP7/c5hiCoPYLpyXweIX3w==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-tables@2.3.1(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-SJ/vIrEql+dSC1K2vWr+g9fGONGYMId4D4AZWq0E/ttENZo8jZqEeUAV+Z6NIfPJyScVsXKxolZAVGWfZ4JK6g==}
    peerDependencies:
      '@remirror/pm': ^2.0.7
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-events': 2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-positioner': 2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-text-case@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-sC0VoHUf2DHKAznn6+19eJTc2z8H6diz49MGodOtnGU9eCYbkKa1aoVB1ZMQndLAIQJ6kR9ElEOMvk4bpu++UA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-text-color@2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-BCcJ2zyt+pu7WGru9D9SfnodtPz0zxRibVqZOsRvNGJQN29Bvxs89e5tV+xTlAoXUz9gwxJrM6cn03umu18mbA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/i18n': 2.0.5
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
      color2k: 2.0.2
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-text-highlight@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-stZ58DDD9jJYHUbSVeJ/r61Ywp7Zt7OtdJFeeD6fjWE6gs/SwQ7MCpIMD9GpFuwOgXsRu6FlOUPllzocpsga6Q==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-text-color': 2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-text@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-0lGw8zz3OZzRY5zGrI1gXJIfuv23Yf3IdK3507oqE0oVAnP61vPB5WMaYv3EtGez9g3RoXO8S0bIJJ3syzdILg==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-trailing-node@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-DCeovi8yJ8HzTHZ/8bIxalm7aSjngN2B+DazGb5b6iLPFiEi00zucsw81bnVkHMa8DKDeH7WVbe13wZI+tn+KA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-underline@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-wrNDSqNvwqtZBiz0nOEQaWOEthxOtBVwkuTku83Sh7ftK6rNWJXtHKNKGUAFW6A9FXxvvqrnr8+n0DSgYdIAMA==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-whitespace@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-daRoiH2/xZWrz8tzV14rfG8Catd0ThPJdWgoQ0en3QxcwYsTOP1+DXE1ZwUdP/uukuHg6DMAvgMvsWwJMtVx9A==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/extension-yjs@3.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3)(yjs@13.6.7):
    resolution: {integrity: sha512-XLKWueyyXsdmv5KSitW86kwAYUrKKE5dAazv+WlHZMtKWjWhfhxMMWgct8R1Gp1sOfVHZhbGf+KxgOfxxMDsRg==}
    peerDependencies:
      '@remirror/pm': ^2.0.8
      yjs: ^13.6.1
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-view: 1.31.7
      y-prosemirror: 1.0.20(prosemirror-model@1.19.3)(prosemirror-state@1.4.3)(prosemirror-view@1.31.7)(y-protocols@1.0.5)(yjs@13.6.7)
      y-protocols: 1.0.5
      yjs: 13.6.7
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/i18n@2.0.5:
    resolution: {integrity: sha512-oZ2umZav60iu+lBoVZxr7i11yUNRYpczVUXCsClNiHN55PDPMyYwNQ9CaEJdyQCvt0lb5WCmBNpnw1mbLaj7lQ==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@lingui/core': 4.4.0
      '@lingui/detect-locale': 4.4.0
      '@remirror/core-helpers': 3.0.0
      make-plural: 6.2.2
    dev: false

  /@remirror/icons@2.0.3:
    resolution: {integrity: sha512-ruOGU4FT6WJdXsdVwfNOurSaQvnk2Uo4AkMxxyLYkBPowxmR9Xe0lOn7d7UARai0wxmwFgX6IYaMSUVLIaaMCQ==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core-helpers': 3.0.0
    dev: false

  /@remirror/messages@2.0.6:
    resolution: {integrity: sha512-JVnfuzuul4tcvnjiSM7Jj6iKDOP4hfaw79SciZ7t+cc2+iWyAcDYSrFMDV4Q50T+2IfWTYlWtKGpIhG6sfZaWw==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@lingui/core': 4.4.0
      '@remirror/core-helpers': 3.0.0
    dev: false

  /@remirror/pm@2.0.8:
    resolution: {integrity: sha512-aHJQKcPkv0dUCcpM61JpKqRhPUA0YBlGJGF1p9zrc2a7fxmwt31UOGFVk4qSdZ2UIMczCuSEy/D6Trp/fl8x2g==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core-constants': 2.0.2
      '@remirror/core-helpers': 3.0.0
      prosemirror-collab: 1.3.1
      prosemirror-commands: 1.5.2
      prosemirror-dropcursor: 1.8.1
      prosemirror-gapcursor: 1.3.2
      prosemirror-history: 1.3.2
      prosemirror-inputrules: 1.2.1
      prosemirror-keymap: 1.2.2
      prosemirror-model: 1.19.3
      prosemirror-paste-rules: 2.0.7(prosemirror-model@1.19.3)(prosemirror-state@1.4.3)(prosemirror-view@1.31.7)
      prosemirror-schema-list: 1.3.0
      prosemirror-state: 1.4.3
      prosemirror-suggest: 2.0.6(prosemirror-model@1.19.3)(prosemirror-state@1.4.3)(prosemirror-view@1.31.7)
      prosemirror-tables: 1.3.4
      prosemirror-trailing-node: 2.0.7(prosemirror-model@1.19.3)(prosemirror-state@1.4.3)(prosemirror-view@1.31.7)
      prosemirror-transform: 1.7.4
      prosemirror-view: 1.31.7
    dev: false

  /@remirror/preset-core@2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-Qg5kDJIoSqSCtqwuT/+xq1pXf5XHonnqE4oRL5Z7Tu9RRmQ6evMhdc6CqehP5xwI+52g7q9CCRQYyNAtAhS3LQ==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-doc': 2.1.5(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-events': 2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-gap-cursor': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-history': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-paragraph': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-positioner': 2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-text': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/preset-formatting@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-Dn8AITrTycEsO3kmR3kiWE8tCjWQUmK4vvyE/aGTyvHQHsDt1KhK6X42os7D+ePxeRf4OkhaZJhdpVmujmCCUw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-bold': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-columns': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-font-size': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-heading': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-italic': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-node-formatting': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-strike': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-sub': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-sup': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-text-case': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-text-color': 2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-text-highlight': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-underline': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-whitespace': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/pm': 2.0.8
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/preset-react@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-brsNceisHfXbBqmuCFEj8bBlc3Ggnz8uGynS5bHDzfFP7EZgsOjIaHu6BpkifX/3qaJlzPSj3p1keoUgeqt6Zg==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
      '@types/react': ^16.14.0 || ^17 || ^18
      '@types/react-dom': ^16.9.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
      react-dom: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-placeholder': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-react-component': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/pm': 2.0.8
      '@remirror/react-utils': 2.0.7(@remirror/pm@2.0.8)(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/preset-wysiwyg@2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-yYDfHy3WJzBmYkTd1R8WgY0xND4ZhSOQSmWWxYcCb9QJOWqeX/LREk6Ac+FV2Ygc+aoRGx5HI2R6POehe1fb5g==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-bidi': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-blockquote': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-bold': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-code': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-code-block': 2.0.18(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-drop-cursor': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-embed': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-gap-cursor': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-hard-break': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-heading': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-horizontal-rule': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-image': 2.1.10(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-italic': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-link': 2.0.17(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-list': 2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-search': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-shortcuts': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-strike': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-trailing-node': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-underline': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/pm': 2.0.8
      '@remirror/preset-core': 2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
    transitivePeerDependencies:
      - '@types/node'
      - '@types/prettier'
      - jsdom
      - prettier
      - supports-color
    dev: false

  /@remirror/react-components@2.1.17(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-25BIEfYJO10cxpChyA2fKdmQw5VSDD/Ltcjlxps9DuXTYtjPD1WQnwVdpUyW43W2r7UsvZ2OcVGtQLdJw+RBiw==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
      '@types/react': ^16.14.0 || ^17 || ^18
      '@types/react-dom': ^16.9.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
      react-dom: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@emotion/react': 11.11.1(@types/react@18.2.14)(react@18.2.0)
      '@emotion/styled': 11.11.0(@emotion/react@11.11.1)(@types/react@18.2.14)(react@18.2.0)
      '@floating-ui/react': 0.24.8(react-dom@18.2.0)(react@18.2.0)
      '@lingui/core': 4.4.0
      '@mui/material': 5.14.5(@emotion/react@11.11.1)(@emotion/styled@11.11.0)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-blockquote': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-bold': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-callout': 2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-code': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-code-block': 2.0.18(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-columns': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-find': 0.1.6(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-font-size': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-heading': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-history': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-horizontal-rule': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-italic': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-list': 2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-node-formatting': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-positioner': 2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-strike': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-sub': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-sup': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-tables': 2.3.1(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-text-color': 2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-underline': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-whitespace': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/i18n': 2.0.5
      '@remirror/icons': 2.0.3
      '@remirror/messages': 2.0.6
      '@remirror/pm': 2.0.8
      '@remirror/react-core': 2.0.21(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/react-hooks': 2.0.25(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/react-utils': 2.0.7(@remirror/pm@2.0.8)(@types/react@18.2.14)(react@18.2.0)
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
      '@seznam/compose-react-refs': 1.0.6
      '@types/react': 18.2.14
      '@types/react-color': 3.0.6
      '@types/react-dom': 18.2.6
      create-context-state: 2.0.2(@types/react@18.2.14)(react@18.2.0)
      match-sorter: 6.3.1
      multishift: 2.0.9(@remirror/pm@2.0.8)(@types/react@18.2.14)(react@18.2.0)
      react: 18.2.0
      react-color: 2.19.3(react@18.2.0)
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/node'
      - '@types/prettier'
      - jsdom
      - prettier
      - supports-color
    dev: false

  /@remirror/react-core@2.0.21(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-8c7+e0Y0LmwErqR4nPdUs73WLFKGqVs/DuE9q0wxSfXWbArAFVAAZB4PWrpcbYMbb0jSvG7rKDgnFN8NM/1f5A==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
      '@types/react': ^16.14.0 || ^17 || ^18
      '@types/react-dom': ^16.9.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
      react-dom: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-positioner': 2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-react-component': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/i18n': 2.0.5
      '@remirror/pm': 2.0.8
      '@remirror/preset-core': 2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/preset-react': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/react-renderer': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react@18.2.14)(react@18.2.0)
      '@remirror/react-utils': 2.0.7(@remirror/pm@2.0.8)(@types/react@18.2.14)(react@18.2.0)
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
      '@seznam/compose-react-refs': 1.0.6
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      create-context-state: 2.0.2(@types/react@18.2.14)(react@18.2.0)
      fast-deep-equal: 3.1.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      resize-observer-polyfill: 1.5.1
      tiny-warning: 1.0.3
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/react-editors@1.0.38(@emotion/react@11.11.1)(@emotion/styled@11.11.0)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)(styled-components@5.3.11):
    resolution: {integrity: sha512-iNpmd1UXBJuO+pYgLz9PzSoyFe/dyPlwjKcIyW+XhwPw35TGnBRqhv7MzmrlFEj5D11eqaFXBKQM0+ySCRfItA==}
    peerDependencies:
      '@types/react': ^16.14.0 || ^17 || ^18
      '@types/react-dom': ^16.9.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
      react-dom: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core-helpers': 3.0.0
      '@remirror/extension-react-tables': 2.2.18(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/pm': 2.0.8
      '@remirror/react': 2.0.35(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/styles': 2.0.7(@emotion/react@11.11.1)(@emotion/styled@11.11.0)(@types/react@18.2.14)(react@18.2.0)(styled-components@5.3.11)
      '@svgmoji/noto': 3.2.0
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      '@types/refractor': 3.0.2
      create-context-state: 2.0.2(@types/react@18.2.14)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      refractor: 3.6.0
      remirror: 2.0.36(@remirror/pm@2.0.8)(@types/node@20.3.3)
      svgmoji: 3.2.0
    transitivePeerDependencies:
      - '@emotion/css'
      - '@emotion/react'
      - '@emotion/styled'
      - '@types/node'
      - '@types/prettier'
      - '@types/styled-components'
      - jsdom
      - prettier
      - styled-components
      - supports-color
    dev: false

  /@remirror/react-hooks@2.0.25(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-/qByk9+OSDVBFD5N3CalsXNvbHF0GEfOEhNstsBGvg0xxf0NsuvPj1rYcXnVHk+9mgx2TMh+EiOhfURRR2A9vg==}
    peerDependencies:
      '@remirror/pm': ^2.0.5
      '@types/react': ^16.14.0 || ^17 || ^18
      '@types/react-dom': ^16.9.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
      react-dom: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-emoji': 2.0.17(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-events': 2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-history': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-mention': 2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-mention-atom': 2.0.17(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-positioner': 2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/i18n': 2.0.5
      '@remirror/pm': 2.0.8
      '@remirror/react-core': 2.0.21(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/react-utils': 2.0.7(@remirror/pm@2.0.8)(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      multishift: 2.0.9(@remirror/pm@2.0.8)(@types/react@18.2.14)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      use-isomorphic-layout-effect: 1.1.2(@types/react@18.2.14)(react@18.2.0)
      use-previous: 1.2.0(@types/react@18.2.14)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/react-renderer@2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-72K/+j+6H1QPPXzQJT3DxY4I+Anc9W5XvqEyPE2GQST4Q6W1Lib10Rvdh/hxcC0TuPEa+Nam2DkRWBxSSsIATw==}
    peerDependencies:
      '@types/react': ^16.14.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@types/react': 18.2.14
      react: 18.2.0
    transitivePeerDependencies:
      - '@remirror/pm'
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /@remirror/react-utils@2.0.7(@remirror/pm@2.0.8)(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-FowQ47k0IV+8qVvQZu5OOS014FTPQ1x2BsXgfikvrMlggx5mRgsFkJ5Sf56iieyUUHqBwVXIWHtR2KVanQWbVQ==}
    peerDependencies:
      '@types/react': ^16.14.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core-constants': 2.0.2
      '@remirror/core-helpers': 3.0.0
      '@remirror/core-types': 2.0.5(@remirror/pm@2.0.8)
      '@types/react': 18.2.14
      react: 18.2.0
    transitivePeerDependencies:
      - '@remirror/pm'
    dev: false

  /@remirror/react@2.0.35(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-SFs64SiQoHUatR+guSo9G1EVwBLqZec+tUtxdun11zC/5iiye0qIk+gnvu2h5pl0zwdqpK+b0ZQ/lR7JKzwagA==}
    peerDependencies:
      '@types/react': ^16.14.0 || ^17 || ^18
      '@types/react-dom': ^16.9.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
      react-dom: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/extension-placeholder': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-positioner': 2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-react-component': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/extension-react-tables': 2.2.18(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/preset-react': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/react-components': 2.1.17(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/react-core': 2.0.21(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/react-hooks': 2.0.25(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react-dom@18.2.6)(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      '@remirror/react-renderer': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)(@types/react@18.2.14)(react@18.2.0)
      '@remirror/react-utils': 2.0.7(@remirror/pm@2.0.8)(@types/react@18.2.14)(react@18.2.0)
      '@types/react': 18.2.14
      '@types/react-dom': 18.2.6
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@remirror/pm'
      - '@types/node'
      - '@types/prettier'
      - jsdom
      - prettier
      - supports-color
    dev: false

  /@remirror/styles@2.0.7(@emotion/react@11.11.1)(@emotion/styled@11.11.0)(@types/react@18.2.14)(react@18.2.0)(styled-components@5.3.11):
    resolution: {integrity: sha512-B3CMEFhVXLG0r1TgNv2v0SUecMItShuGUiVC+GdglyMXFEPEiit9BAMDph/fKsJyFeHIw+qKMZ9mxjj7aNjjVQ==}
    peerDependencies:
      '@emotion/css': ^11.11.0
      '@emotion/react': ^11.11.0
      '@emotion/styled': ^11.11.0
      '@types/react': '>= 16.14.40'
      '@types/styled-components': ^5.1.26
      react: '>= 16.14.0'
      styled-components: ^5.3.10
    peerDependenciesMeta:
      '@emotion/css':
        optional: true
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      '@types/styled-components':
        optional: true
      react:
        optional: true
      styled-components:
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@emotion/react': 11.11.1(@types/react@18.2.14)(react@18.2.0)
      '@emotion/styled': 11.11.0(@emotion/react@11.11.1)(@types/react@18.2.14)(react@18.2.0)
      '@remirror/core-helpers': 3.0.0
      '@types/react': 18.2.14
      react: 18.2.0
      styled-components: 5.3.11(@babel/core@7.24.3)(react-dom@18.2.0)(react-is@18.2.0)(react@18.2.0)
    dev: false

  /@remirror/theme@2.0.9(@remirror/pm@2.0.8):
    resolution: {integrity: sha512-MI6j7C9KImVyfSBh9GR/WQCuLQKXRKQkE0HsS8Sc/BC8a/0n4QTt7dAg5/a/+MbakyymNaGlibCdts8URgGStg==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@linaria/core': 4.2.10
      '@remirror/core-types': 2.0.5(@remirror/pm@2.0.8)
      color2k: 2.0.2
      csstype: 3.1.2
    transitivePeerDependencies:
      - '@remirror/pm'
      - supports-color
    dev: false

  /@remirror/types@1.0.1:
    resolution: {integrity: sha512-VlZQxwGnt1jtQ18D6JqdIF+uFZo525WEqrfp9BOc3COPpK4+AWCgdnAWL+ho6imWcoINlGjR/+3b6y5C1vBVEA==}
    dependencies:
      type-fest: 2.19.0
    dev: false

  /@seznam/compose-react-refs@1.0.6:
    resolution: {integrity: sha512-izzOXQfeQLonzrIQb8u6LQ8dk+ymz3WXTIXjvOlTXHq6sbzROg3NWU+9TTAOpEoK9Bth24/6F/XrfHJ5yR5n6Q==}
    dev: false

  /@stitches/core@1.2.8:
    resolution: {integrity: sha512-Gfkvwk9o9kE9r9XNBmJRfV8zONvXThnm1tcuojL04Uy5uRyqg93DC83lDebl0rocZCfKSjUv+fWYtMQmEDJldg==}
    dev: false

  /@svgmoji/blob@3.2.0:
    resolution: {integrity: sha512-N96WOrH9GxPSPZ/FuvZl6T9Rh54stAEuUcBppIRFh9/WwkU7Hczrjabw4uunwxFLX5TgR+rHlKJl3/jaTnXJrQ==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@svgmoji/core': 3.2.0
    dev: false

  /@svgmoji/core@3.2.0:
    resolution: {integrity: sha512-QsD78Op3S/5kUVsa5ierr4Wu/xwAdYuMI3Zmc/Y2ekYBEMGEUY8QxilXQRSAQ4ku4PnNV4xlB9e7xhD5hy113A==}
    dependencies:
      '@babel/runtime': 7.22.10
      emojibase: 5.2.0
      emojibase-regex: 5.1.3
      idb-keyval: 5.1.5
      match-sorter: 6.3.1
      type-fest: 1.4.0
    dev: false

  /@svgmoji/noto@3.2.0:
    resolution: {integrity: sha512-JgtNciB06hMDI1Pb1N2IgLh44XRMZUUNwBANzjY5jXTPqOCu1A1VA35ENvUsRhEUZOm8I+hbdAEHkwMVqxLeIQ==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@svgmoji/core': 3.2.0
    dev: false

  /@svgmoji/openmoji@3.2.0:
    resolution: {integrity: sha512-USHbG+O80HfmdoNAHbOnlO+2gppXJfHFWKSRFj53Th4aimWEx4/9MB3cFbC3KZ1NOqXaLBq9jDaw4vFuGDVTUQ==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@svgmoji/core': 3.2.0
    dev: false

  /@svgmoji/twemoji@3.2.0:
    resolution: {integrity: sha512-6xqZgh9viFDKf5wvrxw56ImCR3Ni84IqwK45lxojOe1Gc1Mni1GpPfr4gb7WHDKjumfx+K7BHSvX0KXt3Nr3CQ==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@svgmoji/core': 3.2.0
    dev: false

  /@swc/helpers@0.5.1:
    resolution: {integrity: sha512-sJ902EfIzn1Fa+qYmjdQqh8tPsoxyBz+8yBKC2HKUxyezKJFwPGOn7pv4WY6QuQW//ySQi5lJjA/ZT9sNWWNTg==}
    dependencies:
      tslib: 2.6.1
    dev: false

  /@tailwindcss/typography@0.5.9(tailwindcss@3.3.2):
    resolution: {integrity: sha512-t8Sg3DyynFysV9f4JDOVISGsjazNb48AeIYQwcL+Bsq5uf4RYL75C1giZ43KISjeDGBaTN3Kxh7Xj/vRSMJUUg==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders'
    dependencies:
      lodash.castarray: 4.4.0
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      postcss-selector-parser: 6.0.10
      tailwindcss: 3.3.2
    dev: true

  /@tanstack/react-table@8.9.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Ng9rdm3JPoSCi6cVZvANsYnF+UoGVRxflMb270tVj0+LjeT/ZtZ9ckxF6oLPLcKesza6VKBqtdF9mQ+vaz24Aw==}
    engines: {node: '>=12'}
    peerDependencies:
      react: '>=16'
      react-dom: '>=16'
    dependencies:
      '@tanstack/table-core': 8.9.3
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@tanstack/table-core@8.9.3:
    resolution: {integrity: sha512-NpHZBoHTfqyJk0m/s/+CSuAiwtebhYK90mDuf5eylTvgViNOujiaOaxNDxJkQQAsVvHWZftUGAx1EfO1rkKtLg==}
    engines: {node: '>=12'}
    dev: false

  /@tippyjs/react@4.2.6(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-91RicDR+H7oDSyPycI13q3b7o4O60wa2oRbjlz2fyRLmHImc4vyDwuUP8NtZaN0VARJY5hybvDYrFzhY9+Lbyw==}
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      tippy.js: 6.3.7
    dev: false

  /@tiptap/core@2.0.4(@tiptap/pm@2.0.4):
    resolution: {integrity: sha512-2YOMjRqoBGEP4YGgYpuPuBBJHMeqKOhLnS0WVwjVP84zOmMgZ7A8M6ILC9Xr7Q/qHZCvyBGWOSsI7+3HsEzzYQ==}
    peerDependencies:
      '@tiptap/pm': ^2.0.0
    dependencies:
      '@tiptap/pm': 2.0.4(@tiptap/core@2.0.4)
    dev: false

  /@tiptap/core@2.2.4(@tiptap/pm@2.2.4):
    resolution: {integrity: sha512-cRrI8IlLIhCE1hacBQzXIC8dsRvGq6a4lYWQK/BaHuZg21CG7szp3Vd8Ix+ra1f5v0xPOT+Hy+QFNQooRMKMCw==}
    peerDependencies:
      '@tiptap/pm': ^2.0.0
    dependencies:
      '@tiptap/pm': 2.2.4
    dev: false

  /@tiptap/extension-bold@2.0.4(@tiptap/core@2.0.4):
    resolution: {integrity: sha512-CWSQy1uWkVsen8HUsqhm+oEIxJrCiCENABUbhaVcJL/MqhnP4Trrh1B6O00Yfoc0XToPRRibDaHMFs4A3MSO0g==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
    dev: false

  /@tiptap/extension-bubble-menu@2.0.4(@tiptap/core@2.2.4)(@tiptap/pm@2.2.4):
    resolution: {integrity: sha512-+cRZwj0YINNNDElSAiX1pvY2K98S2j9MQW2dXV5oLqsJhqGPZsKxVo8I1u7ZtqUla3QE1V18RYPAzVgTiMRkBg==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
      '@tiptap/pm': ^2.0.0
    dependencies:
      '@tiptap/core': 2.2.4(@tiptap/pm@2.2.4)
      '@tiptap/pm': 2.2.4
      tippy.js: 6.3.7
    dev: false

  /@tiptap/extension-code@2.0.4(@tiptap/core@2.0.4):
    resolution: {integrity: sha512-HuwJSJkipZf4hkns9witv1CABNIPiB9C8lgAQXK4xJKcoUQChcnljEL+PQ2NqeEeMTEeV3nG3A/0QafH0pgTgg==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
    dev: false

  /@tiptap/extension-collaboration-cursor@2.0.4(@tiptap/core@2.0.4)(y-prosemirror@1.0.20):
    resolution: {integrity: sha512-kXNkvlwIcdNhtnYpHiwu2tlczDpTCV3Rm41VHSjlWhaWQnfBr1M7kNOJ96Ui6dnlUdgQZKQSvDQQopjdU3jqzQ==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
      y-prosemirror: 1.0.20
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
      y-prosemirror: 1.0.20(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2)(y-protocols@1.0.5)(yjs@13.6.14)
    dev: false

  /@tiptap/extension-collaboration@2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4)(y-prosemirror@1.0.20):
    resolution: {integrity: sha512-Z853jHGKPSYiG5LDYpwTgeMGNYHmbrd9dBueazo+e1xnNyjDaPxkrZMbjdVAsliMns3Omk9oLZLd28JWB4naEQ==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
      '@tiptap/pm': ^2.0.0
      y-prosemirror: 1.0.20
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
      '@tiptap/pm': 2.0.4(@tiptap/core@2.0.4)
      y-prosemirror: 1.0.20(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2)(y-protocols@1.0.5)(yjs@13.6.14)
    dev: false

  /@tiptap/extension-dropcursor@2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4):
    resolution: {integrity: sha512-1OmKBv/E+nJo2vsosvu8KwFiBB+gZM1pY61qc7JbwEKHSYAxUFHfvLkIA0IQ53Z0DHMrFSKgWmHEcbnqtGevCA==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
      '@tiptap/pm': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
      '@tiptap/pm': 2.0.4(@tiptap/core@2.0.4)
    dev: false

  /@tiptap/extension-floating-menu@2.0.4(@tiptap/core@2.2.4)(@tiptap/pm@2.2.4):
    resolution: {integrity: sha512-0YRE738k+kNKuSHhAb3jj9ZQ7Kda78RYRr+cX2jrQVueIMKebPIY07eBt6JcKmob9V9vcNn9qLtBfmygfcPUQg==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
      '@tiptap/pm': ^2.0.0
    dependencies:
      '@tiptap/core': 2.2.4(@tiptap/pm@2.2.4)
      '@tiptap/pm': 2.2.4
      tippy.js: 6.3.7
    dev: false

  /@tiptap/extension-gapcursor@2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4):
    resolution: {integrity: sha512-VxmKfBQjSSu1mNvHlydA4dJW/zawGKyqmnryiFNcUV9s+/HWLR5i9SiUl4wJM/B8sG8cQxClne5/LrCAeGNYuA==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
      '@tiptap/pm': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
      '@tiptap/pm': 2.0.4(@tiptap/core@2.0.4)
    dev: false

  /@tiptap/extension-hard-break@2.0.4(@tiptap/core@2.0.4):
    resolution: {integrity: sha512-4j8BZa6diuoRytWoIc7j25EYWWut5TZDLbb+OVURdkHnsF8B8zeNTo55W40CdwSaSyTtXtxbTIldV80ShQarGQ==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
    dev: false

  /@tiptap/extension-history@2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4):
    resolution: {integrity: sha512-3GAUszn1xZx3vniHMiX9BSKmfvb5QOb0oSLXInN+hx80CgJDIHqIFuhx2dyV9I/HWpa0cTxaLWj64kfDzb1JVg==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
      '@tiptap/pm': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
      '@tiptap/pm': 2.0.4(@tiptap/core@2.0.4)
    dev: false

  /@tiptap/extension-horizontal-rule@2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4):
    resolution: {integrity: sha512-OMx2ImQseKbSUjPbbRCuYGOJshxYedh9giWAqwgWWokhYkH4nGxXn5m7+Laj+1wLre4bnWgHWVY4wMGniEj3aw==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
      '@tiptap/pm': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
      '@tiptap/pm': 2.0.4(@tiptap/core@2.0.4)
    dev: false

  /@tiptap/extension-italic@2.0.4(@tiptap/core@2.0.4):
    resolution: {integrity: sha512-C/6+qs4Jh8xERRP0wcOopA1+emK8MOkBE4RQx5NbPnT2iCpERP0GlmHBFQIjaYPctZgKFHxsCfRnneS5Xe76+A==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
    dev: false

  /@tiptap/extension-link@2.0.4(@tiptap/core@2.0.4)(@tiptap/pm@2.0.4):
    resolution: {integrity: sha512-CliImI1hmC+J6wHxqgz9P4wMjoNSSgm3fnNHsx5z0Bn6JRA4Evh2E3KZAdMaE8xCTx89rKxMYNbamZf4VLSoqQ==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
      '@tiptap/pm': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
      '@tiptap/pm': 2.0.4(@tiptap/core@2.0.4)
      linkifyjs: 4.1.1
    dev: false

  /@tiptap/extension-paragraph@2.0.4(@tiptap/core@2.0.4):
    resolution: {integrity: sha512-nDxpopi9WigVqpfi8nU3B0fWYB14EMvKIkutNZo8wJvKGTZufNI8hw66wupIx/jZH1gFxEa5dHerw6aSYuWjgQ==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
    dev: false

  /@tiptap/extension-strike@2.0.4(@tiptap/core@2.0.4):
    resolution: {integrity: sha512-Men7LK6N/Dh3/G4/z2Z9WkDHM2Gxx1XyxYix2ZMf5CnqY37SeDNUnGDqit65pdIN3Y/TQnOZTkKSBilSAtXfJA==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
    dev: false

  /@tiptap/extension-text@2.0.4(@tiptap/core@2.0.4):
    resolution: {integrity: sha512-i8/VFlVZh7TkAI49KKX5JmC0tM8RGwyg5zUpozxYbLdCOv07AkJt+E1fLJty9mqH4Y5HJMNnyNxsuZ9Ol/ySRA==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
    dev: false

  /@tiptap/extension-underline@2.0.4(@tiptap/core@2.0.4):
    resolution: {integrity: sha512-Hvhy3iV5dWs0SFTww6sIzyQSSgVzcQuiozhDs11iP+gvFjK7ejg86KZ8wAVvyCi9K3bOMhohsw1Q2b8JSnIxcg==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
    dev: false

  /@tiptap/pm@2.0.4(@tiptap/core@2.0.4):
    resolution: {integrity: sha512-DNgxntpEaiW7ciW0BTNTL0TFqAreZTrAROWakI4XaYRAyi5H9NfZW8jmwGwMBkoZ1KB3pfy+jT/Bisy4okEQGQ==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
    dependencies:
      '@tiptap/core': 2.0.4(@tiptap/pm@2.0.4)
      prosemirror-changeset: 2.2.1
      prosemirror-collab: 1.3.1
      prosemirror-commands: 1.5.2
      prosemirror-dropcursor: 1.8.1
      prosemirror-gapcursor: 1.3.2
      prosemirror-history: 1.3.2
      prosemirror-inputrules: 1.2.1
      prosemirror-keymap: 1.2.2
      prosemirror-markdown: 1.11.2
      prosemirror-menu: 1.2.2
      prosemirror-model: 1.19.3
      prosemirror-schema-basic: 1.2.2
      prosemirror-schema-list: 1.3.0
      prosemirror-state: 1.4.3
      prosemirror-tables: 1.3.4
      prosemirror-trailing-node: 2.0.7(prosemirror-model@1.19.3)(prosemirror-state@1.4.3)(prosemirror-view@1.31.7)
      prosemirror-transform: 1.7.4
      prosemirror-view: 1.31.7
    dev: false

  /@tiptap/pm@2.2.4:
    resolution: {integrity: sha512-Po0klR165zgtinhVp1nwMubjyKx6gAY9kH3IzcniYLCkqhPgiqnAcCr61TBpp4hfK8YURBS4ihvCB1dyfCyY8A==}
    dependencies:
      prosemirror-changeset: 2.2.1
      prosemirror-collab: 1.3.1
      prosemirror-commands: 1.5.2
      prosemirror-dropcursor: 1.8.1
      prosemirror-gapcursor: 1.3.2
      prosemirror-history: 1.4.0
      prosemirror-inputrules: 1.4.0
      prosemirror-keymap: 1.2.2
      prosemirror-markdown: 1.12.0
      prosemirror-menu: 1.2.4
      prosemirror-model: 1.19.4
      prosemirror-schema-basic: 1.2.2
      prosemirror-schema-list: 1.3.0
      prosemirror-state: 1.4.3
      prosemirror-tables: 1.3.7
      prosemirror-trailing-node: 2.0.8(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2)
      prosemirror-transform: 1.8.0
      prosemirror-view: 1.33.2
    dev: false

  /@tiptap/react@2.0.4(@tiptap/core@2.2.4)(@tiptap/pm@2.2.4)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-NcrZL4Tu3+1Xfj/us5AOD7+kJhwYo2XViOB2iRRnfwS80PUtiLWDis6o3ngMGot/jBWzaMn4gofXnMWHtFdIAw==}
    peerDependencies:
      '@tiptap/core': ^2.0.0
      '@tiptap/pm': ^2.0.0
      react: ^17.0.0 || ^18.0.0
      react-dom: ^17.0.0 || ^18.0.0
    dependencies:
      '@tiptap/core': 2.2.4(@tiptap/pm@2.2.4)
      '@tiptap/extension-bubble-menu': 2.0.4(@tiptap/core@2.2.4)(@tiptap/pm@2.2.4)
      '@tiptap/extension-floating-menu': 2.0.4(@tiptap/core@2.2.4)(@tiptap/pm@2.2.4)
      '@tiptap/pm': 2.2.4
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@tootallnate/once@2.0.0:
    resolution: {integrity: sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==}
    engines: {node: '>= 10'}
    requiresBuild: true
    dev: false
    optional: true

  /@types/acorn@4.0.6:
    resolution: {integrity: sha512-veQTnWP+1D/xbxVrPC3zHnCZRjSrKfhbMUlEA43iMZLu7EsnTtkJklIuwrCPbOi8YkvDQAiW05VQQFvvz9oieQ==}
    dependencies:
      '@types/estree': 1.0.1
    dev: false

  /@types/body-parser@1.19.2:
    resolution: {integrity: sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==}
    dependencies:
      '@types/connect': 3.4.35
      '@types/node': 20.3.3
    dev: false

  /@types/codemirror@5.60.8:
    resolution: {integrity: sha512-VjFgDF/eB+Aklcy15TtOTLQeMjTo07k7KAjql8OK5Dirr7a6sJY4T1uVBDuTVG9VEmn1uUsohOpYnVfgC6/jyw==}
    dependencies:
      '@types/tern': 0.23.4
    dev: false

  /@types/connect@3.4.35:
    resolution: {integrity: sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==}
    dependencies:
      '@types/node': 20.3.3
    dev: false

  /@types/d3-color@3.1.3:
    resolution: {integrity: sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==}
    dev: false

  /@types/d3-drag@3.0.7:
    resolution: {integrity: sha512-HE3jVKlzU9AaMazNufooRJ5ZpWmLIoc90A37WU2JMmeq28w1FQqCZswHZ3xR+SuxYftzHq6WU6KJHvqxKzTxxQ==}
    dependencies:
      '@types/d3-selection': 3.0.11
    dev: false

  /@types/d3-interpolate@3.0.4:
    resolution: {integrity: sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==}
    dependencies:
      '@types/d3-color': 3.1.3
    dev: false

  /@types/d3-selection@3.0.11:
    resolution: {integrity: sha512-bhAXu23DJWsrI45xafYpkQ4NtcKMwWnAC/vKrd2l+nxMFuvOT3XMYTIj2opv8vq8AO5Yh7Qac/nSeP/3zjTK0w==}
    dev: false

  /@types/d3-transition@3.0.9:
    resolution: {integrity: sha512-uZS5shfxzO3rGlu0cC3bjmMFKsXv+SmZZcgp0KD22ts4uGXp5EVYGzu/0YdwZeKmddhcAccYtREJKkPfXkZuCg==}
    dependencies:
      '@types/d3-selection': 3.0.11
    dev: false

  /@types/d3-zoom@3.0.8:
    resolution: {integrity: sha512-iqMC4/YlFCSlO8+2Ii1GGGliCAY4XdeG748w5vQUbevlbDu0zSjH/+jojorQVBK/se0j6DUFNPBGSqD3YWYnDw==}
    dependencies:
      '@types/d3-interpolate': 3.0.4
      '@types/d3-selection': 3.0.11
    dev: false

  /@types/debug@4.1.8:
    resolution: {integrity: sha512-/vPO1EPOs306Cvhwv7KfVfYvOJqA/S/AXjaHQiJboCZzcNDb+TIJFN9/2C9DZ//ijSKWioNyUxD792QmDJ+HKQ==}
    dependencies:
      '@types/ms': 0.7.31
    dev: false

  /@types/direction@1.0.0:
    resolution: {integrity: sha512-et1wmqXm/5smJ8lTJfBnwD12/2Y7eVJLKbuaRT0h2xaKAoo1h8Dz2Io22GObDLFwxY1ddXRTLH3Gq5v44Fl/2w==}
    dev: false

  /@types/eslint-scope@3.7.7:
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==}
    dependencies:
      '@types/eslint': 8.56.6
      '@types/estree': 1.0.5
    dev: false

  /@types/eslint@8.56.6:
    resolution: {integrity: sha512-ymwc+qb1XkjT/gfoQwxIeHZ6ixH23A+tCT2ADSA/DPVKzAjwYkTXBMCQ/f6fe4wEa85Lhp26VPeUxI7wMhAi7A==}
    dependencies:
      '@types/estree': 1.0.5
      '@types/json-schema': 7.0.15
    dev: false

  /@types/estree-jsx@1.0.0:
    resolution: {integrity: sha512-3qvGd0z8F2ENTGr/GG1yViqfiKmRfrXVx5sJyHGFu3z7m5g5utCQtGp/g29JnjflhtQJBv1WDQukHiT58xPcYQ==}
    dependencies:
      '@types/estree': 1.0.1
    dev: false

  /@types/estree@1.0.1:
    resolution: {integrity: sha512-LG4opVs2ANWZ1TJoKc937iMmNstM/d0ae1vNbnBvBhqCSezgVUOzcLCqbI5elV8Vy6WKwKjaqR+zO9VKirBBCA==}
    dev: false

  /@types/estree@1.0.5:
    resolution: {integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==}
    dev: false

  /@types/express-serve-static-core@4.17.35:
    resolution: {integrity: sha512-wALWQwrgiB2AWTT91CB62b6Yt0sNHpznUXeZEcnPU3DRdlDIz74x8Qg1UUYKSVFi+va5vKOLYRBI1bRKiLLKIg==}
    dependencies:
      '@types/node': 20.3.3
      '@types/qs': 6.9.7
      '@types/range-parser': 1.2.4
      '@types/send': 0.17.1
    dev: false

  /@types/express@4.17.17:
    resolution: {integrity: sha512-Q4FmmuLGBG58btUnfS1c1r/NQdlp3DMfGDGig8WhfpA2YRUtEkxAjkZb0yvplJGYdF1fsQ81iMDcH24sSCNC/Q==}
    dependencies:
      '@types/body-parser': 1.19.2
      '@types/express-serve-static-core': 4.17.35
      '@types/qs': 6.9.7
      '@types/serve-static': 1.15.2
    dev: false

  /@types/extend@3.0.1:
    resolution: {integrity: sha512-R1g/VyKFFI2HLC1QGAeTtCBWCo6n75l41OnsVYNbmKG+kempOESaodf6BeJyUM3Q0rKa/NQcTHbB2+66lNnxLw==}
    dev: false

  /@types/glob@8.1.0:
    resolution: {integrity: sha512-IO+MJPVhoqz+28h1qLAcBEH2+xHMK6MTyHJc7MTnnYb6wsoLR29POVGJ7LycmVXIqyy/4/2ShP5sUwTXuOwb/w==}
    requiresBuild: true
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 20.3.3
    dev: false
    optional: true

  /@types/hast@2.3.5:
    resolution: {integrity: sha512-SvQi0L/lNpThgPoleH53cdjB3y9zpLlVjRbqB3rH8hx1jiRSBGAhyjV3H+URFjNVRqt2EdYNrbZE5IsGlNfpRg==}
    dependencies:
      '@types/unist': 2.0.7
    dev: false

  /@types/http-errors@2.0.1:
    resolution: {integrity: sha512-/K3ds8TRAfBvi5vfjuz8y6+GiAYBZ0x4tXv1Av6CWBWn0IlADc+ZX9pMq7oU0fNQPnBwIZl3rmeLp6SBApbxSQ==}
    dev: false

  /@types/json-schema@7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}
    dev: false

  /@types/jsonwebtoken@9.0.2:
    resolution: {integrity: sha512-drE6uz7QBKq1fYqqoFKTDRdFCPHd5TCub75BM+D+cMx7NU9hUz7SESLfC2fSCXVFMO5Yj8sOWHuGqPgjc+fz0Q==}
    dependencies:
      '@types/node': 20.3.3
    dev: false

  /@types/linkify-it@3.0.2:
    resolution: {integrity: sha512-HZQYqbiFVWufzCwexrvh694SOim8z2d+xJl5UNamcvQFejLY/2YUtzXHYi3cHdI7PMlS8ejH2slRAOJQ32aNbA==}
    requiresBuild: true
    dev: false
    optional: true

  /@types/long@4.0.2:
    resolution: {integrity: sha512-MqTGEo5bj5t157U6fA/BiDynNkn0YknVdh48CMPkTSpFTVmvao5UQmm7uEF6xBEo7qIMAlY/JSleYaE6VOdpaA==}
    dev: false

  /@types/markdown-it@12.2.3:
    resolution: {integrity: sha512-GKMHFfv3458yYy+v/N8gjufHO6MSZKCOXpZc5GXIWWy8uldwfmPn98vp81gZ5f9SVw8YYBctgfJ22a2d7AOMeQ==}
    requiresBuild: true
    dependencies:
      '@types/linkify-it': 3.0.2
      '@types/mdurl': 1.0.2
    dev: false
    optional: true

  /@types/marked@4.3.1:
    resolution: {integrity: sha512-vSSbKZFbNktrQ15v7o1EaH78EbWV+sPQbPjHG+Cp8CaNcPFUEfjZ0Iml/V0bFDwsTlYe8o6XC5Hfdp91cqPV2g==}
    dev: false

  /@types/mdast@3.0.12:
    resolution: {integrity: sha512-DT+iNIRNX884cx0/Q1ja7NyUPpZuv0KPyL5rGNxm1WC1OtHstl7n4Jb7nk+xacNShQMbczJjt8uFzznpp6kYBg==}
    dependencies:
      '@types/unist': 2.0.7
    dev: false

  /@types/mdurl@1.0.2:
    resolution: {integrity: sha512-eC4U9MlIcu2q0KQmXszyn5Akca/0jrQmwDRgpAMJai7qBWq4amIQhZyNau4VYGtCeALvW1/NtjzJJ567aZxfKA==}
    requiresBuild: true
    dev: false
    optional: true

  /@types/mime@1.3.2:
    resolution: {integrity: sha512-YATxVxgRqNH6nHEIsvg6k2Boc1JHI9ZbH5iWFFv/MTkchz3b1ieGDa5T0a9RznNdI0KhVbdbWSN+KWWrQZRxTw==}
    dev: false

  /@types/mime@3.0.1:
    resolution: {integrity: sha512-Y4XFY5VJAuw0FgAqPNd6NNoV44jbq9Bz2L7Rh/J6jLTiHBSBJa9fxqQIvkIld4GsoDOcCbvzOUAbLPsSKKg+uA==}
    dev: false

  /@types/min-document@2.19.0:
    resolution: {integrity: sha512-lsYeSW1zfNqHTL1RuaOgfAhoiOWV1RAQDKT0BZ26z4Faz8llVIj1r1ablUo5QY6yzHMketuvu4+N0sv0eZpXTg==}
    dev: false

  /@types/minimatch@3.0.5:
    resolution: {integrity: sha512-Klz949h02Gz2uZCMGwDUSDS1YBlTdDDgbWHi+81l29tQALUtvz4rAYi5uoVhE5Lagoq6DeqAUlbrHvW/mXDgdQ==}
    dev: false

  /@types/minimatch@5.1.2:
    resolution: {integrity: sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA==}
    requiresBuild: true
    dev: false
    optional: true

  /@types/ms@0.7.31:
    resolution: {integrity: sha512-iiUgKzV9AuaEkZqkOLDIvlQiL6ltuZd9tGcW3gwpnX8JbuiuhFlEGmmFXEXkN50Cvq7Os88IY2v0dkDqXYWVgA==}
    dev: false

  /@types/node-fetch@2.6.4:
    resolution: {integrity: sha512-1ZX9fcN4Rvkvgv4E6PAY5WXUFWFcRWxZa3EW83UjycOB9ljJCedb2CupIP4RZMEwF/M3eTcCihbBRgwtGbg5Rg==}
    dependencies:
      '@types/node': 20.3.3
      form-data: 3.0.1
    dev: false

  /@types/node@18.17.12:
    resolution: {integrity: sha512-d6xjC9fJ/nSnfDeU0AMDsaJyb1iHsqCSOdi84w4u+SlN/UgQdY5tRhpMzaFYsI4mnpvgTivEaQd0yOUhAtOnEQ==}
    dev: false

  /@types/node@20.3.3:
    resolution: {integrity: sha512-wheIYdr4NYML61AjC8MKj/2jrR/kDQri/CIpVoZwldwhnIrD/j9jIU5bJ8yBKuB2VhpFV7Ab6G2XkBjv9r9Zzw==}

  /@types/object.omit@3.0.0:
    resolution: {integrity: sha512-I27IoPpH250TUzc9FzXd0P1BV/BMJuzqD3jOz98ehf9dQqGkxlq+hO1bIqZGWqCg5bVOy0g4AUVJtnxe0klDmw==}
    dev: false

  /@types/object.pick@1.3.2:
    resolution: {integrity: sha512-sn7L+qQ6RLPdXRoiaE7bZ/Ek+o4uICma/lBFPyJEKDTPTBP1W8u0c4baj3EiS4DiqLs+Hk+KUGvMVJtAw3ePJg==}
    dev: false

  /@types/parse-json@4.0.0:
    resolution: {integrity: sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==}
    dev: false

  /@types/parse5@6.0.3:
    resolution: {integrity: sha512-SuT16Q1K51EAVPz1K29DJ/sXjhSQ0zjvsypYJ6tlwVsRV9jwW5Adq2ch8Dq8kDBCkYnELS7N7VNCSB5nC56t/g==}
    dev: false

  /@types/prismjs@1.26.0:
    resolution: {integrity: sha512-ZTaqn/qSqUuAq1YwvOFQfVW1AR/oQJlLSZVustdjwI+GZ8kr0MSHBj0tsXPW1EqHubx50gtBEjbPGsdZwQwCjQ==}
    dev: false

  /@types/prop-types@15.7.5:
    resolution: {integrity: sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w==}

  /@types/qs@6.9.7:
    resolution: {integrity: sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw==}
    dev: false

  /@types/querystringify@2.0.0:
    resolution: {integrity: sha512-9WgEGTevECrXJC2LSWPqiPYWq8BRmeaOyZn47js/3V6UF0PWtcVfvvR43YjeO8BzBsthTz98jMczujOwTw+WYg==}
    dev: false

  /@types/range-parser@1.2.4:
    resolution: {integrity: sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw==}
    dev: false

  /@types/react-color@3.0.6:
    resolution: {integrity: sha512-OzPIO5AyRmLA7PlOyISlgabpYUa3En74LP8mTMa0veCA719SvYQov4WLMsHvCgXP+L+KI9yGhYnqZafVGG0P4w==}
    dependencies:
      '@types/react': 18.2.14
      '@types/reactcss': 1.2.6

  /@types/react-dom@18.2.6:
    resolution: {integrity: sha512-2et4PDvg6PVCyS7fuTc4gPoksV58bW0RwSxWKcPRcHZf0PRUGq03TKcD/rUHe3azfV6/5/biUBJw+HhCQjaP0A==}
    dependencies:
      '@types/react': 18.2.14
    dev: false

  /@types/react-is@18.2.1:
    resolution: {integrity: sha512-wyUkmaaSZEzFZivD8F2ftSyAfk6L+DfFliVj/mYdOXbVjRcS87fQJLTnhk6dRZPuJjI+9g6RZJO4PNCngUrmyw==}
    dependencies:
      '@types/react': 18.2.14
    dev: false

  /@types/react-transition-group@4.4.6:
    resolution: {integrity: sha512-VnCdSxfcm08KjsJVQcfBmhEQAPnLB8G08hAxn39azX1qYBQ/5RVQuoHuKIcfKOdncuaUvEpFKFzEvbtIMsfVew==}
    dependencies:
      '@types/react': 18.2.14
    dev: false

  /@types/react@18.2.14:
    resolution: {integrity: sha512-A0zjq+QN/O0Kpe30hA1GidzyFjatVvrpIvWLxD+xv67Vt91TWWgco9IvrJBkeyHm1trGaFS/FSGqPlhyeZRm0g==}
    dependencies:
      '@types/prop-types': 15.7.5
      '@types/scheduler': 0.16.3
      csstype: 3.1.2

  /@types/reactcss@1.2.6:
    resolution: {integrity: sha512-qaIzpCuXNWomGR1Xq8SCFTtF4v8V27Y6f+b9+bzHiv087MylI/nTCqqdChNeWS7tslgROmYB7yeiruWX7WnqNg==}
    dependencies:
      '@types/react': 18.2.14

  /@types/refractor@3.0.2:
    resolution: {integrity: sha512-2HMXuwGuOqzUG+KUTm9GDJCHl0LCBKsB5cg28ujEmVi/0qgTb6jOmkVSO5K48qXksyl2Fr3C0Q2VrgD4zbwyXg==}
    dependencies:
      '@types/prismjs': 1.26.0
    dev: false

  /@types/rimraf@3.0.2:
    resolution: {integrity: sha512-F3OznnSLAUxFrCEu/L5PY8+ny8DtcFRjx7fZZ9bycvXRi3KPTRS9HOitGZwvPg0juRhXFWIeKX58cnX5YqLohQ==}
    requiresBuild: true
    dependencies:
      '@types/glob': 8.1.0
      '@types/node': 20.3.3
    dev: false
    optional: true

  /@types/scheduler@0.16.3:
    resolution: {integrity: sha512-5cJ8CB4yAx7BH1oMvdU0Jh9lrEXyPkar6F9G/ERswkCuvP4KQZfZkSjcMbAICCpQTN4OuZn8tz0HiKv9TGZgrQ==}

  /@types/send@0.17.1:
    resolution: {integrity: sha512-Cwo8LE/0rnvX7kIIa3QHCkcuF21c05Ayb0ZfxPiv0W8VRiZiNW/WuRupHKpqqGVGf7SUA44QSOUKaEd9lIrd/Q==}
    dependencies:
      '@types/mime': 1.3.2
      '@types/node': 20.3.3
    dev: false

  /@types/serve-static@1.15.2:
    resolution: {integrity: sha512-J2LqtvFYCzaj8pVYKw8klQXrLLk7TBZmQ4ShlcdkELFKGwGMfevMLneMMRkMgZxotOD9wg497LpC7O8PcvAmfw==}
    dependencies:
      '@types/http-errors': 2.0.1
      '@types/mime': 3.0.1
      '@types/node': 20.3.3
    dev: false

  /@types/string.prototype.matchall@4.0.1:
    resolution: {integrity: sha512-jVQQq9YbEcLwWejeHs4CMVZkereuGPgflaOH/BGqHOYT45f3LV0Ah2Cmc0Cby/DZ9qhIp2V3lqPTHnLXlK7VLQ==}
    dev: false

  /@types/tern@0.23.4:
    resolution: {integrity: sha512-JAUw1iXGO1qaWwEOzxTKJZ/5JxVeON9kvGZ/osgZaJImBnyjyn0cjovPsf6FNLmyGY8Vw9DoXZCMlfMkMwHRWg==}
    dependencies:
      '@types/estree': 1.0.1
    dev: false

  /@types/throttle-debounce@2.1.0:
    resolution: {integrity: sha512-5eQEtSCoESnh2FsiLTxE121IiE60hnMqcb435fShf4bpLRjEu1Eoekht23y6zXS9Ts3l+Szu3TARnTsA0GkOkQ==}
    dev: false

  /@types/turndown@5.0.1:
    resolution: {integrity: sha512-N8Ad4e3oJxh9n9BiZx9cbe/0M3kqDpOTm2wzj13wdDUxDPjfjloWIJaquZzWE1cYTAHpjOH3rcTnXQdpEfS/SQ==}
    dev: false

  /@types/unist@2.0.7:
    resolution: {integrity: sha512-cputDpIbFgLUaGQn6Vqg3/YsJwxUwHLO13v3i5ouxT4lat0khip9AEWxtERujXV9wxIB1EyF97BSJFt6vpdI8g==}
    dev: false

  /@types/uuid@9.0.2:
    resolution: {integrity: sha512-kNnC1GFBLuhImSnV7w4njQkUiJi0ZXUycu1rUaouPqiKlXkh77JKgdRnTAp1x5eBwcIwbtI+3otwzuIDEuDoxQ==}
    dev: false

  /@types/validator@13.11.1:
    resolution: {integrity: sha512-d/MUkJYdOeKycmm75Arql4M5+UuXmf4cHdHKsyw1GcvnNgL6s77UkgSgJ8TE/rI5PYsnwYq5jkcWBLuN/MpQ1A==}
    dev: false

  /@types/xml2js@0.4.14:
    resolution: {integrity: sha512-4YnrRemBShWRO2QjvUin8ESA41rH+9nQGLUGZV/1IDhi3SL9OhdpNC/MrulTWuptXKwhx/aDxE7toV0f/ypIXQ==}
    dependencies:
      '@types/node': 20.3.3
    dev: true

  /@webassemblyjs/ast@1.12.1:
    resolution: {integrity: sha512-EKfMUOPRRUTy5UII4qJDGPpqfwjOmZ5jeGFwid9mnoqIFK+e0vqoi1qH56JpmZSzEL53jKnNzScdmftJyG5xWg==}
    dependencies:
      '@webassemblyjs/helper-numbers': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
    dev: false

  /@webassemblyjs/floating-point-hex-parser@1.11.6:
    resolution: {integrity: sha512-ejAj9hfRJ2XMsNHk/v6Fu2dGS+i4UaXBXGemOfQ/JfQ6mdQg/WXtwleQRLLS4OvfDhv8rYnVwH27YJLMyYsxhw==}
    dev: false

  /@webassemblyjs/helper-api-error@1.11.6:
    resolution: {integrity: sha512-o0YkoP4pVu4rN8aTJgAyj9hC2Sv5UlkzCHhxqWj8butaLvnpdc2jOwh4ewE6CX0txSfLn/UYaV/pheS2Txg//Q==}
    dev: false

  /@webassemblyjs/helper-buffer@1.12.1:
    resolution: {integrity: sha512-nzJwQw99DNDKr9BVCOZcLuJJUlqkJh+kVzVl6Fmq/tI5ZtEyWT1KZMyOXltXLZJmDtvLCDgwsyrkohEtopTXCw==}
    dev: false

  /@webassemblyjs/helper-numbers@1.11.6:
    resolution: {integrity: sha512-vUIhZ8LZoIWHBohiEObxVm6hwP034jwmc9kuq5GdHZH0wiLVLIPcMCdpJzG4C11cHoQ25TFIQj9kaVADVX7N3g==}
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.11.6
      '@webassemblyjs/helper-api-error': 1.11.6
      '@xtuc/long': 4.2.2
    dev: false

  /@webassemblyjs/helper-wasm-bytecode@1.11.6:
    resolution: {integrity: sha512-sFFHKwcmBprO9e7Icf0+gddyWYDViL8bpPjJJl0WHxCdETktXdmtWLGVzoHbqUcY4Be1LkNfwTmXOJUFZYSJdA==}
    dev: false

  /@webassemblyjs/helper-wasm-section@1.12.1:
    resolution: {integrity: sha512-Jif4vfB6FJlUlSbgEMHUyk1j234GTNG9dBJ4XJdOySoj518Xj0oGsNi59cUQF4RRMS9ouBUxDDdyBVfPTypa5g==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/helper-buffer': 1.12.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/wasm-gen': 1.12.1
    dev: false

  /@webassemblyjs/ieee754@1.11.6:
    resolution: {integrity: sha512-LM4p2csPNvbij6U1f19v6WR56QZ8JcHg3QIJTlSwzFcmx6WSORicYj6I63f9yU1kEUtrpG+kjkiIAkevHpDXrg==}
    dependencies:
      '@xtuc/ieee754': 1.2.0
    dev: false

  /@webassemblyjs/leb128@1.11.6:
    resolution: {integrity: sha512-m7a0FhE67DQXgouf1tbN5XQcdWoNgaAuoULHIfGFIEVKA6tu/edls6XnIlkmS6FrXAquJRPni3ZZKjw6FSPjPQ==}
    dependencies:
      '@xtuc/long': 4.2.2
    dev: false

  /@webassemblyjs/utf8@1.11.6:
    resolution: {integrity: sha512-vtXf2wTQ3+up9Zsg8sa2yWiQpzSsMyXj0qViVP6xKGCUT8p8YJ6HqI7l5eCnWx1T/FYdsv07HQs2wTFbbof/RA==}
    dev: false

  /@webassemblyjs/wasm-edit@1.12.1:
    resolution: {integrity: sha512-1DuwbVvADvS5mGnXbE+c9NfA8QRcZ6iKquqjjmR10k6o+zzsRVesil54DKexiowcFCPdr/Q0qaMgB01+SQ1u6g==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/helper-buffer': 1.12.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/helper-wasm-section': 1.12.1
      '@webassemblyjs/wasm-gen': 1.12.1
      '@webassemblyjs/wasm-opt': 1.12.1
      '@webassemblyjs/wasm-parser': 1.12.1
      '@webassemblyjs/wast-printer': 1.12.1
    dev: false

  /@webassemblyjs/wasm-gen@1.12.1:
    resolution: {integrity: sha512-TDq4Ojh9fcohAw6OIMXqiIcTq5KUXTGRkVxbSo1hQnSy6lAM5GSdfwWeSxpAo0YzgsgF182E/U0mDNhuA0tW7w==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/ieee754': 1.11.6
      '@webassemblyjs/leb128': 1.11.6
      '@webassemblyjs/utf8': 1.11.6
    dev: false

  /@webassemblyjs/wasm-opt@1.12.1:
    resolution: {integrity: sha512-Jg99j/2gG2iaz3hijw857AVYekZe2SAskcqlWIZXjji5WStnOpVoat3gQfT/Q5tb2djnCjBtMocY/Su1GfxPBg==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/helper-buffer': 1.12.1
      '@webassemblyjs/wasm-gen': 1.12.1
      '@webassemblyjs/wasm-parser': 1.12.1
    dev: false

  /@webassemblyjs/wasm-parser@1.12.1:
    resolution: {integrity: sha512-xikIi7c2FHXysxXe3COrVUPSheuBtpcfhbpFj4gmu7KRLYOzANztwUU0IbsqvMqzuNK2+glRGWCEqZo1WCLyAQ==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/helper-api-error': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/ieee754': 1.11.6
      '@webassemblyjs/leb128': 1.11.6
      '@webassemblyjs/utf8': 1.11.6
    dev: false

  /@webassemblyjs/wast-printer@1.12.1:
    resolution: {integrity: sha512-+X4WAlOisVWQMikjbcvY2e0rwPsKQ9F688lksZhBcPycBBuii3O7m8FACbDMWDojpAqvjIncrG8J0XHKyQfVeA==}
    dependencies:
      '@webassemblyjs/ast': 1.12.1
      '@xtuc/long': 4.2.2
    dev: false

  /@xtuc/ieee754@1.2.0:
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}
    dev: false

  /@xtuc/long@4.2.2:
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}
    dev: false

  /@xyflow/react@12.3.1(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-PurYFxwzJa0U6RRX9k4VbNRU+vQd6mRKFR8Uk1dF81diCKZDj495y6AupqsjMHtkO66tGHV0LdenLpIHvnOEFw==}
    peerDependencies:
      react: '>=17'
      react-dom: '>=17'
    dependencies:
      '@xyflow/system': 0.0.43
      classcat: 5.0.5
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      zustand: 4.4.0(@types/react@18.2.14)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - immer
    dev: false

  /@xyflow/system@0.0.43:
    resolution: {integrity: sha512-1zHgad1cWr1mKm2xbFaarK0Jg8WRgaQ8ubSBIo/pRdq3fEgCuqgNkL9NSAP6Rvm8zi3+Lu4JPUMN+EEx5QgX9A==}
    dependencies:
      '@types/d3-drag': 3.0.7
      '@types/d3-selection': 3.0.11
      '@types/d3-transition': 3.0.9
      '@types/d3-zoom': 3.0.8
      d3-drag: 3.0.0
      d3-selection: 3.0.0
      d3-zoom: 3.0.0
    dev: false

  /a11y-status@2.0.1:
    resolution: {integrity: sha512-VcW0aF3xb5wYFDYEfK7u41HBIU/bCE9zWl4zG+RhW1g0jGgTdS3h4ulVjVs7/2+KbGMpZvafP7wf/CDs/duNtA==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@types/throttle-debounce': 2.1.0
      throttle-debounce: 3.0.1
    dev: false

  /abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}
    dependencies:
      event-target-shim: 5.0.1
    dev: false

  /acorn-import-assertions@1.9.0(acorn@8.11.3):
    resolution: {integrity: sha512-cmMwop9x+8KFhxvKrKfPYmN6/pKTYYHBqLa0DfvVZcKMJWNyWLnaqND7dx/qn66R7ewM1UX5XMaDVP5wlVTaVA==}
    peerDependencies:
      acorn: ^8
    dependencies:
      acorn: 8.11.3
    dev: false

  /acorn-jsx@5.3.2(acorn@8.10.0):
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 8.10.0
    dev: false

  /acorn@8.10.0:
    resolution: {integrity: sha512-F0SAmZ8iUtS//m8DmCTA0jlh6TDKkHQyK6xc6V4KDTyZKA9dnvX9/3sRTVQrWm79glUAZbnmmNcdYwUIHWVybw==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /acorn@8.11.3:
    resolution: {integrity: sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /acorn@8.8.2:
    resolution: {integrity: sha512-xjIYgE8HBrkpd/sJqOGNspf8uHG+NOHGOw6a/Urj8taM2EXfdNAH2oFcPeIFfsv3+kz/mJrS5VuMqbNLjCa2vw==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}
    requiresBuild: true
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /agentkeepalive@4.5.0:
    resolution: {integrity: sha512-5GG/5IbQQpC9FpkRGsSvZI5QYeSCzlJHdpBQntCsuTOxhKD8lqKhrleg2Yi7yvMIf82Ycmmqln9U8V9qwEiJew==}
    engines: {node: '>= 8.0.0'}
    dependencies:
      humanize-ms: 1.2.1
    dev: false

  /ajv-keywords@3.5.2(ajv@6.12.6):
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==}
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: 6.12.6
    dev: false

  /ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1
    dev: false

  /anser@2.1.1:
    resolution: {integrity: sha512-nqLm4HxOTpeLOxcmB3QWmV5TcDFhW9y/fyQ+hivtDFcK4OQ+pQ5fzPnXHM1Mfcm0VkLtvVi1TCPr++Qy0Q/3EQ==}
    dev: false

  /ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}
    dev: false

  /ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: false

  /ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: false

  /any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  /anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  /arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  /argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}
    dev: false

  /aria-hidden@1.2.3:
    resolution: {integrity: sha512-xcLxITLe2HYa1cnYnwCjkOO1PqUHQpozB8x9AR0OgWN2woOBi5kSDVxKfd0b7sb1hw5qFeJhXm9H1nu3xSfLeQ==}
    engines: {node: '>=10'}
    dependencies:
      tslib: 2.6.1
    dev: false

  /array-buffer-byte-length@1.0.0:
    resolution: {integrity: sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==}
    dependencies:
      call-bind: 1.0.2
      is-array-buffer: 3.0.2
    dev: false

  /array-differ@3.0.0:
    resolution: {integrity: sha512-THtfYS6KtME/yIAhKjZ2ul7XI96lQGHRputJQHO80LAWQnuGP4iCIN8vdMRboGbIEYBwU33q8Tch1os2+X0kMg==}
    engines: {node: '>=8'}
    dev: false

  /array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}
    dev: false

  /arraybuffer.prototype.slice@1.0.1:
    resolution: {integrity: sha512-09x0ZWFEjj4WD8PDbykUwo3t9arLn8NIzmmYEJFpYekOAQjpkGSyrQhNoRTcwwcFRu+ycWF78QZ63oWTqSjBcw==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.0
      call-bind: 1.0.2
      define-properties: 1.2.0
      get-intrinsic: 1.2.1
      is-array-buffer: 3.0.2
      is-shared-array-buffer: 1.0.2
    dev: false

  /arrify@2.0.1:
    resolution: {integrity: sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==}
    engines: {node: '>=8'}
    dev: false

  /assert@2.0.0:
    resolution: {integrity: sha512-se5Cd+js9dXJnu6Ag2JFc00t+HmHOen+8Q+L7O9zI0PqQXr20uk2J0XQqMxZEeo5U50o8Nvmmx7dZrl+Ufr35A==}
    dependencies:
      es6-object-assign: 1.1.0
      is-nan: 1.3.2
      object-is: 1.1.5
      util: 0.12.5
    dev: false

  /async-retry@1.3.3:
    resolution: {integrity: sha512-wfr/jstw9xNi/0teMHrRW7dsz3Lt5ARhYNZ2ewpadnhaIp5mbALhOAP+EAdsC7t4Z6wqsDVv9+W6gm1Dk9mEyw==}
    requiresBuild: true
    dependencies:
      retry: 0.13.1
    dev: false
    optional: true

  /asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}
    dev: false

  /autoprefixer@10.4.14(postcss@8.4.24):
    resolution: {integrity: sha512-FQzyfOsTlwVzjHxKEqRIAdJx9niO6VCBCoEwax/VLSoQF29ggECcPuBqUMZ+u8jCZOPSy8b8/8KnuFbp0SaFZQ==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      browserslist: 4.21.10
      caniuse-lite: 1.0.30001600
      fraction.js: 4.2.0
      normalize-range: 0.1.2
      picocolors: 1.0.0
      postcss: 8.4.24
      postcss-value-parser: 4.2.0
    dev: false

  /available-typed-arrays@1.0.5:
    resolution: {integrity: sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==}
    engines: {node: '>= 0.4'}
    dev: false

  /axios@1.4.0:
    resolution: {integrity: sha512-S4XCWMEmzvo64T9GfvQDOXgYRDJ/wsSZc7Jvdgx5u1sd0JwsuPLqb3SYmusag+edF6ziyMensPVqLTSc1PiSEA==}
    dependencies:
      follow-redirects: 1.15.2
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug
    dev: false

  /babel-merge@3.0.0(@babel/core@7.22.10):
    resolution: {integrity: sha512-eBOBtHnzt9xvnjpYNI5HmaPp/b2vMveE5XggzqHnQeHJ8mFIBrBv6WZEVIj5jJ2uwTItkqKo9gWzEEcBxEq0yw==}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.22.10
      deepmerge: 2.2.1
      object.omit: 3.0.0
    dev: false

  /babel-plugin-macros@3.1.0:
    resolution: {integrity: sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==}
    engines: {node: '>=10', npm: '>=6'}
    dependencies:
      '@babel/runtime': 7.22.10
      cosmiconfig: 7.1.0
      resolve: 1.22.4
    dev: false

  /babel-plugin-styled-components@2.1.4(@babel/core@7.24.3)(styled-components@5.3.11):
    resolution: {integrity: sha512-Xgp9g+A/cG47sUyRwwYxGM4bR/jDRg5N6it/8+HxCnbT5XNKSKDT9xm4oag/osgqjC2It/vH0yXsomOG6k558g==}
    peerDependencies:
      styled-components: '>= 2'
    dependencies:
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-module-imports': 7.22.5
      '@babel/plugin-syntax-jsx': 7.22.5(@babel/core@7.24.3)
      lodash: 4.17.21
      picomatch: 2.3.1
      styled-components: 5.3.11(@babel/core@7.24.3)(react-dom@18.2.0)(react-is@18.2.0)(react@18.2.0)
    transitivePeerDependencies:
      - '@babel/core'
    dev: false

  /bail@2.0.2:
    resolution: {integrity: sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==}
    dev: false

  /balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  /base-64@0.1.0:
    resolution: {integrity: sha512-Y5gU45svrR5tI2Vt/X9GPd3L0HNIKzGu202EjxrXMpuc2V2CiKgemAbUUsqYmZJvPtCXoUKjNZwBJzsNScUbXA==}
    dev: false

  /base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}
    dev: false

  /beautiful-dom@1.0.9:
    resolution: {integrity: sha512-DCtfle8jxvIjUgyrcokWcxVxDxS9bsWBC5lntKasOczGwkGM7Q/M22aD5OVEpIsF3blcm7S64Ub3nWHb0MUYxQ==}
    dev: false

  /big.js@5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}
    dev: false

  /bignumber.js@9.1.1:
    resolution: {integrity: sha512-pHm4LsMJ6lzgNGVfZHjMoO8sdoRhOzOH4MLmY65Jg70bpxCKu5iOHNJyfF6OyvYw7t8Fpf35RuzUyqnQsj8Vig==}
    requiresBuild: true
    dev: false
    optional: true

  /binary-extensions@2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    engines: {node: '>=8'}

  /bluebird@3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==}
    requiresBuild: true
    dev: false
    optional: true

  /boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}
    dev: false

  /brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  /brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}
    dependencies:
      balanced-match: 1.0.2
    dev: false

  /braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1

  /browserslist@4.21.10:
    resolution: {integrity: sha512-bipEBdZfVH5/pwrvqc+Ub0kUPVfGUhlKxbvfD+z1BDnPEO/X98ruXGA1WP5ASpAFKan7Qr6j736IacbZQuAlKQ==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001600
      electron-to-chromium: 1.4.488
      node-releases: 2.0.13
      update-browserslist-db: 1.0.11(browserslist@4.21.10)
    dev: false

  /browserslist@4.23.0:
    resolution: {integrity: sha512-QW8HiM1shhT2GuzkvklfjcKDiWFXHOeFCIA/huJPwHsslwcydgk7X+z2zXpEijP98UCY7HbubZt5J2Zgvf0CaQ==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001600
      electron-to-chromium: 1.4.715
      node-releases: 2.0.14
      update-browserslist-db: 1.0.13(browserslist@4.23.0)
    dev: false

  /buffer-equal-constant-time@1.0.1:
    resolution: {integrity: sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==}
    dev: false

  /buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}
    dev: false

  /buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
    dev: false

  /busboy@1.6.0:
    resolution: {integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==}
    engines: {node: '>=10.16.0'}
    dependencies:
      streamsearch: 1.1.0
    dev: false

  /call-bind@1.0.2:
    resolution: {integrity: sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==}
    dependencies:
      function-bind: 1.1.1
      get-intrinsic: 1.2.1
    dev: false

  /callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}
    dev: false

  /camelcase-css@2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}

  /camelize@1.0.1:
    resolution: {integrity: sha512-dU+Tx2fsypxTgtLoE36npi3UqcjSSMNYfkqgmoEhtZrraP5VWq0K7FkWVTYa8eMPtnU/G2txVsfdCJTn9uzpuQ==}
    dev: false

  /caniuse-lite@1.0.30001600:
    resolution: {integrity: sha512-+2S9/2JFhYmYaDpZvo0lKkfvuKIglrx68MwOBqMGHhQsNkLjB5xtc/TGoEPs+MxjSyN/72qer2g97nzR641mOQ==}
    dev: false

  /case-anything@2.1.13:
    resolution: {integrity: sha512-zlOQ80VrQ2Ue+ymH5OuM/DlDq64mEm+B9UTdHULv5osUMD6HalNTblf2b1u/m6QecjsnOkBpqVZ+XPwIVsy7Ng==}
    engines: {node: '>=12.13'}
    dev: false

  /catharsis@0.9.0:
    resolution: {integrity: sha512-prMTQVpcns/tzFgFVkVp6ak6RykZyWb3gu8ckUpd6YkTlacOd3DXGJjIpD4Q6zJirizvaiAjSSHlOsA+6sNh2A==}
    engines: {node: '>= 10'}
    requiresBuild: true
    dependencies:
      lodash: 4.17.21
    dev: false
    optional: true

  /ccount@2.0.1:
    resolution: {integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==}
    dev: false

  /chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: false

  /chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: false

  /chance@1.1.9:
    resolution: {integrity: sha512-TfxnA/DcZXRTA4OekA2zL9GH8qscbbl6X0ZqU4tXhGveVY/mXWvEQLt5GwZcYXTEyEFflVtj+pG8nc8EwSm1RQ==}
    dev: false

  /char-regex@1.0.2:
    resolution: {integrity: sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==}
    engines: {node: '>=10'}
    dev: false

  /character-entities-html4@2.1.0:
    resolution: {integrity: sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==}
    dev: false

  /character-entities-legacy@1.1.4:
    resolution: {integrity: sha512-3Xnr+7ZFS1uxeiUDvV02wQ+QDbc55o97tIV5zHScSPJpcLm/r0DFPcoY3tYRp+VZukxuMeKgXYmsXQHO05zQeA==}
    dev: false

  /character-entities-legacy@3.0.0:
    resolution: {integrity: sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==}
    dev: false

  /character-entities@1.2.4:
    resolution: {integrity: sha512-iBMyeEHxfVnIakwOuDXpVkc54HijNgCyQB2w0VfGQThle6NXn50zU6V/u+LDhxHcDUPojn6Kpga3PTAD8W1bQw==}
    dev: false

  /character-entities@2.0.2:
    resolution: {integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==}
    dev: false

  /character-reference-invalid@1.1.4:
    resolution: {integrity: sha512-mKKUkUbhPpQlCOfIuZkvSEgktjPFIsZKRRbC6KWVEMvlzblj3i3asQv5ODsrwt0N3pHAEvjP8KTQPHkp0+6jOg==}
    dev: false

  /character-reference-invalid@2.0.1:
    resolution: {integrity: sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==}
    dev: false

  /charenc@0.0.2:
    resolution: {integrity: sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==}
    dev: false

  /cheerio-select@2.1.0:
    resolution: {integrity: sha512-9v9kG0LvzrlcungtnJtpGNxY+fzECQKhK4EGJX2vByejiMX84MFNQw4UxPJl3bFbTMw+Dfs37XaIkCwTZfLh4g==}
    dependencies:
      boolbase: 1.0.0
      css-select: 5.1.0
      css-what: 6.1.0
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.1.0
    dev: false

  /cheerio@1.0.0-rc.12:
    resolution: {integrity: sha512-VqR8m68vM46BNnuZ5NtnGBKIE/DfN0cRIzg9n40EIq9NOv90ayxLBXA8fXC5gquFRGJSTRqBq25Jt2ECLR431Q==}
    engines: {node: '>= 6'}
    dependencies:
      cheerio-select: 2.1.0
      dom-serializer: 2.0.0
      domhandler: 5.0.3
      domutils: 3.1.0
      htmlparser2: 8.0.2
      parse5: 7.1.2
      parse5-htmlparser2-tree-adapter: 7.0.0
    dev: false

  /chokidar@3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  /chrome-trace-event@1.0.3:
    resolution: {integrity: sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==}
    engines: {node: '>=6.0'}
    dev: false

  /class-validator@0.14.0:
    resolution: {integrity: sha512-ct3ltplN8I9fOwUd8GrP8UQixwff129BkEtuWDKL5W45cQuLd19xqmTLu5ge78YDm/fdje6FMt0hGOhl0lii3A==}
    dependencies:
      '@types/validator': 13.11.1
      libphonenumber-js: 1.10.39
      validator: 13.11.0
    dev: false

  /class-variance-authority@0.7.0:
    resolution: {integrity: sha512-jFI8IQw4hczaL4ALINxqLEXQbWcNjoSkloa4IaufXCJr6QawJyw7tuRysRsrE8w2p/4gGaxKIt/hX3qz/IbD1A==}
    dependencies:
      clsx: 2.0.0
    dev: false

  /classcat@5.0.5:
    resolution: {integrity: sha512-JhZUT7JFcQy/EzW605k/ktHtncoo9vnyW/2GspNYwFlN1C/WmjuV/xtS04e9SOkL2sTdw0VAZ2UGCcQ9lR6p6w==}
    dev: false

  /classnames@2.3.2:
    resolution: {integrity: sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw==}
    dev: false

  /clean-set@1.1.2:
    resolution: {integrity: sha512-cA8uCj0qSoG9e0kevyOWXwPaELRPVg5Pxp6WskLMwerx257Zfnh8Nl0JBH59d7wQzij2CK7qEfJQK3RjuKKIug==}
    dev: false

  /client-only@0.0.1:
    resolution: {integrity: sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==}
    dev: false

  /cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: false

  /cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: false

  /clsx@1.1.1:
    resolution: {integrity: sha512-6/bPho624p3S2pMyvP5kKBPXnI3ufHLObBFCfgx+LkeR5lg2XYy2hqZqUf45ypD8COn2bhgGJSUE+l5dhNBieA==}
    engines: {node: '>=6'}
    dev: false

  /clsx@1.2.1:
    resolution: {integrity: sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==}
    engines: {node: '>=6'}
    dev: false

  /clsx@2.0.0:
    resolution: {integrity: sha512-rQ1+kcj+ttHG0MKVGBUXwayCCF1oh39BF5COIpRzuCEv8Mwjv0XucrI2ExNTOn9IlLifGClWQcU9BrZORvtw6Q==}
    engines: {node: '>=6'}
    dev: false

  /cmdk@0.2.0(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-JQpKvEOb86SnvMZbYaFKYhvzFntWBeSZdyii0rZPhKJj9uwJBxu4DaVYDrRN7r3mPop56oPhRw+JYWTKs66TYw==}
    peerDependencies:
      react: ^18.0.0
      react-dom: ^18.0.0
    dependencies:
      '@radix-ui/react-dialog': 1.0.0(@types/react@18.2.14)(react-dom@18.2.0)(react@18.2.0)
      command-score: 0.1.2
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /codemirror@5.65.14:
    resolution: {integrity: sha512-VSNugIBDGt0OU9gDjeVr6fNkoFQznrWEUdAApMlXQNbfE8gGO19776D6MwSqF/V/w/sDwonsQ0z7KmmI9guScg==}
    dev: false

  /codesandbox-import-util-types@2.2.3:
    resolution: {integrity: sha512-Qj00p60oNExthP2oR3vvXmUGjukij+rxJGuiaKM6tyUmSyimdZsqHI/TUvFFClAffk9s7hxGnQgWQ8KCce27qQ==}
    dev: false

  /color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3
    dev: false

  /color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: false

  /color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}
    dev: false

  /color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}
    dev: false

  /color2k@2.0.2:
    resolution: {integrity: sha512-kJhwH5nAwb34tmyuqq/lgjEKzlFXn1U99NlnB6Ws4qVaERcRUYeYP1cBw6BJ4vxaWStAUEef4WMr7WjOCnBt8w==}
    dev: false

  /colord@2.9.3:
    resolution: {integrity: sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==}

  /combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: false

  /comma-separated-tokens@1.0.8:
    resolution: {integrity: sha512-GHuDRO12Sypu2cV70d1dkA2EUmXHgntrzbpvOB+Qy+49ypNfGgFQIC2fhhXbnyrJRynDCAARsT7Ou0M6hirpfw==}
    dev: false

  /comma-separated-tokens@2.0.3:
    resolution: {integrity: sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==}
    dev: false

  /command-score@0.1.2:
    resolution: {integrity: sha512-VtDvQpIJBvBatnONUsPzXYFVKQQAhuf3XTNOAsdBxCNO/QCtUUd8LSgjn0GVarBkCad6aJCZfXgrjYbl/KRr7w==}
    dev: false

  /commander@10.0.0:
    resolution: {integrity: sha512-zS5PnTI22FIRM6ylNW8G4Ap0IEOyk62fhLSD0+uHRT9McRCLGpkVNvao4bjimpK/GShynyQkFFxHhwMcETmduA==}
    engines: {node: '>=14'}
    dev: false

  /commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}
    dev: false

  /commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}

  /compressible@2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}
    requiresBuild: true
    dependencies:
      mime-db: 1.52.0
    dev: false
    optional: true

  /compute-scroll-into-view@1.0.20:
    resolution: {integrity: sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg==}
    dev: false

  /compute-scroll-into-view@2.0.4:
    resolution: {integrity: sha512-y/ZA3BGnxoM/QHHQ2Uy49CLtnWPbt4tTPpEEZiEmmiWBFKjej7nEyH8Ryz54jH0MLXflUYA3Er2zUxPSJu5R+g==}
    dev: false

  /concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  /convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}
    dev: false

  /convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}
    dev: false

  /cosmiconfig@7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.0
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: false

  /create-context-state@2.0.2(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-WIz5i5QYt0xvlpbpSnhl4RY7WfcPy8gWtqzE6xtr2hfhuR3WJTsa5V4Y7jgPt+Knp5r0yKbKK0myK59HW2+HHw==}
    peerDependencies:
      '@types/react': ^16.14.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /crelt@1.0.6:
    resolution: {integrity: sha512-VQ2MBenTq1fWZUH9DJNGti7kKv6EeAuYr3cLwxUWhIu1baTaXh4Ib5W2CqHVqib4/MqbYGJqiL3Zb8GJZr3l4g==}
    dev: false

  /crypt@0.0.2:
    resolution: {integrity: sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==}
    dev: false

  /css-color-keywords@1.0.0:
    resolution: {integrity: sha512-FyyrDHZKEjXDpNJYvVsV960FiqQyXc/LlYmsxl2BcdMb2WPx0OGRVgTg55rPSyLSNMqP52R9r8geSp7apN3Ofg==}
    engines: {node: '>=4'}
    dev: false

  /css-in-js-utils@3.1.0:
    resolution: {integrity: sha512-fJAcud6B3rRu+KHYk+Bwf+WFL2MDCJJ1XG9x137tJQ0xYxor7XziQtuGFbWNdqrvF4Tk26O3H73nfVqXt/fW1A==}
    dependencies:
      hyphenate-style-name: 1.0.4
    dev: false

  /css-select@5.1.0:
    resolution: {integrity: sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.1.0
      nth-check: 2.1.1
    dev: false

  /css-selector-tokenizer@0.8.0:
    resolution: {integrity: sha512-Jd6Ig3/pe62/qe5SBPTN8h8LeUg/pT4lLgtavPf7updwwHpvFzxvOQBHYj2LZDMjUnBzgvIUSjRcf6oT5HzHFg==}
    dependencies:
      cssesc: 3.0.0
      fastparse: 1.1.2

  /css-to-react-native@3.2.0:
    resolution: {integrity: sha512-e8RKaLXMOFii+02mOlqwjbD00KSEKqblnpO9e++1aXS1fPQOpS1YoqdVHBqPjHNoxeF2mimzVqawm2KCbEdtHQ==}
    dependencies:
      camelize: 1.0.1
      css-color-keywords: 1.0.0
      postcss-value-parser: 4.2.0
    dev: false

  /css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}
    dev: false

  /cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  /csstype@3.0.9:
    resolution: {integrity: sha512-rpw6JPxK6Rfg1zLOYCSwle2GFOOsnjmDYDaBwEcwoOg4qlsIVCN789VkBZDJAGi4T07gI4YSutR43t9Zz4Lzuw==}
    dev: false

  /csstype@3.1.2:
    resolution: {integrity: sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ==}

  /d3-color@3.1.0:
    resolution: {integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==}
    engines: {node: '>=12'}
    dev: false

  /d3-dispatch@3.0.1:
    resolution: {integrity: sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==}
    engines: {node: '>=12'}
    dev: false

  /d3-drag@3.0.0:
    resolution: {integrity: sha512-pWbUJLdETVA8lQNJecMxoXfH6x+mO2UQo8rSmZ+QqxcbyA3hfeprFgIT//HW2nlHChWeIIMwS2Fq+gEARkhTkg==}
    engines: {node: '>=12'}
    dependencies:
      d3-dispatch: 3.0.1
      d3-selection: 3.0.0
    dev: false

  /d3-ease@3.0.1:
    resolution: {integrity: sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==}
    engines: {node: '>=12'}
    dev: false

  /d3-interpolate@3.0.1:
    resolution: {integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==}
    engines: {node: '>=12'}
    dependencies:
      d3-color: 3.1.0
    dev: false

  /d3-selection@3.0.0:
    resolution: {integrity: sha512-fmTRWbNMmsmWq6xJV8D19U/gw/bwrHfNXxrIN+HfZgnzqTHp9jOmKMhsTUjXOJnZOdZY9Q28y4yebKzqDKlxlQ==}
    engines: {node: '>=12'}
    dev: false

  /d3-timer@3.0.1:
    resolution: {integrity: sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==}
    engines: {node: '>=12'}
    dev: false

  /d3-transition@3.0.1(d3-selection@3.0.0):
    resolution: {integrity: sha512-ApKvfjsSR6tg06xrL434C0WydLr7JewBB3V+/39RMHsaXTOG0zmt/OAXeng5M5LBm0ojmxJrpomQVZ1aPvBL4w==}
    engines: {node: '>=12'}
    peerDependencies:
      d3-selection: 2 - 3
    dependencies:
      d3-color: 3.1.0
      d3-dispatch: 3.0.1
      d3-ease: 3.0.1
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-timer: 3.0.1
    dev: false

  /d3-zoom@3.0.0:
    resolution: {integrity: sha512-b8AmV3kfQaqWAuacbPuNbL6vahnOJflOhexLzMMNLga62+/nh0JzvJ0aO/5a5MVgUFGS7Hu1P9P03o3fJkDCyw==}
    engines: {node: '>=12'}
    dependencies:
      d3-dispatch: 3.0.1
      d3-drag: 3.0.0
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-transition: 3.0.1(d3-selection@3.0.0)
    dev: false

  /d@1.0.1:
    resolution: {integrity: sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA==}
    dependencies:
      es5-ext: 0.10.62
      type: 1.2.0
    dev: false

  /daisyui@3.1.7(postcss@8.4.24):
    resolution: {integrity: sha512-VQhaunQlB7Buo+AbE+S3i6H/eYknKEuKVHG67y7sbG58uEjeLK6n2rojG3YE7fwoOss6ldbUL8Oy0MyoJi0CHw==}
    engines: {node: '>=16.9.0'}
    peerDependencies:
      postcss: ^8
    dependencies:
      colord: 2.9.3
      css-selector-tokenizer: 0.8.0
      postcss: 8.4.24
      postcss-js: 4.0.1(postcss@8.4.24)
      tailwindcss: 3.3.2
    transitivePeerDependencies:
      - ts-node

  /dash-get@1.0.2:
    resolution: {integrity: sha512-4FbVrHDwfOASx7uQVxeiCTo7ggSdYZbqs8lH+WU6ViypPlDbe9y6IP5VVUDQBv9DcnyaiPT5XT0UWHgJ64zLeQ==}
    dev: false

  /date-fns@2.30.0:
    resolution: {integrity: sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==}
    engines: {node: '>=0.11'}
    dependencies:
      '@babel/runtime': 7.22.10
    dev: false

  /debug@3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
    dev: false

  /debug@4.3.4(supports-color@5.5.0):
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
      supports-color: 5.5.0
    dev: false

  /decode-named-character-reference@1.0.2:
    resolution: {integrity: sha512-O8x12RzrUF8xyVcY0KJowWsmaJxQbmy0/EtnNtHRpsOcT7dFk5W598coHqBVpmWo1oQQfsCqfCmkZN5DJrZVdg==}
    dependencies:
      character-entities: 2.0.2
    dev: false

  /deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}
    dev: false

  /deepmerge@2.2.1:
    resolution: {integrity: sha512-R9hc1Xa/NOBi9WRVUWg19rl1UB7Tt4kuPd+thNJgFZoxXsTz7ncaPaeIm+40oSGuP33DfMb4sZt1QIGiJzC4EA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}
    dev: false

  /define-properties@1.2.0:
    resolution: {integrity: sha512-xvqAVKGfT1+UAvPwKTVw/njhdQ8ZhXK4lI0bCIuCMrp2up9nPnaDftrLtmpTazqd1o+UY4zgzU+avtMbDP+ldA==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-property-descriptors: 1.0.0
      object-keys: 1.1.1
    dev: false

  /delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}
    dev: false

  /dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}
    dev: false

  /detect-node-es@1.1.0:
    resolution: {integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==}
    dev: false

  /didyoumean@1.2.2:
    resolution: {integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==}

  /diff-match-patch@1.0.5:
    resolution: {integrity: sha512-IayShXAgj/QMXgB0IWmKx+rOPuGMhqm5w6jvFxmVenXKIzRqTAAsbBPT3kWQeGANj3jGgvcvv4yK6SxqYmikgw==}
    dev: false

  /diff@5.1.0:
    resolution: {integrity: sha512-D+mk+qE8VC/PAUrlAU34N+VfXev0ghe5ywmpqrawphmVZc1bEfn56uo9qpyGp1p4xpzOHkSW4ztBd6L7Xx4ACw==}
    engines: {node: '>=0.3.1'}
    dev: false

  /digest-fetch@1.3.0:
    resolution: {integrity: sha512-CGJuv6iKNM7QyZlM2T3sPAdZWd/p9zQiRNS9G+9COUCwzWFTs0Xp8NF5iePx7wtvhDykReiRRrSeNb4oMmB8lA==}
    dependencies:
      base-64: 0.1.0
      md5: 2.3.0
    dev: false

  /direction@1.0.4:
    resolution: {integrity: sha512-GYqKi1aH7PJXxdhTeZBFrg8vUBeKXi+cNprXsC1kpJcbcVnV9wBsrOu1cQEdG0WeQwlfHiy3XvnKfIrJ2R0NzQ==}
    hasBin: true
    dev: false

  /dlv@1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}

  /dnd-core@16.0.1:
    resolution: {integrity: sha512-HK294sl7tbw6F6IeuK16YSBUoorvHpY8RHO+9yFfaJyCDVb6n7PRcezrOEOa2SBCqiYpemh5Jx20ZcjKdFAVng==}
    dependencies:
      '@react-dnd/asap': 5.0.2
      '@react-dnd/invariant': 4.0.2
      redux: 4.2.1
    dev: false

  /dom-helpers@5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}
    dependencies:
      '@babel/runtime': 7.22.10
      csstype: 3.1.2
    dev: false

  /dom-serializer@2.0.0:
    resolution: {integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0
    dev: false

  /dom-walk@0.1.2:
    resolution: {integrity: sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w==}
    dev: false

  /domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}
    dev: false

  /domhandler@5.0.3:
    resolution: {integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: false

  /domino@2.1.6:
    resolution: {integrity: sha512-3VdM/SXBZX2omc9JF9nOPCtDaYQ67BGp5CoLpIQlO2KCAPETs8TcDHacF26jXadGbvUteZzRTeos2fhID5+ucQ==}
    dev: false

  /domutils@3.1.0:
    resolution: {integrity: sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==}
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3
    dev: false

  /dotenv@16.3.1:
    resolution: {integrity: sha512-IPzF4w4/Rd94bA9imS68tZBaYyBWSCE47V1RGuMrB94iyTOIEwRmVL2x/4An+6mETpLrKJ5hQkB8W4kFAadeIQ==}
    engines: {node: '>=12'}
    dev: false

  /downshift@7.6.2(react@18.2.0):
    resolution: {integrity: sha512-iOv+E1Hyt3JDdL9yYcOgW7nZ7GQ2Uz6YbggwXvKUSleetYhU2nXD482Rz6CzvM4lvI1At34BYruKAL4swRGxaA==}
    peerDependencies:
      react: '>=16.12.0'
    dependencies:
      '@babel/runtime': 7.22.10
      compute-scroll-into-view: 2.0.4
      prop-types: 15.8.1
      react: 18.2.0
      react-is: 17.0.2
      tslib: 2.6.1
    dev: false

  /duplexify@4.1.2:
    resolution: {integrity: sha512-fz3OjcNCHmRP12MJoZMPglx8m4rrFP8rovnk4vT8Fs+aonZoCwGg10dSsQsfP/E62eZcPTMSMP6686fu9Qlqtw==}
    requiresBuild: true
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 3.6.2
      stream-shift: 1.0.1
    dev: false
    optional: true

  /ecdsa-sig-formatter@1.0.11:
    resolution: {integrity: sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /electron-to-chromium@1.4.488:
    resolution: {integrity: sha512-Dv4sTjiW7t/UWGL+H8ZkgIjtUAVZDgb/PwGWvMsCT7jipzUV/u5skbLXPFKb6iV0tiddVi/bcS2/kUrczeWgIQ==}
    dev: false

  /electron-to-chromium@1.4.715:
    resolution: {integrity: sha512-XzWNH4ZSa9BwVUQSDorPWAUQ5WGuYz7zJUNpNif40zFCiCl20t8zgylmreNmn26h5kiyw2lg7RfTmeMBsDklqg==}
    dev: false

  /emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    dev: false

  /emojibase-data@6.2.0(emojibase@6.1.0):
    resolution: {integrity: sha512-SWKaXD2QeQs06IE7qfJftsI5924Dqzp+V9xaa5RzZIEWhmlrG6Jt2iKwfgOPHu+5S8MEtOI7GdpKsXj46chXOw==}
    peerDependencies:
      emojibase: '*'
    dependencies:
      emojibase: 6.1.0
    dev: false

  /emojibase-regex@5.1.3:
    resolution: {integrity: sha512-gT8T9LxLA8VJdI+8KQtyykB9qKzd7WuUL3M2yw6y9tplFeufOUANg3UKVaKUvkMcRNvZsSElWhxcJrx8WPE12g==}
    dev: false

  /emojibase-regex@6.0.1:
    resolution: {integrity: sha512-Mj1UT6IIk4j91yMFE0QetpUYcmsr5ZDkkOIMSGafhIgC086mBMaCh2Keaykx8YEllmV7hmx5zdANDzCYBYAVDw==}
    dev: false

  /emojibase@5.2.0:
    resolution: {integrity: sha512-5T02oTJaWpScAtYbukKVc8vQ1367MyfVtFHUMoOVZ9/r1kFcbYqjSktD56TICBAeyW9uc1t+7qQuXEtntM6p5A==}
    dev: false

  /emojibase@6.1.0:
    resolution: {integrity: sha512-1GkKJPXP6tVkYJHOBSJHoGOr/6uaDxZ9xJ6H7m6PfdGXTmQgbALHLWaVRY4Gi/qf5x/gT/NUXLPuSHYLqtLtrQ==}
    dev: false

  /emojis-list@3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}
    dev: false

  /encoding@0.1.13:
    resolution: {integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==}
    dependencies:
      iconv-lite: 0.6.3
    dev: false

  /end-of-stream@1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}
    requiresBuild: true
    dependencies:
      once: 1.4.0
    dev: false
    optional: true

  /enhanced-resolve@5.16.0:
    resolution: {integrity: sha512-O+QWCviPNSSLAD9Ucn8Awv+poAkqn3T1XY5/N7kR7rQO9yfSGWkYZDwpJ+iKF7B8rxaQKWngSqACpgzeapSyoA==}
    engines: {node: '>=10.13.0'}
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1
    dev: false

  /ent@2.2.0:
    resolution: {integrity: sha512-GHrMyVZQWvTIdDtpiEXdHZnFQKzeO09apj8Cbl4pKWy4i0Oprcq17usfDt5aO63swf0JOeMWjWQE/LzgSRuWpA==}
    requiresBuild: true
    dev: false
    optional: true

  /entities@2.1.0:
    resolution: {integrity: sha512-hCx1oky9PFrJ611mf0ifBLBRW8lUUVRlFolb5gWRfIELabBlbp9xZvrqZLZAs+NxFnbfQoeGd8wDkygjg7U85w==}
    requiresBuild: true
    dev: false
    optional: true

  /entities@3.0.1:
    resolution: {integrity: sha512-WiyBqoomrwMdFG1e0kqvASYfnlb0lp8M5o5Fw2OFq1hNZxxcNk8Ik0Xm7LxzBhuidnZB/UtBqVCgUz3kBOP51Q==}
    engines: {node: '>=0.12'}
    dev: false

  /entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}
    dev: false

  /error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1
    dev: false

  /es-abstract@1.22.1:
    resolution: {integrity: sha512-ioRRcXMO6OFyRpyzV3kE1IIBd4WG5/kltnzdxSCqoP8CMGs/Li+M1uF5o7lOkZVFjDs+NLesthnF66Pg/0q0Lw==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.0
      arraybuffer.prototype.slice: 1.0.1
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      es-set-tostringtag: 2.0.1
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.5
      get-intrinsic: 1.2.1
      get-symbol-description: 1.0.0
      globalthis: 1.0.3
      gopd: 1.0.1
      has: 1.0.3
      has-property-descriptors: 1.0.0
      has-proto: 1.0.1
      has-symbols: 1.0.3
      internal-slot: 1.0.5
      is-array-buffer: 3.0.2
      is-callable: 1.2.7
      is-negative-zero: 2.0.2
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.2
      is-string: 1.0.7
      is-typed-array: 1.1.12
      is-weakref: 1.0.2
      object-inspect: 1.12.3
      object-keys: 1.1.1
      object.assign: 4.1.4
      regexp.prototype.flags: 1.5.0
      safe-array-concat: 1.0.0
      safe-regex-test: 1.0.0
      string.prototype.trim: 1.2.7
      string.prototype.trimend: 1.0.6
      string.prototype.trimstart: 1.0.6
      typed-array-buffer: 1.0.0
      typed-array-byte-length: 1.0.0
      typed-array-byte-offset: 1.0.0
      typed-array-length: 1.0.4
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.11
    dev: false

  /es-module-lexer@1.4.2:
    resolution: {integrity: sha512-7nOqkomXZEaxUDJw21XZNtRk739QvrPSoZoRtbsEfcii00vdzZUh6zh1CQwHhrib8MdEtJfv5rJiGeb4KuV/vw==}
    dev: false

  /es-set-tostringtag@2.0.1:
    resolution: {integrity: sha512-g3OMbtlwY3QewlqAiMLI47KywjWZoEytKr8pf6iTC8uJq5bIAH52Z9pnQ8pVL6whrCto53JZDuUIsifGeLorTg==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.1
      has: 1.0.3
      has-tostringtag: 1.0.0
    dev: false

  /es-to-primitive@1.2.1:
    resolution: {integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4
    dev: false

  /es5-ext@0.10.62:
    resolution: {integrity: sha512-BHLqn0klhEpnOKSrzn/Xsz2UIW8j+cGmo9JLzr8BiUapV8hPL9+FliFqjwr9ngW7jWdnxv6eO+/LqyhJVqgrjA==}
    engines: {node: '>=0.10'}
    requiresBuild: true
    dependencies:
      es6-iterator: 2.0.3
      es6-symbol: 3.1.3
      next-tick: 1.1.0
    dev: false

  /es6-iterator@2.0.3:
    resolution: {integrity: sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g==}
    dependencies:
      d: 1.0.1
      es5-ext: 0.10.62
      es6-symbol: 3.1.3
    dev: false

  /es6-object-assign@1.1.0:
    resolution: {integrity: sha512-MEl9uirslVwqQU369iHNWZXsI8yaZYGg/D65aOgZkeyFJwHYSxilf7rQzXKI7DdDuBPrBXbfk3sl9hJhmd5AUw==}
    dev: false

  /es6-symbol@3.1.3:
    resolution: {integrity: sha512-NJ6Yn3FuDinBaBRWl/q5X/s4koRHBrgKAu+yGI6JCBeiu3qrcbJhwT2GeR/EXVfylRk8dpQVJoLEFhK+Mu31NA==}
    dependencies:
      d: 1.0.1
      ext: 1.7.0
    dev: false

  /escalade@3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}
    dev: false

  /escalade@3.1.2:
    resolution: {integrity: sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==}
    engines: {node: '>=6'}
    dev: false

  /escape-carriage@1.3.1:
    resolution: {integrity: sha512-GwBr6yViW3ttx1kb7/Oh+gKQ1/TrhYwxKqVmg5gS+BK+Qe2KrOa/Vh7w3HPBvgGf0LfcDGoY9I6NHKoA5Hozhw==}
    dev: false

  /escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}
    dev: false

  /escape-string-regexp@2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==}
    engines: {node: '>=8'}
    requiresBuild: true
    dev: false
    optional: true

  /escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}
    dev: false

  /escape-string-regexp@5.0.0:
    resolution: {integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==}
    engines: {node: '>=12'}
    dev: false

  /escodegen@1.14.3:
    resolution: {integrity: sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==}
    engines: {node: '>=4.0'}
    hasBin: true
    requiresBuild: true
    dependencies:
      esprima: 4.0.1
      estraverse: 4.3.0
      esutils: 2.0.3
      optionator: 0.8.3
    optionalDependencies:
      source-map: 0.6.1
    dev: false
    optional: true

  /eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: false

  /eslint-scope@7.1.1:
    resolution: {integrity: sha512-QKQM/UXpIiHcLqJ5AOyIW7XZmzjkzQXYE54n1++wb0u9V/abW3l9uQnxX8Z5Xd18xyKIMTUAyQ0k1e8pz6LUrw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0
    dev: false

  /eslint-visitor-keys@3.3.0:
    resolution: {integrity: sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: false

  /eslint-visitor-keys@3.4.2:
    resolution: {integrity: sha512-8drBzUEyZ2llkpCA67iYrgEssKDUu68V8ChqqOfFupIaG/LCVPUT+CoGJpT77zJprs4T/W7p07LP7zAIMuweVw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    requiresBuild: true
    dev: false
    optional: true

  /espree@9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    requiresBuild: true
    dependencies:
      acorn: 8.10.0
      acorn-jsx: 5.3.2(acorn@8.10.0)
      eslint-visitor-keys: 3.4.2
    dev: false
    optional: true

  /esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: false

  /estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}
    dev: false

  /estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}
    dev: false

  /estree-util-is-identifier-name@2.1.0:
    resolution: {integrity: sha512-bEN9VHRyXAUOjkKVQVvArFym08BTWB0aJPppZZr0UNyAqWsLaVfAqP7hbaTJjzHifmB5ebnR8Wm7r7yGN/HonQ==}
    dev: false

  /estree-util-visit@1.2.1:
    resolution: {integrity: sha512-xbgqcrkIVbIG+lI/gzbvd9SGTJL4zqJKBFttUl5pP27KhAjtMKbX/mQXJ7qgyXpMgVy/zvpm0xoQQaGL8OloOw==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      '@types/unist': 2.0.7
    dev: false

  /esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}
    requiresBuild: true
    dev: false

  /events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}
    dev: false

  /ext@1.7.0:
    resolution: {integrity: sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==}
    dependencies:
      type: 2.7.2
    dev: false

  /extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}
    dev: false

  /extract-domain@2.2.1:
    resolution: {integrity: sha512-lOq1adCJha0tFFBci4quxC4XLa6+Rs2WgAwTo9qbO9OsElvJmGgCvOzmHo/yg5CiqeP4+sHjkXYGkrCcIEprMg==}
    dev: false

  /fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}
    dev: false

  /fast-glob@3.3.1:
    resolution: {integrity: sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5

  /fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}
    dev: false

  /fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}
    dev: false

  /fast-text-encoding@1.0.6:
    resolution: {integrity: sha512-VhXlQgj9ioXCqGstD37E/HBeqEGV/qOD/kmbVG8h5xKBYvM1L3lR1Zn4555cQ8GkYbJa8aJSipLPndE1k6zK2w==}
    requiresBuild: true
    dev: false
    optional: true

  /fast-xml-parser@4.2.7:
    resolution: {integrity: sha512-J8r6BriSLO1uj2miOk1NW0YVm8AGOOu3Si2HQp/cSmo6EA4m3fcwu2WKjJ4RK9wMLBtg69y1kS8baDiQBR41Ig==}
    hasBin: true
    requiresBuild: true
    dependencies:
      strnum: 1.0.5
    dev: false
    optional: true

  /fastparse@1.1.2:
    resolution: {integrity: sha512-483XLLxTVIwWK3QTrMGRqUfUpoOs/0hbQrl2oz4J0pAcm3A3bu84wxTFqGqkJzewCLdME38xJLJAxBABfQT8sQ==}

  /fastq@1.15.0:
    resolution: {integrity: sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==}
    dependencies:
      reusify: 1.0.4

  /fault@2.0.1:
    resolution: {integrity: sha512-WtySTkS4OKev5JtpHXnib4Gxiurzh5NCGvWrFaZ34m6JehfTUhKZvn9njTfw48t6JumVQOmrKqpmGcdwxnhqBQ==}
    dependencies:
      format: 0.2.2
    dev: false

  /faye-websocket@0.11.4:
    resolution: {integrity: sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==}
    engines: {node: '>=0.8.0'}
    dependencies:
      websocket-driver: 0.7.4
    dev: false

  /fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1

  /find-root@1.1.0:
    resolution: {integrity: sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==}
    dev: false

  /find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0
    dev: false

  /firebase-admin@11.10.1(encoding@0.1.13):
    resolution: {integrity: sha512-atv1E6GbuvcvWaD3eHwrjeP5dAVs+EaHEJhu9CThMzPY6In8QYDiUR6tq5SwGl4SdA/GcAU0nhwWc/FSJsAzfQ==}
    engines: {node: '>=14'}
    dependencies:
      '@fastify/busboy': 1.2.1
      '@firebase/database-compat': 0.3.4
      '@firebase/database-types': 0.10.4
      '@types/node': 20.3.3
      jsonwebtoken: 9.0.1
      jwks-rsa: 3.0.1
      node-forge: 1.3.1
      uuid: 9.0.0
    optionalDependencies:
      '@google-cloud/firestore': 6.8.0(encoding@0.1.13)
      '@google-cloud/storage': 6.12.0(encoding@0.1.13)
    transitivePeerDependencies:
      - encoding
      - supports-color
    dev: false

  /firebase@9.23.0(encoding@0.1.13):
    resolution: {integrity: sha512-/4lUVY0lUvBDIaeY1q6dUYhS8Sd18Qb9CgWkPZICUo9IXpJNCEagfNZXBBFCkMTTN5L5gx2Hjr27y21a9NzUcA==}
    dependencies:
      '@firebase/analytics': 0.10.0(@firebase/app@0.9.13)
      '@firebase/analytics-compat': 0.2.6(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13)
      '@firebase/app': 0.9.13
      '@firebase/app-check': 0.8.0(@firebase/app@0.9.13)
      '@firebase/app-check-compat': 0.3.7(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13)
      '@firebase/app-compat': 0.2.13
      '@firebase/app-types': 0.9.0
      '@firebase/auth': 0.23.2(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/auth-compat': 0.4.2(@firebase/app-compat@0.2.13)(@firebase/app-types@0.9.0)(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/database': 0.14.4
      '@firebase/database-compat': 0.3.4
      '@firebase/firestore': 3.13.0(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/firestore-compat': 0.3.12(@firebase/app-compat@0.2.13)(@firebase/app-types@0.9.0)(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/functions': 0.10.0(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/functions-compat': 0.3.5(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/installations': 0.6.4(@firebase/app@0.9.13)
      '@firebase/installations-compat': 0.2.4(@firebase/app-compat@0.2.13)(@firebase/app-types@0.9.0)(@firebase/app@0.9.13)
      '@firebase/messaging': 0.12.4(@firebase/app@0.9.13)
      '@firebase/messaging-compat': 0.2.4(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13)
      '@firebase/performance': 0.6.4(@firebase/app@0.9.13)
      '@firebase/performance-compat': 0.2.4(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13)
      '@firebase/remote-config': 0.4.4(@firebase/app@0.9.13)
      '@firebase/remote-config-compat': 0.2.4(@firebase/app-compat@0.2.13)(@firebase/app@0.9.13)
      '@firebase/storage': 0.11.2(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/storage-compat': 0.3.2(@firebase/app-compat@0.2.13)(@firebase/app-types@0.9.0)(@firebase/app@0.9.13)(encoding@0.1.13)
      '@firebase/util': 1.9.3
    transitivePeerDependencies:
      - encoding
    dev: false

  /follow-redirects@1.15.2:
    resolution: {integrity: sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: false

  /for-each@0.3.3:
    resolution: {integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==}
    dependencies:
      is-callable: 1.2.7
    dev: false

  /form-data-encoder@1.7.2:
    resolution: {integrity: sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==}
    dev: false

  /form-data@3.0.1:
    resolution: {integrity: sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg==}
    engines: {node: '>= 6'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35
    dev: false

  /form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35
    dev: false

  /format@0.2.2:
    resolution: {integrity: sha512-wzsgA6WOq+09wrU1tsJ09udeR/YZRaeArL9e1wPbFg3GG2yDnC2ldKpxs4xunpFF9DgqCqOIra3bc1HWrJ37Ww==}
    engines: {node: '>=0.4.x'}
    dev: false

  /formdata-node@4.4.1:
    resolution: {integrity: sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==}
    engines: {node: '>= 12.20'}
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 4.0.0-beta.3
    dev: false

  /formik@2.4.3(react@18.2.0):
    resolution: {integrity: sha512-2Dy79Szw3zlXmZiokUdKsn+n1ow4G8hRrC/n92cOWHNTWXCRpQXlyvz6HcjW7aSQZrldytvDOavYjhfmDnUq8Q==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      deepmerge: 2.2.1
      hoist-non-react-statics: 3.3.2
      lodash: 4.17.21
      lodash-es: 4.17.21
      react: 18.2.0
      react-fast-compare: 2.0.4
      tiny-warning: 1.0.3
      tslib: 2.6.1
    dev: false

  /fraction.js@4.2.0:
    resolution: {integrity: sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA==}
    dev: false

  /fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  /fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    optional: true

  /function-bind@1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}

  /function.prototype.name@1.1.5:
    resolution: {integrity: sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      functions-have-names: 1.2.3
    dev: false

  /functional-red-black-tree@1.0.1:
    resolution: {integrity: sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==}
    requiresBuild: true
    dev: false
    optional: true

  /functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}
    dev: false

  /gaxios@5.1.3(encoding@0.1.13):
    resolution: {integrity: sha512-95hVgBRgEIRQQQHIbnxBXeHbW4TqFk4ZDJW7wmVtvYar72FdhRIo1UGOLS2eRAKCPEdPBWu+M7+A33D9CdX9rA==}
    engines: {node: '>=12'}
    requiresBuild: true
    dependencies:
      extend: 3.0.2
      https-proxy-agent: 5.0.1
      is-stream: 2.0.1
      node-fetch: 2.6.12(encoding@0.1.13)
    transitivePeerDependencies:
      - encoding
      - supports-color
    dev: false
    optional: true

  /gcp-metadata@5.3.0(encoding@0.1.13):
    resolution: {integrity: sha512-FNTkdNEnBdlqF2oatizolQqNANMrcqJt6AAYt99B3y1aLLC8Hc5IOBb+ZnnzllodEEf6xMBp6wRcBbc16fa65w==}
    engines: {node: '>=12'}
    requiresBuild: true
    dependencies:
      gaxios: 5.1.3(encoding@0.1.13)
      json-bigint: 1.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color
    dev: false
    optional: true

  /gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}
    dev: false

  /get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}
    dev: false

  /get-dom-document@0.1.3:
    resolution: {integrity: sha512-bZ0O00gSQgMo+wz7gU6kbbWCPh4dfDsL9ZOmVhA8TOXszl5GV56TpTuW1/Qq/QctgpjK56yyvB1vBO+wzz8Szw==}
    peerDependencies:
      jsdom: '*'
    peerDependenciesMeta:
      jsdom:
        optional: true
    dev: false

  /get-intrinsic@1.2.1:
    resolution: {integrity: sha512-2DcsyfABl+gVHEfCOaTrWgyt+tb6MSEGmKq+kI5HwLbIYgjgmMcV8KQ41uaKz1xxUcn9tJtgFbQUEVcEbd0FYw==}
    dependencies:
      function-bind: 1.1.1
      has: 1.0.3
      has-proto: 1.0.1
      has-symbols: 1.0.3
    dev: false

  /get-nonce@1.0.1:
    resolution: {integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==}
    engines: {node: '>=6'}
    dev: false

  /get-symbol-description@1.0.0:
    resolution: {integrity: sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
    dev: false

  /gitdiff-parser@0.3.1:
    resolution: {integrity: sha512-YQJnY8aew65id8okGxKCksH3efDCJ9HzV7M9rsvd65habf39Pkh4cgYJ27AaoDMqo1X98pgNJhNMrm/kpV7UVQ==}
    dev: false

  /glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3

  /glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3

  /glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}
    dev: false

  /glob@7.1.6:
    resolution: {integrity: sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  /glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    requiresBuild: true
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: false
    optional: true

  /glob@8.1.0:
    resolution: {integrity: sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ==}
    engines: {node: '>=12'}
    requiresBuild: true
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 5.1.6
      once: 1.4.0
    dev: false
    optional: true

  /globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}
    dev: false

  /globalthis@1.0.3:
    resolution: {integrity: sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-properties: 1.2.0
    dev: false

  /google-auth-library@8.9.0(encoding@0.1.13):
    resolution: {integrity: sha512-f7aQCJODJFmYWN6PeNKzgvy9LI2tYmXnzpNDHEjG5sDNPgGb2FXQyTBnXeSH+PAtpKESFD+LmHw3Ox3mN7e1Fg==}
    engines: {node: '>=12'}
    requiresBuild: true
    dependencies:
      arrify: 2.0.1
      base64-js: 1.5.1
      ecdsa-sig-formatter: 1.0.11
      fast-text-encoding: 1.0.6
      gaxios: 5.1.3(encoding@0.1.13)
      gcp-metadata: 5.3.0(encoding@0.1.13)
      gtoken: 6.1.2(encoding@0.1.13)
      jws: 4.0.0
      lru-cache: 6.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color
    dev: false
    optional: true

  /google-gax@3.6.1(encoding@0.1.13):
    resolution: {integrity: sha512-g/lcUjGcB6DSw2HxgEmCDOrI/CByOwqRvsuUvNalHUK2iPPPlmAIpbMbl62u0YufGMr8zgE3JL7th6dCb1Ry+w==}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    dependencies:
      '@grpc/grpc-js': 1.8.21
      '@grpc/proto-loader': 0.7.8
      '@types/long': 4.0.2
      '@types/rimraf': 3.0.2
      abort-controller: 3.0.0
      duplexify: 4.1.2
      fast-text-encoding: 1.0.6
      google-auth-library: 8.9.0(encoding@0.1.13)
      is-stream-ended: 0.1.4
      node-fetch: 2.6.12(encoding@0.1.13)
      object-hash: 3.0.0
      proto3-json-serializer: 1.1.1
      protobufjs: 7.2.4
      protobufjs-cli: 1.1.1(protobufjs@7.2.4)
      retry-request: 5.0.2
    transitivePeerDependencies:
      - encoding
      - supports-color
    dev: false
    optional: true

  /google-p12-pem@4.0.1:
    resolution: {integrity: sha512-WPkN4yGtz05WZ5EhtlxNDWPhC4JIic6G8ePitwUWy4l+XPVYec+a0j0Ts47PDtW59y3RwAhUd9/h9ZZ63px6RQ==}
    engines: {node: '>=12.0.0'}
    hasBin: true
    requiresBuild: true
    dependencies:
      node-forge: 1.3.1
    dev: false
    optional: true

  /gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}
    dependencies:
      get-intrinsic: 1.2.1
    dev: false

  /graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}
    dev: false

  /gtoken@6.1.2(encoding@0.1.13):
    resolution: {integrity: sha512-4ccGpzz7YAr7lxrT2neugmXQ3hP9ho2gcaityLVkiUecAiwiy60Ii8gRbZeOsXV19fYaRjgBSshs8kXw+NKCPQ==}
    engines: {node: '>=12.0.0'}
    requiresBuild: true
    dependencies:
      gaxios: 5.1.3(encoding@0.1.13)
      google-p12-pem: 4.0.1
      jws: 4.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color
    dev: false
    optional: true

  /has-bigints@1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}
    dev: false

  /has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}
    dev: false

  /has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}
    dev: false

  /has-property-descriptors@1.0.0:
    resolution: {integrity: sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==}
    dependencies:
      get-intrinsic: 1.2.1
    dev: false

  /has-proto@1.0.1:
    resolution: {integrity: sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==}
    engines: {node: '>= 0.4'}
    dev: false

  /has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}
    dev: false

  /has-tostringtag@1.0.0:
    resolution: {integrity: sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: false

  /has@1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1

  /hast-util-embedded@2.0.1:
    resolution: {integrity: sha512-QUdSOP1/o+/TxXtpPFXR2mUg2P+ySrmlX7QjwHZCXqMFyYk7YmcGSvqRW+4XgXAoHifdE1t2PwFaQK33TqVjSw==}
    dependencies:
      hast-util-is-element: 2.1.3
    dev: false

  /hast-util-from-dom@4.2.0:
    resolution: {integrity: sha512-t1RJW/OpJbCAJQeKi3Qrj1cAOLA0+av/iPFori112+0X7R3wng+jxLA+kXec8K4szqPRGI8vPxbbpEYvvpwaeQ==}
    dependencies:
      hastscript: 7.2.0
      web-namespaces: 2.0.1
    dev: false

  /hast-util-from-parse5@7.1.2:
    resolution: {integrity: sha512-Nz7FfPBuljzsN3tCQ4kCBKqdNhQE2l0Tn+X1ubgKBPRoiDIu1mL08Cfw4k7q71+Duyaw7DXDN+VTAp4Vh3oCOw==}
    dependencies:
      '@types/hast': 2.3.5
      '@types/unist': 2.0.7
      hastscript: 7.2.0
      property-information: 6.2.0
      vfile: 5.3.7
      vfile-location: 4.1.0
      web-namespaces: 2.0.1
    dev: false

  /hast-util-has-property@2.0.1:
    resolution: {integrity: sha512-X2+RwZIMTMKpXUzlotatPzWj8bspCymtXH3cfG3iQKV+wPF53Vgaqxi/eLqGck0wKq1kS9nvoB1wchbCPEL8sg==}
    dev: false

  /hast-util-is-body-ok-link@2.0.0:
    resolution: {integrity: sha512-S58hCexyKdD31vMsErvgLfflW6vYWo/ixRLPJTtkOvLld24vyI8vmYmkgLA5LG3la2ME7nm7dLGdm48gfLRBfw==}
    dependencies:
      '@types/hast': 2.3.5
      hast-util-has-property: 2.0.1
      hast-util-is-element: 2.1.3
    dev: false

  /hast-util-is-element@2.1.3:
    resolution: {integrity: sha512-O1bKah6mhgEq2WtVMk+Ta5K7pPMqsBBlmzysLdcwKVrqzZQ0CHqUPiIVspNhAG1rvxpvJjtGee17XfauZYKqVA==}
    dependencies:
      '@types/hast': 2.3.5
      '@types/unist': 2.0.7
    dev: false

  /hast-util-parse-selector@2.2.5:
    resolution: {integrity: sha512-7j6mrk/qqkSehsM92wQjdIgWM2/BW61u/53G6xmC8i1OmEdKLHbk419QKQUjz6LglWsfqoiHmyMRkP1BGjecNQ==}
    dev: false

  /hast-util-parse-selector@3.1.1:
    resolution: {integrity: sha512-jdlwBjEexy1oGz0aJ2f4GKMaVKkA9jwjr4MjAAI22E5fM/TXVZHuS5OpONtdeIkRKqAaryQ2E9xNQxijoThSZA==}
    dependencies:
      '@types/hast': 2.3.5
    dev: false

  /hast-util-phrasing@2.0.2:
    resolution: {integrity: sha512-yGkCfPkkfCyiLfK6KEl/orMDr/zgCnq/NaO9HfULx6/Zga5fso5eqQA5Ov/JZVqACygvw9shRYWgXNcG2ilo7w==}
    dependencies:
      '@types/hast': 2.3.5
      hast-util-embedded: 2.0.1
      hast-util-has-property: 2.0.1
      hast-util-is-body-ok-link: 2.0.0
      hast-util-is-element: 2.1.3
    dev: false

  /hast-util-raw@7.2.3:
    resolution: {integrity: sha512-RujVQfVsOrxzPOPSzZFiwofMArbQke6DJjnFfceiEbFh7S05CbPt0cYN+A5YeD3pso0JQk6O1aHBnx9+Pm2uqg==}
    dependencies:
      '@types/hast': 2.3.5
      '@types/parse5': 6.0.3
      hast-util-from-parse5: 7.1.2
      hast-util-to-parse5: 7.1.0
      html-void-elements: 2.0.1
      parse5: 6.0.1
      unist-util-position: 4.0.4
      unist-util-visit: 4.1.2
      vfile: 5.3.7
      web-namespaces: 2.0.1
      zwitch: 2.0.4
    dev: false

  /hast-util-raw@8.0.0:
    resolution: {integrity: sha512-bKbaUxMNLjZMMowgcrc4l3aQSPiMLiceZD+mp+AKF8Si0mtyR2DYVdxzS2XBxXYDeW/VvfZy40lNxHRiY6MMTg==}
    dependencies:
      '@types/hast': 2.3.5
      extend: 3.0.2
      hast-util-from-parse5: 7.1.2
      hast-util-to-parse5: 7.1.0
      html-void-elements: 2.0.1
      mdast-util-to-hast: 12.3.0
      parse5: 7.1.2
      unist-util-position: 4.0.4
      unist-util-visit: 4.1.2
      vfile: 5.3.7
      web-namespaces: 2.0.1
      zwitch: 2.0.4
    dev: false

  /hast-util-sanitize@4.1.0:
    resolution: {integrity: sha512-Hd9tU0ltknMGRDv+d6Ro/4XKzBqQnP/EZrpiTbpFYfXv/uOhWeKc+2uajcbEvAEH98VZd7eII2PiXm13RihnLw==}
    dependencies:
      '@types/hast': 2.3.5
    dev: false

  /hast-util-to-html@8.0.4:
    resolution: {integrity: sha512-4tpQTUOr9BMjtYyNlt0P50mH7xj0Ks2xpo8M943Vykljf99HW6EzulIoJP1N3eKOSScEHzyzi9dm7/cn0RfGwA==}
    dependencies:
      '@types/hast': 2.3.5
      '@types/unist': 2.0.7
      ccount: 2.0.1
      comma-separated-tokens: 2.0.3
      hast-util-raw: 7.2.3
      hast-util-whitespace: 2.0.1
      html-void-elements: 2.0.1
      property-information: 6.2.0
      space-separated-tokens: 2.0.2
      stringify-entities: 4.0.3
      zwitch: 2.0.4
    dev: false

  /hast-util-to-mdast@8.4.1:
    resolution: {integrity: sha512-tfmBLASuCgyhCzpkTXM5kU8xeuS5jkMZ17BYm2YftGT5wvgc7uHXTZ/X8WfNd6F5NV/IGmrLsuahZ+jXQir4zQ==}
    dependencies:
      '@types/extend': 3.0.1
      '@types/hast': 2.3.5
      '@types/mdast': 3.0.12
      '@types/unist': 2.0.7
      extend: 3.0.2
      hast-util-has-property: 2.0.1
      hast-util-is-element: 2.1.3
      hast-util-phrasing: 2.0.2
      hast-util-to-text: 3.1.2
      mdast-util-phrasing: 3.0.1
      mdast-util-to-string: 3.2.0
      rehype-minify-whitespace: 5.0.1
      trim-trailing-lines: 2.1.0
      unist-util-is: 5.2.1
      unist-util-visit: 4.1.2
    dev: false

  /hast-util-to-parse5@7.1.0:
    resolution: {integrity: sha512-YNRgAJkH2Jky5ySkIqFXTQiaqcAtJyVE+D5lkN6CdtOqrnkLfGYYrEcKuHOJZlp+MwjSwuD3fZuawI+sic/RBw==}
    dependencies:
      '@types/hast': 2.3.5
      comma-separated-tokens: 2.0.3
      property-information: 6.2.0
      space-separated-tokens: 2.0.2
      web-namespaces: 2.0.1
      zwitch: 2.0.4
    dev: false

  /hast-util-to-text@3.1.2:
    resolution: {integrity: sha512-tcllLfp23dJJ+ju5wCCZHVpzsQQ43+moJbqVX3jNWPB7z/KFC4FyZD6R7y94cHL6MQ33YtMZL8Z0aIXXI4XFTw==}
    dependencies:
      '@types/hast': 2.3.5
      '@types/unist': 2.0.7
      hast-util-is-element: 2.1.3
      unist-util-find-after: 4.0.1
    dev: false

  /hast-util-whitespace@2.0.1:
    resolution: {integrity: sha512-nAxA0v8+vXSBDt3AnRUNjyRIQ0rD+ntpbAp4LnPkumc5M9yUbSMa4XDU9Q6etY4f1Wp4bNgvc1yjiZtsTTrSng==}
    dev: false

  /hastscript@6.0.0:
    resolution: {integrity: sha512-nDM6bvd7lIqDUiYEiu5Sl/+6ReP0BMk/2f4U/Rooccxkj0P5nm+acM5PrGJ/t5I8qPGiqZSE6hVAwZEdZIvP4w==}
    dependencies:
      '@types/hast': 2.3.5
      comma-separated-tokens: 1.0.8
      hast-util-parse-selector: 2.2.5
      property-information: 5.6.0
      space-separated-tokens: 1.1.5
    dev: false

  /hastscript@7.2.0:
    resolution: {integrity: sha512-TtYPq24IldU8iKoJQqvZOuhi5CyCQRAbvDOX0x1eW6rsHSxa/1i2CCiptNTotGHJ3VoHRGmqiv6/D3q113ikkw==}
    dependencies:
      '@types/hast': 2.3.5
      comma-separated-tokens: 2.0.3
      hast-util-parse-selector: 3.1.1
      property-information: 6.2.0
      space-separated-tokens: 2.0.2
    dev: false

  /hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}
    dependencies:
      react-is: 16.13.1
    dev: false

  /html-void-elements@2.0.1:
    resolution: {integrity: sha512-0quDb7s97CfemeJAnW9wC0hw78MtW7NU3hqtCD75g2vFlDLt36llsYD7uB7SUzojLMP24N5IatXf7ylGXiGG9A==}
    dev: false

  /htmlparser2@8.0.2:
    resolution: {integrity: sha512-GYdjWKDkbRLkZ5geuHs5NY1puJ+PXwP7+fHPRz06Eirsb9ugf6d8kkXav6ADhcODhFFPMIXyxkxSuMf3D6NCFA==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.1.0
      entities: 4.5.0
    dev: false

  /http-parser-js@0.5.8:
    resolution: {integrity: sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==}
    dev: false

  /http-proxy-agent@5.0.0:
    resolution: {integrity: sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==}
    engines: {node: '>= 6'}
    requiresBuild: true
    dependencies:
      '@tootallnate/once': 2.0.0
      agent-base: 6.0.2
      debug: 4.3.4(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}
    requiresBuild: true
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.4(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /humanize-ms@1.2.1:
    resolution: {integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==}
    dependencies:
      ms: 2.1.3
    dev: false

  /hyphenate-style-name@1.0.4:
    resolution: {integrity: sha512-ygGZLjmXfPHj+ZWh6LwbC37l43MhfztxetbFCoYTM2VjkIUpeHgSNn7QIyVFj7YQ1Wl9Cbw5sholVJPzWvC2MQ==}
    dev: false

  /iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /idb-keyval@5.1.5:
    resolution: {integrity: sha512-J1utxYWQokYjy01LvDQ7WmiAtZCGUSkVi9EIBfUSyLOr/BesnMIxNGASTh9A1LzeISSjSqEPsfFdTss7EE7ofQ==}
    dependencies:
      safari-14-idb-fix: 1.0.6
    dev: false

  /idb@7.0.1:
    resolution: {integrity: sha512-UUxlE7vGWK5RfB/fDwEGgRf84DY/ieqNha6msMV99UsEMQhJ1RwbCd8AYBj3QMgnE3VZnfQvm4oKVCJTYlqIgg==}
    dev: false

  /idb@7.1.1:
    resolution: {integrity: sha512-gchesWBzyvGHRO9W8tzUWFDycow5gwjvFKfyV9FF32Y7F50yZMp7mP+T2mJIWFx49zicqyC4uefHM17o6xKIVQ==}
    dev: false

  /ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}
    dev: false

  /image-size@1.1.1:
    resolution: {integrity: sha512-541xKlUw6jr/6gGuk92F+mYM5zaFAc5ahphvkqvNe2bQ6gVBkd6bfrmVJ2t4KDAfikAYZyIqTnktX3i6/aQDrQ==}
    engines: {node: '>=16.x'}
    hasBin: true
    dependencies:
      queue: 6.0.2
    dev: false

  /immutable@4.3.2:
    resolution: {integrity: sha512-oGXzbEDem9OOpDWZu88jGiYCvIsLHMvGw+8OXlpsvTFvIQplQbjg1B1cvKg8f7Hoch6+NGjpPsH1Fr+Mc2D1aA==}
    dev: false

  /import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: false

  /inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  /inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  /internal-slot@1.0.5:
    resolution: {integrity: sha512-Y+R5hJrzs52QCG2laLn4udYVnxsfny9CpOhNhUvk/SSSVyF6T27FzRbF0sroPidSu3X8oEAkOn2K804mjpt6UQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.1
      has: 1.0.3
      side-channel: 1.0.4
    dev: false

  /intersection-observer@0.10.0:
    resolution: {integrity: sha512-fn4bQ0Xq8FTej09YC/jqKZwtijpvARlRp6wxL5WTA6yPe2YWSJ5RJh7Nm79rK2qB0wr6iDQzH60XGq5V/7u8YQ==}
    dev: false

  /invariant@2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /inversify@6.0.1:
    resolution: {integrity: sha512-B3ex30927698TJENHR++8FfEaJGqoWOgI6ZY5Ht/nLUsFCwHn6akbwtnUAPCgUepAnTpe2qHxhDNjoKLyz6rgQ==}
    dev: false

  /is-alphabetical@1.0.4:
    resolution: {integrity: sha512-DwzsA04LQ10FHTZuL0/grVDk4rFoVH1pjAToYwBrHSxcrBIGQuXrQMtD5U1b0U2XVgKZCTLLP8u2Qxqhy3l2Vg==}
    dev: false

  /is-alphabetical@2.0.1:
    resolution: {integrity: sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==}
    dev: false

  /is-alphanumerical@1.0.4:
    resolution: {integrity: sha512-UzoZUr+XfVz3t3v4KyGEniVL9BDRoQtY7tOyrRybkVNjDFWyo1yhXNGrrBTQxp3ib9BLAWs7k2YKBQsFRkZG9A==}
    dependencies:
      is-alphabetical: 1.0.4
      is-decimal: 1.0.4
    dev: false

  /is-alphanumerical@2.0.1:
    resolution: {integrity: sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==}
    dependencies:
      is-alphabetical: 2.0.1
      is-decimal: 2.0.1
    dev: false

  /is-arguments@1.1.1:
    resolution: {integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: false

  /is-array-buffer@3.0.2:
    resolution: {integrity: sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
      is-typed-array: 1.1.12
    dev: false

  /is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}
    dev: false

  /is-bigint@1.0.4:
    resolution: {integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==}
    dependencies:
      has-bigints: 1.0.2
    dev: false

  /is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0

  /is-boolean-object@1.1.2:
    resolution: {integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: false

  /is-buffer@1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}
    dev: false

  /is-buffer@2.0.5:
    resolution: {integrity: sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==}
    engines: {node: '>=4'}
    dev: false

  /is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}
    dev: false

  /is-core-module@2.13.0:
    resolution: {integrity: sha512-Z7dk6Qo8pOCp3l4tsX2C5ZVas4V+UxwQodwZhLopL91TX8UyyHEXafPcyoeeWuLrwzHcr3igO78wNLwHJHsMCQ==}
    dependencies:
      has: 1.0.3

  /is-date-object@1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: false

  /is-decimal@1.0.4:
    resolution: {integrity: sha512-RGdriMmQQvZ2aqaQq3awNA6dCGtKpiDFcOzrTWrDAT2MiWrKQVPmxLGHl7Y2nNu6led0kEyoX0enY0qXYsv9zw==}
    dev: false

  /is-decimal@2.0.1:
    resolution: {integrity: sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==}
    dev: false

  /is-extendable@1.0.1:
    resolution: {integrity: sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-object: 2.0.4
    dev: false

  /is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  /is-finite@1.0.2:
    resolution: {integrity: sha512-e+gU0KGrlbqjEcV80SAqg4g7PQYOm3/IrdwAJ+kPwHqGhLKhtuTJGGxGtrsc8RXlHt2A8Vlnv+79Vq2B1GQasg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      number-is-nan: 1.0.1
    dev: false

  /is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}
    dev: false

  /is-generator-function@1.0.10:
    resolution: {integrity: sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: false

  /is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1

  /is-hexadecimal@1.0.4:
    resolution: {integrity: sha512-gyPJuv83bHMpocVYoqof5VDiZveEoGoFL8m3BXNb2VW8Xs+rz9kqO8LOQ5DH6EsuvilT1ApazU0pyl+ytbPtlw==}
    dev: false

  /is-hexadecimal@2.0.1:
    resolution: {integrity: sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==}
    dev: false

  /is-integer@1.0.7:
    resolution: {integrity: sha512-RPQc/s9yBHSvpi+hs9dYiJ2cuFeU6x3TyyIp8O2H6SKEltIvJOzRj9ToyvcStDvPR/pS4rxgr1oBFajQjZ2Szg==}
    dependencies:
      is-finite: 1.0.2
    dev: false

  /is-nan@1.3.2:
    resolution: {integrity: sha512-E+zBKpQ2t6MEo1VsonYmluk9NxGrbzpeeLC2xIViuO2EjU2xsXsBPwTr3Ykv9l08UYEVEdWeRZNouaZqF6RN0w==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
    dev: false

  /is-negative-zero@2.0.2:
    resolution: {integrity: sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==}
    engines: {node: '>= 0.4'}
    dev: false

  /is-number-object@1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: false

  /is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  /is-plain-obj@4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==}
    engines: {node: '>=12'}
    dev: false

  /is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: false

  /is-regex@1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      has-tostringtag: 1.0.0
    dev: false

  /is-shared-array-buffer@1.0.2:
    resolution: {integrity: sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==}
    dependencies:
      call-bind: 1.0.2
    dev: false

  /is-stream-ended@0.1.4:
    resolution: {integrity: sha512-xj0XPvmr7bQFTvirqnFr50o0hQIh6ZItDqloxt5aJrR4NQsYeSsyFQERYGCAzfindAcnKjINnwEEgLx4IqVzQw==}
    requiresBuild: true
    dev: false
    optional: true

  /is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}
    requiresBuild: true
    dev: false
    optional: true

  /is-string@1.0.7:
    resolution: {integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0
    dev: false

  /is-symbol@1.0.4:
    resolution: {integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: false

  /is-typed-array@1.1.12:
    resolution: {integrity: sha512-Z14TF2JNG8Lss5/HMqt0//T9JeHXttXy5pH/DBU4vi98ozO2btxzq9MwYDZYnKwU8nRsz/+GVFVRDq3DkVuSPg==}
    engines: {node: '>= 0.4'}
    dependencies:
      which-typed-array: 1.1.11
    dev: false

  /is-weakref@1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}
    dependencies:
      call-bind: 1.0.2
    dev: false

  /isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}
    dev: false

  /isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /isomorphic.js@0.2.5:
    resolution: {integrity: sha512-PIeMbHqMt4DnUP3MA/Flc0HElYjMXArsw1qwJZcm9sqR8mq3l8NYizFMty0pWwE/tzIGH3EKK5+jes5mAr85yw==}
    dev: false

  /javascript-obfuscator@4.0.2:
    resolution: {integrity: sha512-MRBQREvjEwqEGdwNGG7yp0te5uZBFzrPZdxEs4rDYkPpHhhOQu+E0IhSXP7bO9dIUWXZU0Kkdohsuws525aVGQ==}
    engines: {node: ^12.22.0 || ^14.0.0 || ^16.0.0 || ^17.0.0 || >=18.0.0}
    hasBin: true
    requiresBuild: true
    dependencies:
      '@javascript-obfuscator/escodegen': 2.3.0
      '@javascript-obfuscator/estraverse': 5.4.0
      acorn: 8.8.2
      assert: 2.0.0
      chalk: 4.1.2
      chance: 1.1.9
      class-validator: 0.14.0
      commander: 10.0.0
      eslint-scope: 7.1.1
      eslint-visitor-keys: 3.3.0
      fast-deep-equal: 3.1.3
      inversify: 6.0.1
      js-string-escape: 1.0.1
      md5: 2.3.0
      mkdirp: 2.1.3
      multimatch: 5.0.0
      opencollective-postinstall: 2.0.3
      process: 0.11.10
      reflect-metadata: 0.1.13
      source-map-support: 0.5.21
      string-template: 1.0.0
      stringz: 2.1.0
      tslib: 2.5.0
    dev: false

  /jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': 20.3.3
      merge-stream: 2.0.0
      supports-color: 8.1.1
    dev: false

  /jiti@1.19.1:
    resolution: {integrity: sha512-oVhqoRDaBXf7sjkll95LHVS6Myyyb1zaunVwk4Z0+WPSW4gjS0pl01zYKHScTuyEhQsFxV5L4DR5r+YqSyqyyg==}
    hasBin: true

  /jose@4.14.4:
    resolution: {integrity: sha512-j8GhLiKmUAh+dsFXlX1aJCbt5KMibuKb+d7j1JaOJG6s2UjX1PQlW+OKB/sD4a/5ZYF4RcmYmLSndOoU3Lt/3g==}
    dev: false

  /js-string-escape@1.0.1:
    resolution: {integrity: sha512-Smw4xcfIQ5LVjAOuJCvN/zIodzA/BBSsluuoSykP+lUvScIi4U6RJLfwHet5cxFnCswUjISV8oAXaqaJDY3chg==}
    engines: {node: '>= 0.8'}
    dev: false

  /js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}
    dev: false

  /js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
    dev: false

  /js2xmlparser@4.0.2:
    resolution: {integrity: sha512-6n4D8gLlLf1n5mNLQPRfViYzu9RATblzPEtm1SthMX1Pjao0r9YI9nw7ZIfRxQMERS87mcswrg+r/OYrPRX6jA==}
    requiresBuild: true
    dependencies:
      xmlcreate: 2.0.4
    dev: false
    optional: true

  /jsdoc@4.0.2:
    resolution: {integrity: sha512-e8cIg2z62InH7azBBi3EsSEqrKx+nUtAS5bBcYTSpZFA+vhNPyhv8PTFZ0WsjOPDj04/dOLlm08EDcQJDqaGQg==}
    engines: {node: '>=12.0.0'}
    hasBin: true
    requiresBuild: true
    dependencies:
      '@babel/parser': 7.22.10
      '@jsdoc/salty': 0.2.5
      '@types/markdown-it': 12.2.3
      bluebird: 3.7.2
      catharsis: 0.9.0
      escape-string-regexp: 2.0.0
      js2xmlparser: 4.0.2
      klaw: 3.0.0
      markdown-it: 12.3.2
      markdown-it-anchor: 8.6.7(@types/markdown-it@12.2.3)(markdown-it@12.3.2)
      marked: 4.3.0
      mkdirp: 1.0.4
      requizzle: 0.2.4
      strip-json-comments: 3.1.1
      underscore: 1.13.6
    dev: false
    optional: true

  /jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /json-bigint@1.0.0:
    resolution: {integrity: sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==}
    requiresBuild: true
    dependencies:
      bignumber.js: 9.1.1
    dev: false
    optional: true

  /json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}
    dev: false

  /json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}
    dev: false

  /json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true
    dev: false

  /jsonwebtoken@9.0.1:
    resolution: {integrity: sha512-K8wx7eJ5TPvEjuiVSkv167EVboBDv9PZdDoF7BgeQnBLVvZWW9clr2PsQHVJDTKaEIH5JBIwHujGcHp7GgI2eg==}
    engines: {node: '>=12', npm: '>=6'}
    dependencies:
      jws: 3.2.2
      lodash: 4.17.21
      ms: 2.1.3
      semver: 7.5.4
    dev: false

  /jsx-dom-cjs@8.0.7:
    resolution: {integrity: sha512-dQWnuQ+bTm7o72ZlJU4glzeMX8KLxx5U+ZwmEAzVP1+roL7BSM0MrkWdHjdsuNgmxobZCJ+qgiot9EgbJPOoEg==}
    dependencies:
      csstype: 3.1.2
    dev: false

  /jwa@1.4.1:
    resolution: {integrity: sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==}
    dependencies:
      buffer-equal-constant-time: 1.0.1
      ecdsa-sig-formatter: 1.0.11
      safe-buffer: 5.2.1
    dev: false

  /jwa@2.0.0:
    resolution: {integrity: sha512-jrZ2Qx916EA+fq9cEAeCROWPTfCwi1IVHqT2tapuqLEVVDKFDENFw1oL+MwrTvH6msKxsd1YTDVw6uKEcsrLEA==}
    requiresBuild: true
    dependencies:
      buffer-equal-constant-time: 1.0.1
      ecdsa-sig-formatter: 1.0.11
      safe-buffer: 5.2.1
    dev: false
    optional: true

  /jwks-rsa@3.0.1:
    resolution: {integrity: sha512-UUOZ0CVReK1QVU3rbi9bC7N5/le8ziUj0A2ef1Q0M7OPD2KvjEYizptqIxGIo6fSLYDkqBrazILS18tYuRc8gw==}
    engines: {node: '>=14'}
    dependencies:
      '@types/express': 4.17.17
      '@types/jsonwebtoken': 9.0.2
      debug: 4.3.4(supports-color@5.5.0)
      jose: 4.14.4
      limiter: 1.1.5
      lru-memoizer: 2.2.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /jws@3.2.2:
    resolution: {integrity: sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==}
    dependencies:
      jwa: 1.4.1
      safe-buffer: 5.2.1
    dev: false

  /jws@4.0.0:
    resolution: {integrity: sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==}
    requiresBuild: true
    dependencies:
      jwa: 2.0.0
      safe-buffer: 5.2.1
    dev: false
    optional: true

  /klaw@3.0.0:
    resolution: {integrity: sha512-0Fo5oir+O9jnXu5EefYbVK+mHMBeEVEy2cmctR1O1NECcCkPRreJKrS6Qt/j3KC2C148Dfo9i3pCmCMsdqGr0g==}
    requiresBuild: true
    dependencies:
      graceful-fs: 4.2.11
    dev: false
    optional: true

  /kleur@4.1.5:
    resolution: {integrity: sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==}
    engines: {node: '>=6'}
    dev: false

  /levn@0.3.0:
    resolution: {integrity: sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2
    dev: false

  /lexical@0.11.3:
    resolution: {integrity: sha512-xsMKgx/Fa+QHg/nweemU04lCy7TnEr8LyeDtsKUC7fIDN9wH3GqbnQ0+e3Hbg4FmxlhDCiPPt0GcZAROq3R8uw==}
    dev: false

  /lib0@0.2.80:
    resolution: {integrity: sha512-1yVb13p19DrgbL7M/zQmRe/5tQrm37QlCHOssk+G8Q9qnZBh6Azfk876zhaxmKqyMnFGbQqBjH+CV0zkpr+TTw==}
    engines: {node: '>=16'}
    hasBin: true
    dependencies:
      isomorphic.js: 0.2.5
    dev: false

  /lib0@0.2.93:
    resolution: {integrity: sha512-M5IKsiFJYulS+8Eal8f+zAqf5ckm1vffW0fFDxfgxJ+uiVopvDdd3PxJmz0GsVi3YNO7QCFSq0nAsiDmNhLj9Q==}
    engines: {node: '>=16'}
    hasBin: true
    dependencies:
      isomorphic.js: 0.2.5
    dev: false

  /libphonenumber-js@1.10.39:
    resolution: {integrity: sha512-iPMM/NbSNIrdwbr94rAOos6krB7snhfzEptmk/DJUtTPs+P9gOhZ1YXVPcRgjpp3jJByclfm/Igvz45spfJK7g==}
    dev: false

  /lilconfig@2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    engines: {node: '>=10'}

  /limiter@1.1.5:
    resolution: {integrity: sha512-FWWMIEOxz3GwUI4Ts/IvgVy6LPvoMPgjMdQ185nN6psJyBJ4yOpzqm695/h5umdLJg2vW3GR5iG11MAkR2AzJA==}
    dev: false

  /lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  /linkify-it@3.0.3:
    resolution: {integrity: sha512-ynTsyrFSdE5oZ/O9GEf00kPngmOfVwazR5GKDq6EYfhlpFug3J2zybX56a2PRRpc9P+FuSoGNAwjlbDs9jJBPQ==}
    requiresBuild: true
    dependencies:
      uc.micro: 1.0.6
    dev: false
    optional: true

  /linkify-it@4.0.1:
    resolution: {integrity: sha512-C7bfi1UZmoj8+PQx22XyeXCuBlokoyWQL5pWSP+EI6nzRylyThouddufc2c1NDIcP9k5agmN9fLpA7VNJfIiqw==}
    dependencies:
      uc.micro: 1.0.6
    dev: false

  /linkify-it@5.0.0:
    resolution: {integrity: sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ==}
    dependencies:
      uc.micro: 2.1.0
    dev: false

  /linkifyjs@4.1.1:
    resolution: {integrity: sha512-zFN/CTVmbcVef+WaDXT63dNzzkfRBKT1j464NJQkV7iSgJU0sLBus9W0HBwnXK13/hf168pbrx/V/bjEHOXNHA==}
    dev: false

  /loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}
    dev: false

  /loader-utils@2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==}
    engines: {node: '>=8.9.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3
    dev: false

  /locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}
    dependencies:
      p-locate: 5.0.0
    dev: false

  /lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}
    dev: false

  /lodash.camelcase@4.3.0:
    resolution: {integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==}
    dev: false

  /lodash.castarray@4.4.0:
    resolution: {integrity: sha512-aVx8ztPv7/2ULbArGJ2Y42bG1mEQ5mGjpdvrbJcJFU3TbYybe+QlLS4pst9zV52ymy2in1KpFPiZnAOATxD4+Q==}
    dev: true

  /lodash.clonedeep@4.5.0:
    resolution: {integrity: sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==}
    dev: false

  /lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}
    dev: false

  /lodash.isplainobject@4.0.6:
    resolution: {integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==}
    dev: true

  /lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}
    dev: true

  /lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: false

  /long@4.0.0:
    resolution: {integrity: sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA==}
    dev: false

  /long@5.2.3:
    resolution: {integrity: sha512-lcHwpNoggQTObv5apGNCTdJrO69eHOZMi4BNC+rTLER8iHAqGrUVeLh/irVIM7zTw2bOXA8T6uNPeujwOLg/2Q==}
    requiresBuild: true
    dev: false

  /longest-streak@3.1.0:
    resolution: {integrity: sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==}
    dev: false

  /loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0
    dev: false

  /lru-cache@4.0.2:
    resolution: {integrity: sha512-uQw9OqphAGiZhkuPlpFGmdTU2tEuhxTourM/19qGJrxBPHAr/f8BT1a0i/lOclESnGatdJG/UCkP9kZB/Lh1iw==}
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2
    dev: false

  /lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}
    dependencies:
      yallist: 3.1.1
    dev: false

  /lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: false

  /lru-memoizer@2.2.0:
    resolution: {integrity: sha512-QfOZ6jNkxCcM/BkIPnFsqDhtrazLRsghi9mBwFAzol5GCvj4EkFT899Za3+QwikCg5sRX8JstioBDwOxEyzaNw==}
    dependencies:
      lodash.clonedeep: 4.5.0
      lru-cache: 4.0.2
    dev: false

  /lucide-react@0.263.1(react@18.2.0):
    resolution: {integrity: sha512-keqxAx97PlaEN89PXZ6ki1N8nRjGWtDa4021GFYLNj0RgruM5odbpl8GHTExj0hhPq3sF6Up0gnxt6TSHu+ovw==}
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /lz-string@1.5.0:
    resolution: {integrity: sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ==}
    hasBin: true
    dev: false

  /make-error@1.3.6:
    resolution: {integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==}
    dev: false

  /make-plural@6.2.2:
    resolution: {integrity: sha512-8iTuFioatnTTmb/YJjywkVIHLjcwkFD9Ms0JpxjEm9Mo8eQYkh1z+55dwv4yc1jQ8ftVBxWQbihvZL1DfzGGWA==}
    dev: false

  /markdown-it-anchor@8.6.7(@types/markdown-it@12.2.3)(markdown-it@12.3.2):
    resolution: {integrity: sha512-FlCHFwNnutLgVTflOYHPW2pPcl2AACqVzExlkGQNsi4CJgqOHN7YTgDd4LuhgN1BFO3TS0vLAruV1Td6dwWPJA==}
    requiresBuild: true
    peerDependencies:
      '@types/markdown-it': '*'
      markdown-it: '*'
    dependencies:
      '@types/markdown-it': 12.2.3
      markdown-it: 12.3.2
    dev: false
    optional: true

  /markdown-it@12.3.2:
    resolution: {integrity: sha512-TchMembfxfNVpHkbtriWltGWc+m3xszaRD0CZup7GFFhzIgQqxIfn3eGj1yZpfuflzPvfkt611B2Q/Bsk1YnGg==}
    hasBin: true
    requiresBuild: true
    dependencies:
      argparse: 2.0.1
      entities: 2.1.0
      linkify-it: 3.0.3
      mdurl: 1.0.1
      uc.micro: 1.0.6
    dev: false
    optional: true

  /markdown-it@13.0.1:
    resolution: {integrity: sha512-lTlxriVoy2criHP0JKRhO2VDG9c2ypWCsT237eDiLqi09rmbKoUetyGHq2uOIRoRS//kfoJckS0eUzzkDR+k2Q==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
      entities: 3.0.1
      linkify-it: 4.0.1
      mdurl: 1.0.1
      uc.micro: 1.0.6
    dev: false

  /markdown-it@14.1.0:
    resolution: {integrity: sha512-a54IwgWPaeBCAAsv13YgmALOF1elABB08FxO9i+r4VFk5Vl4pKokRPeX8u5TCgSsPi6ec1otfLjdOpVcgbpshg==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
      entities: 4.5.0
      linkify-it: 5.0.0
      mdurl: 2.0.0
      punycode.js: 2.3.1
      uc.micro: 2.1.0
    dev: false

  /markdown-table@3.0.3:
    resolution: {integrity: sha512-Z1NL3Tb1M9wH4XESsCDEksWoKTdlUafKc4pt0GRwjUyXaCFZ+dc3g2erqB6zm3szA2IUSi7VnPI+o/9jnxh9hw==}
    dev: false

  /marked@4.3.0:
    resolution: {integrity: sha512-PRsaiG84bK+AMvxziE/lCFss8juXjNaWzVbN5tXAm4XjeaS9NAHhop+PjQxz2A9h8Q4M/xGmzP8vqNwy6JeK0A==}
    engines: {node: '>= 12'}
    hasBin: true
    dev: false

  /match-sorter@6.3.1:
    resolution: {integrity: sha512-mxybbo3pPNuA+ZuCUhm5bwNkXrJTbsk5VWbR5wiwz/GC6LIiegBGn2w3O08UG/jdbYLinw51fSQ5xNU1U3MgBw==}
    dependencies:
      '@babel/runtime': 7.22.10
      remove-accents: 0.4.2
    dev: false

  /material-colors@1.2.6:
    resolution: {integrity: sha512-6qE4B9deFBIa9YSpOc9O0Sgc43zTeVYbgDT5veRKSlB2+ZuHNoVVxA1L/ckMUayV9Ay9y7Z/SZCLcGteW9i7bg==}
    dev: false

  /md5@2.3.0:
    resolution: {integrity: sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==}
    dependencies:
      charenc: 0.0.2
      crypt: 0.0.2
      is-buffer: 1.1.6
    dev: false

  /mdast-util-definitions@5.1.2:
    resolution: {integrity: sha512-8SVPMuHqlPME/z3gqVwWY4zVXn8lqKv/pAhC57FuJ40ImXyBpmO5ukh98zB2v7Blql2FiHjHv9LVztSIqjY+MA==}
    dependencies:
      '@types/mdast': 3.0.12
      '@types/unist': 2.0.7
      unist-util-visit: 4.1.2
    dev: false

  /mdast-util-directive@2.2.4:
    resolution: {integrity: sha512-sK3ojFP+jpj1n7Zo5ZKvoxP1MvLyzVG63+gm40Z/qI00avzdPCYxt7RBMgofwAva9gBjbDBWVRB/i+UD+fUCzQ==}
    dependencies:
      '@types/mdast': 3.0.12
      '@types/unist': 2.0.7
      mdast-util-from-markdown: 1.3.1
      mdast-util-to-markdown: 1.5.0
      parse-entities: 4.0.1
      stringify-entities: 4.0.3
      unist-util-visit-parents: 5.1.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-find-and-replace@2.2.2:
    resolution: {integrity: sha512-MTtdFRz/eMDHXzeK6W3dO7mXUlF82Gom4y0oOgvHhh/HXZAGvIQDUvQ0SuUx+j2tv44b8xTHOm8K/9OoRFnXKw==}
    dependencies:
      '@types/mdast': 3.0.12
      escape-string-regexp: 5.0.0
      unist-util-is: 5.2.1
      unist-util-visit-parents: 5.1.3
    dev: false

  /mdast-util-from-markdown@1.3.1:
    resolution: {integrity: sha512-4xTO/M8c82qBcnQc1tgpNtubGUW/Y1tBQ1B0i5CtSoelOLKFYlElIr3bvgREYYO5iRqbMY1YuqZng0GVOI8Qww==}
    dependencies:
      '@types/mdast': 3.0.12
      '@types/unist': 2.0.7
      decode-named-character-reference: 1.0.2
      mdast-util-to-string: 3.2.0
      micromark: 3.2.0
      micromark-util-decode-numeric-character-reference: 1.1.0
      micromark-util-decode-string: 1.1.0
      micromark-util-normalize-identifier: 1.1.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      unist-util-stringify-position: 3.0.3
      uvu: 0.5.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-frontmatter@1.0.1:
    resolution: {integrity: sha512-JjA2OjxRqAa8wEG8hloD0uTU0kdn8kbtOWpPP94NBkfAlbxn4S8gCGf/9DwFtEeGPXrDcNXdiDjVaRdUFqYokw==}
    dependencies:
      '@types/mdast': 3.0.12
      mdast-util-to-markdown: 1.5.0
      micromark-extension-frontmatter: 1.1.0
    dev: false

  /mdast-util-gfm-autolink-literal@1.0.3:
    resolution: {integrity: sha512-My8KJ57FYEy2W2LyNom4n3E7hKTuQk/0SES0u16tjA9Z3oFkF4RrC/hPAPgjlSpezsOvI8ObcXcElo92wn5IGA==}
    dependencies:
      '@types/mdast': 3.0.12
      ccount: 2.0.1
      mdast-util-find-and-replace: 2.2.2
      micromark-util-character: 1.2.0
    dev: false

  /mdast-util-gfm-footnote@1.0.2:
    resolution: {integrity: sha512-56D19KOGbE00uKVj3sgIykpwKL179QsVFwx/DCW0u/0+URsryacI4MAdNJl0dh+u2PSsD9FtxPFbHCzJ78qJFQ==}
    dependencies:
      '@types/mdast': 3.0.12
      mdast-util-to-markdown: 1.5.0
      micromark-util-normalize-identifier: 1.1.0
    dev: false

  /mdast-util-gfm-strikethrough@1.0.3:
    resolution: {integrity: sha512-DAPhYzTYrRcXdMjUtUjKvW9z/FNAMTdU0ORyMcbmkwYNbKocDpdk+PX1L1dQgOID/+vVs1uBQ7ElrBQfZ0cuiQ==}
    dependencies:
      '@types/mdast': 3.0.12
      mdast-util-to-markdown: 1.5.0
    dev: false

  /mdast-util-gfm-table@1.0.7:
    resolution: {integrity: sha512-jjcpmNnQvrmN5Vx7y7lEc2iIOEytYv7rTvu+MeyAsSHTASGCCRA79Igg2uKssgOs1i1po8s3plW0sTu1wkkLGg==}
    dependencies:
      '@types/mdast': 3.0.12
      markdown-table: 3.0.3
      mdast-util-from-markdown: 1.3.1
      mdast-util-to-markdown: 1.5.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-gfm-task-list-item@1.0.2:
    resolution: {integrity: sha512-PFTA1gzfp1B1UaiJVyhJZA1rm0+Tzn690frc/L8vNX1Jop4STZgOE6bxUhnzdVSB+vm2GU1tIsuQcA9bxTQpMQ==}
    dependencies:
      '@types/mdast': 3.0.12
      mdast-util-to-markdown: 1.5.0
    dev: false

  /mdast-util-gfm@2.0.2:
    resolution: {integrity: sha512-qvZ608nBppZ4icQlhQQIAdc6S3Ffj9RGmzwUKUWuEICFnd1LVkN3EktF7ZHAgfcEdvZB5owU9tQgt99e2TlLjg==}
    dependencies:
      mdast-util-from-markdown: 1.3.1
      mdast-util-gfm-autolink-literal: 1.0.3
      mdast-util-gfm-footnote: 1.0.2
      mdast-util-gfm-strikethrough: 1.0.3
      mdast-util-gfm-table: 1.0.7
      mdast-util-gfm-task-list-item: 1.0.2
      mdast-util-to-markdown: 1.5.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-mdx-expression@1.3.2:
    resolution: {integrity: sha512-xIPmR5ReJDu/DHH1OoIT1HkuybIfRGYRywC+gJtI7qHjCJp/M9jrmBEJW22O8lskDWm562BX2W8TiAwRTb0rKA==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      '@types/hast': 2.3.5
      '@types/mdast': 3.0.12
      mdast-util-from-markdown: 1.3.1
      mdast-util-to-markdown: 1.5.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-mdx-jsx@2.1.4:
    resolution: {integrity: sha512-DtMn9CmVhVzZx3f+optVDF8yFgQVt7FghCRNdlIaS3X5Bnym3hZwPbg/XW86vdpKjlc1PVj26SpnLGeJBXD3JA==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      '@types/hast': 2.3.5
      '@types/mdast': 3.0.12
      '@types/unist': 2.0.7
      ccount: 2.0.1
      mdast-util-from-markdown: 1.3.1
      mdast-util-to-markdown: 1.5.0
      parse-entities: 4.0.1
      stringify-entities: 4.0.3
      unist-util-remove-position: 4.0.2
      unist-util-stringify-position: 3.0.3
      vfile-message: 3.1.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-mdx@2.0.1:
    resolution: {integrity: sha512-38w5y+r8nyKlGvNjSEqWrhG0w5PmnRA+wnBvm+ulYCct7nsGYhFVb0lljS9bQav4psDAS1eGkP2LMVcZBi/aqw==}
    dependencies:
      mdast-util-from-markdown: 1.3.1
      mdast-util-mdx-expression: 1.3.2
      mdast-util-mdx-jsx: 2.1.4
      mdast-util-mdxjs-esm: 1.3.1
      mdast-util-to-markdown: 1.5.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-mdxjs-esm@1.3.1:
    resolution: {integrity: sha512-SXqglS0HrEvSdUEfoXFtcg7DRl7S2cwOXc7jkuusG472Mmjag34DUDeOJUZtl+BVnyeO1frIgVpHlNRWc2gk/w==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      '@types/hast': 2.3.5
      '@types/mdast': 3.0.12
      mdast-util-from-markdown: 1.3.1
      mdast-util-to-markdown: 1.5.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-phrasing@3.0.1:
    resolution: {integrity: sha512-WmI1gTXUBJo4/ZmSk79Wcb2HcjPJBzM1nlI/OUWA8yk2X9ik3ffNbBGsU+09BFmXaL1IBb9fiuvq6/KMiNycSg==}
    dependencies:
      '@types/mdast': 3.0.12
      unist-util-is: 5.2.1
    dev: false

  /mdast-util-to-hast@12.3.0:
    resolution: {integrity: sha512-pits93r8PhnIoU4Vy9bjW39M2jJ6/tdHyja9rrot9uujkN7UTU9SDnE6WNJz/IGyQk3XHX6yNNtrBH6cQzm8Hw==}
    dependencies:
      '@types/hast': 2.3.5
      '@types/mdast': 3.0.12
      mdast-util-definitions: 5.1.2
      micromark-util-sanitize-uri: 1.2.0
      trim-lines: 3.0.1
      unist-util-generated: 2.0.1
      unist-util-position: 4.0.4
      unist-util-visit: 4.1.2
    dev: false

  /mdast-util-to-markdown@1.5.0:
    resolution: {integrity: sha512-bbv7TPv/WC49thZPg3jXuqzuvI45IL2EVAr/KxF0BSdHsU0ceFHOmwQn6evxAh1GaoK/6GQ1wp4R4oW2+LFL/A==}
    dependencies:
      '@types/mdast': 3.0.12
      '@types/unist': 2.0.7
      longest-streak: 3.1.0
      mdast-util-phrasing: 3.0.1
      mdast-util-to-string: 3.2.0
      micromark-util-decode-string: 1.1.0
      unist-util-visit: 4.1.2
      zwitch: 2.0.4
    dev: false

  /mdast-util-to-string@3.2.0:
    resolution: {integrity: sha512-V4Zn/ncyN1QNSqSBxTrMOLpjr+IKdHl2v3KVLoWmDPscP4r9GcCi71gjgvUV1SFSKh92AjAG4peFuBl2/YgCJg==}
    dependencies:
      '@types/mdast': 3.0.12
    dev: false

  /mdurl@1.0.1:
    resolution: {integrity: sha512-/sKlQJCBYVY9Ers9hqzKou4H6V5UWc/M59TH2dvkt+84itfnq7uFOMLpOiOS4ujvHP4etln18fmIxA5R5fll0g==}
    dev: false

  /mdurl@2.0.0:
    resolution: {integrity: sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w==}
    dev: false

  /merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: false

  /merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  /micromark-core-commonmark@1.1.0:
    resolution: {integrity: sha512-BgHO1aRbolh2hcrzL2d1La37V0Aoz73ymF8rAcKnohLy93titmv62E0gP8Hrx9PKcKrqCZ1BbLGbP3bEhoXYlw==}
    dependencies:
      decode-named-character-reference: 1.0.2
      micromark-factory-destination: 1.1.0
      micromark-factory-label: 1.1.0
      micromark-factory-space: 1.1.0
      micromark-factory-title: 1.1.0
      micromark-factory-whitespace: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-chunked: 1.1.0
      micromark-util-classify-character: 1.1.0
      micromark-util-html-tag-name: 1.2.0
      micromark-util-normalize-identifier: 1.1.0
      micromark-util-resolve-all: 1.1.0
      micromark-util-subtokenize: 1.1.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
    dev: false

  /micromark-extension-directive@2.2.0:
    resolution: {integrity: sha512-LWc2mGlJlPEcESz4IHNJR/tpJfWJEEFHGM+6vgCZGXkKMXc/y8rCKB07x5ZNnafIFe0/sjt6DIIihk78/Egj5Q==}
    dependencies:
      micromark-factory-space: 1.1.0
      micromark-factory-whitespace: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      parse-entities: 4.0.1
      uvu: 0.5.6
    dev: false

  /micromark-extension-frontmatter@1.1.0:
    resolution: {integrity: sha512-0nLelmvXR5aZ+F2IL6/Ed4cDnHLpL/VD/EELKuclsTWHrLI8UgxGHEmeoumeX2FXiM6z2WrBIOEcbKUZR8RYNg==}
    dependencies:
      fault: 2.0.1
      micromark-util-character: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-extension-gfm-autolink-literal@1.0.5:
    resolution: {integrity: sha512-z3wJSLrDf8kRDOh2qBtoTRD53vJ+CWIyo7uyZuxf/JAbNJjiHsOpG1y5wxk8drtv3ETAHutCu6N3thkOOgueWg==}
    dependencies:
      micromark-util-character: 1.2.0
      micromark-util-sanitize-uri: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-extension-gfm-footnote@1.1.2:
    resolution: {integrity: sha512-Yxn7z7SxgyGWRNa4wzf8AhYYWNrwl5q1Z8ii+CSTTIqVkmGZF1CElX2JI8g5yGoM3GAman9/PVCUFUSJ0kB/8Q==}
    dependencies:
      micromark-core-commonmark: 1.1.0
      micromark-factory-space: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-normalize-identifier: 1.1.0
      micromark-util-sanitize-uri: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
    dev: false

  /micromark-extension-gfm-strikethrough@1.0.7:
    resolution: {integrity: sha512-sX0FawVE1o3abGk3vRjOH50L5TTLr3b5XMqnP9YDRb34M0v5OoZhG+OHFz1OffZ9dlwgpTBKaT4XW/AsUVnSDw==}
    dependencies:
      micromark-util-chunked: 1.1.0
      micromark-util-classify-character: 1.1.0
      micromark-util-resolve-all: 1.1.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
    dev: false

  /micromark-extension-gfm-table@1.0.7:
    resolution: {integrity: sha512-3ZORTHtcSnMQEKtAOsBQ9/oHp9096pI/UvdPtN7ehKvrmZZ2+bbWhi0ln+I9drmwXMt5boocn6OlwQzNXeVeqw==}
    dependencies:
      micromark-factory-space: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
    dev: false

  /micromark-extension-gfm-tagfilter@1.0.2:
    resolution: {integrity: sha512-5XWB9GbAUSHTn8VPU8/1DBXMuKYT5uOgEjJb8gN3mW0PNW5OPHpSdojoqf+iq1xo7vWzw/P8bAHY0n6ijpXF7g==}
    dependencies:
      micromark-util-types: 1.1.0
    dev: false

  /micromark-extension-gfm-task-list-item@1.0.5:
    resolution: {integrity: sha512-RMFXl2uQ0pNQy6Lun2YBYT9g9INXtWJULgbt01D/x8/6yJ2qpKyzdZD3pi6UIkzF++Da49xAelVKUeUMqd5eIQ==}
    dependencies:
      micromark-factory-space: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
    dev: false

  /micromark-extension-gfm@2.0.3:
    resolution: {integrity: sha512-vb9OoHqrhCmbRidQv/2+Bc6pkP0FrtlhurxZofvOEy5o8RtuuvTq+RQ1Vw5ZDNrVraQZu3HixESqbG+0iKk/MQ==}
    dependencies:
      micromark-extension-gfm-autolink-literal: 1.0.5
      micromark-extension-gfm-footnote: 1.1.2
      micromark-extension-gfm-strikethrough: 1.0.7
      micromark-extension-gfm-table: 1.0.7
      micromark-extension-gfm-tagfilter: 1.0.2
      micromark-extension-gfm-task-list-item: 1.0.5
      micromark-util-combine-extensions: 1.1.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-extension-mdx-expression@1.0.8:
    resolution: {integrity: sha512-zZpeQtc5wfWKdzDsHRBY003H2Smg+PUi2REhqgIhdzAa5xonhP03FcXxqFSerFiNUr5AWmHpaNPQTBVOS4lrXw==}
    dependencies:
      '@types/estree': 1.0.1
      micromark-factory-mdx-expression: 1.0.9
      micromark-factory-space: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-events-to-acorn: 1.2.3
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
    dev: false

  /micromark-extension-mdx-jsx@1.0.5:
    resolution: {integrity: sha512-gPH+9ZdmDflbu19Xkb8+gheqEDqkSpdCEubQyxuz/Hn8DOXiXvrXeikOoBA71+e8Pfi0/UYmU3wW3H58kr7akA==}
    dependencies:
      '@types/acorn': 4.0.6
      '@types/estree': 1.0.1
      estree-util-is-identifier-name: 2.1.0
      micromark-factory-mdx-expression: 1.0.9
      micromark-factory-space: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
      vfile-message: 3.1.4
    dev: false

  /micromark-extension-mdx-md@1.0.1:
    resolution: {integrity: sha512-7MSuj2S7xjOQXAjjkbjBsHkMtb+mDGVW6uI2dBL9snOBCbZmoNgDAeZ0nSn9j3T42UE/g2xVNMn18PJxZvkBEA==}
    dependencies:
      micromark-util-types: 1.1.0
    dev: false

  /micromark-extension-mdxjs-esm@1.0.5:
    resolution: {integrity: sha512-xNRBw4aoURcyz/S69B19WnZAkWJMxHMT5hE36GtDAyhoyn/8TuAeqjFJQlwk+MKQsUD7b3l7kFX+vlfVWgcX1w==}
    dependencies:
      '@types/estree': 1.0.1
      micromark-core-commonmark: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-events-to-acorn: 1.2.3
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      unist-util-position-from-estree: 1.1.2
      uvu: 0.5.6
      vfile-message: 3.1.4
    dev: false

  /micromark-extension-mdxjs@1.0.1:
    resolution: {integrity: sha512-7YA7hF6i5eKOfFUzZ+0z6avRG52GpWR8DL+kN47y3f2KhxbBZMhmxe7auOeaTBrW2DenbbZTf1ea9tA2hDpC2Q==}
    dependencies:
      acorn: 8.10.0
      acorn-jsx: 5.3.2(acorn@8.10.0)
      micromark-extension-mdx-expression: 1.0.8
      micromark-extension-mdx-jsx: 1.0.5
      micromark-extension-mdx-md: 1.0.1
      micromark-extension-mdxjs-esm: 1.0.5
      micromark-util-combine-extensions: 1.1.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-factory-destination@1.1.0:
    resolution: {integrity: sha512-XaNDROBgx9SgSChd69pjiGKbV+nfHGDPVYFs5dOoDd7ZnMAE+Cuu91BCpsY8RT2NP9vo/B8pds2VQNCLiu0zhg==}
    dependencies:
      micromark-util-character: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-factory-label@1.1.0:
    resolution: {integrity: sha512-OLtyez4vZo/1NjxGhcpDSbHQ+m0IIGnT8BoPamh+7jVlzLJBH98zzuCoUeMxvM6WsNeh8wx8cKvqLiPHEACn0w==}
    dependencies:
      micromark-util-character: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
    dev: false

  /micromark-factory-mdx-expression@1.0.9:
    resolution: {integrity: sha512-jGIWzSmNfdnkJq05c7b0+Wv0Kfz3NJ3N4cBjnbO4zjXIlxJr+f8lk+5ZmwFvqdAbUy2q6B5rCY//g0QAAaXDWA==}
    dependencies:
      '@types/estree': 1.0.1
      micromark-util-character: 1.2.0
      micromark-util-events-to-acorn: 1.2.3
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      unist-util-position-from-estree: 1.1.2
      uvu: 0.5.6
      vfile-message: 3.1.4
    dev: false

  /micromark-factory-space@1.1.0:
    resolution: {integrity: sha512-cRzEj7c0OL4Mw2v6nwzttyOZe8XY/Z8G0rzmWQZTBi/jjwyw/U4uqKtUORXQrR5bAZZnbTI/feRV/R7hc4jQYQ==}
    dependencies:
      micromark-util-character: 1.2.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-factory-title@1.1.0:
    resolution: {integrity: sha512-J7n9R3vMmgjDOCY8NPw55jiyaQnH5kBdV2/UXCtZIpnHH3P6nHUKaH7XXEYuWwx/xUJcawa8plLBEjMPU24HzQ==}
    dependencies:
      micromark-factory-space: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-factory-whitespace@1.1.0:
    resolution: {integrity: sha512-v2WlmiymVSp5oMg+1Q0N1Lxmt6pMhIHD457whWM7/GUlEks1hI9xj5w3zbc4uuMKXGisksZk8DzP2UyGbGqNsQ==}
    dependencies:
      micromark-factory-space: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-util-character@1.2.0:
    resolution: {integrity: sha512-lXraTwcX3yH/vMDaFWCQJP1uIszLVebzUa3ZHdrgxr7KEU/9mL4mVgCpGbyhvNLNlauROiNUq7WN5u7ndbY6xg==}
    dependencies:
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-util-chunked@1.1.0:
    resolution: {integrity: sha512-Ye01HXpkZPNcV6FiyoW2fGZDUw4Yc7vT0E9Sad83+bEDiCJ1uXu0S3mr8WLpsz3HaG3x2q0HM6CTuPdcZcluFQ==}
    dependencies:
      micromark-util-symbol: 1.1.0
    dev: false

  /micromark-util-classify-character@1.1.0:
    resolution: {integrity: sha512-SL0wLxtKSnklKSUplok1WQFoGhUdWYKggKUiqhX+Swala+BtptGCu5iPRc+xvzJ4PXE/hwM3FNXsfEVgoZsWbw==}
    dependencies:
      micromark-util-character: 1.2.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-util-combine-extensions@1.1.0:
    resolution: {integrity: sha512-Q20sp4mfNf9yEqDL50WwuWZHUrCO4fEyeDCnMGmG5Pr0Cz15Uo7KBs6jq+dq0EgX4DPwwrh9m0X+zPV1ypFvUA==}
    dependencies:
      micromark-util-chunked: 1.1.0
      micromark-util-types: 1.1.0
    dev: false

  /micromark-util-decode-numeric-character-reference@1.1.0:
    resolution: {integrity: sha512-m9V0ExGv0jB1OT21mrWcuf4QhP46pH1KkfWy9ZEezqHKAxkj4mPCy3nIH1rkbdMlChLHX531eOrymlwyZIf2iw==}
    dependencies:
      micromark-util-symbol: 1.1.0
    dev: false

  /micromark-util-decode-string@1.1.0:
    resolution: {integrity: sha512-YphLGCK8gM1tG1bd54azwyrQRjCFcmgj2S2GoJDNnh4vYtnL38JS8M4gpxzOPNyHdNEpheyWXCTnnTDY3N+NVQ==}
    dependencies:
      decode-named-character-reference: 1.0.2
      micromark-util-character: 1.2.0
      micromark-util-decode-numeric-character-reference: 1.1.0
      micromark-util-symbol: 1.1.0
    dev: false

  /micromark-util-encode@1.1.0:
    resolution: {integrity: sha512-EuEzTWSTAj9PA5GOAs992GzNh2dGQO52UvAbtSOMvXTxv3Criqb6IOzJUBCmEqrrXSblJIJBbFFv6zPxpreiJw==}
    dev: false

  /micromark-util-events-to-acorn@1.2.3:
    resolution: {integrity: sha512-ij4X7Wuc4fED6UoLWkmo0xJQhsktfNh1J0m8g4PbIMPlx+ek/4YdW5mvbye8z/aZvAPUoxgXHrwVlXAPKMRp1w==}
    dependencies:
      '@types/acorn': 4.0.6
      '@types/estree': 1.0.1
      '@types/unist': 2.0.7
      estree-util-visit: 1.2.1
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
      vfile-message: 3.1.4
    dev: false

  /micromark-util-html-tag-name@1.2.0:
    resolution: {integrity: sha512-VTQzcuQgFUD7yYztuQFKXT49KghjtETQ+Wv/zUjGSGBioZnkA4P1XXZPT1FHeJA6RwRXSF47yvJ1tsJdoxwO+Q==}
    dev: false

  /micromark-util-normalize-identifier@1.1.0:
    resolution: {integrity: sha512-N+w5vhqrBihhjdpM8+5Xsxy71QWqGn7HYNUvch71iV2PM7+E3uWGox1Qp90loa1ephtCxG2ftRV/Conitc6P2Q==}
    dependencies:
      micromark-util-symbol: 1.1.0
    dev: false

  /micromark-util-resolve-all@1.1.0:
    resolution: {integrity: sha512-b/G6BTMSg+bX+xVCshPTPyAu2tmA0E4X98NSR7eIbeC6ycCqCeE7wjfDIgzEbkzdEVJXRtOG4FbEm/uGbCRouA==}
    dependencies:
      micromark-util-types: 1.1.0
    dev: false

  /micromark-util-sanitize-uri@1.2.0:
    resolution: {integrity: sha512-QO4GXv0XZfWey4pYFndLUKEAktKkG5kZTdUNaTAkzbuJxn2tNBOr+QtxR2XpWaMhbImT2dPzyLrPXLlPhph34A==}
    dependencies:
      micromark-util-character: 1.2.0
      micromark-util-encode: 1.1.0
      micromark-util-symbol: 1.1.0
    dev: false

  /micromark-util-subtokenize@1.1.0:
    resolution: {integrity: sha512-kUQHyzRoxvZO2PuLzMt2P/dwVsTiivCK8icYTeR+3WgbuPqfHgPPy7nFKbeqRivBvn/3N3GBiNC+JRTMSxEC7A==}
    dependencies:
      micromark-util-chunked: 1.1.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
    dev: false

  /micromark-util-symbol@1.1.0:
    resolution: {integrity: sha512-uEjpEYY6KMs1g7QfJ2eX1SQEV+ZT4rUD3UcF6l57acZvLNK7PBZL+ty82Z1qhK1/yXIY4bdx04FKMgR0g4IAag==}
    dev: false

  /micromark-util-types@1.1.0:
    resolution: {integrity: sha512-ukRBgie8TIAcacscVHSiddHjO4k/q3pnedmzMQ4iwDcK0FtFCohKOlFbaOL/mPgfnPsL3C1ZyxJa4sbWrBl3jg==}
    dev: false

  /micromark@3.2.0:
    resolution: {integrity: sha512-uD66tJj54JLYq0De10AhWycZWGQNUvDI55xPgk2sQM5kn1JYlhbCMTtEeT27+vAhW2FBQxLlOmS3pmA7/2z4aA==}
    dependencies:
      '@types/debug': 4.1.8
      debug: 4.3.4(supports-color@5.5.0)
      decode-named-character-reference: 1.0.2
      micromark-core-commonmark: 1.1.0
      micromark-factory-space: 1.1.0
      micromark-util-character: 1.2.0
      micromark-util-chunked: 1.1.0
      micromark-util-combine-extensions: 1.1.0
      micromark-util-decode-numeric-character-reference: 1.1.0
      micromark-util-encode: 1.1.0
      micromark-util-normalize-identifier: 1.1.0
      micromark-util-resolve-all: 1.1.0
      micromark-util-sanitize-uri: 1.2.0
      micromark-util-subtokenize: 1.1.0
      micromark-util-symbol: 1.1.0
      micromark-util-types: 1.1.0
      uvu: 0.5.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1

  /mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}
    dev: false

  /mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: false

  /mime@3.0.0:
    resolution: {integrity: sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==}
    engines: {node: '>=10.0.0'}
    hasBin: true
    requiresBuild: true
    dev: false
    optional: true

  /min-document@2.19.0:
    resolution: {integrity: sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ==}
    dependencies:
      dom-walk: 0.1.2
    dev: false

  /minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11

  /minimatch@5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==}
    engines: {node: '>=10'}
    requiresBuild: true
    dependencies:
      brace-expansion: 2.0.1
    dev: false
    optional: true

  /minimatch@9.0.3:
    resolution: {integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.1
    dev: false

  /minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}
    requiresBuild: true
    dev: false
    optional: true

  /mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true
    requiresBuild: true
    dev: false
    optional: true

  /mkdirp@2.1.3:
    resolution: {integrity: sha512-sjAkg21peAG9HS+Dkx7hlG9Ztx7HLeKnvB3NQRcu/mltCVmvkF0pisbiTSfDVYTT86XEfZrTUosLdZLStquZUw==}
    engines: {node: '>=10'}
    hasBin: true
    dev: false

  /moo@0.5.2:
    resolution: {integrity: sha512-iSAJLHYKnX41mKcJKjqvnAN9sf0LMDTXDEvFv+ffuRR9a1MIuXLjMNL6EsnDHSkKLTWNqQQ5uo61P4EbU4NU+Q==}
    dev: false

  /mri@1.2.0:
    resolution: {integrity: sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA==}
    engines: {node: '>=4'}
    dev: false

  /ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    dev: false

  /ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}
    dev: false

  /multimatch@5.0.0:
    resolution: {integrity: sha512-ypMKuglUrZUD99Tk2bUQ+xNQj43lPEfAeX2o9cTteAmShXy2VHDJpuwu1o0xqoKCt9jLVAvwyFKdLTPXKAfJyA==}
    engines: {node: '>=10'}
    dependencies:
      '@types/minimatch': 3.0.5
      array-differ: 3.0.0
      array-union: 2.1.0
      arrify: 2.0.1
      minimatch: 3.1.2
    dev: false

  /multiply-llamaindex@0.0.25:
    resolution: {integrity: sha512-x/fp80TZ/v0lQ+0lj6bZIrqotEkcSh73v2Ls+36Gg6nUIWf4DEDLs29YKsyOTVsiYMuKf97Es9bEYr3iR+tNRA==}
    engines: {node: '>=18.0.0'}
    dependencies:
      '@anthropic-ai/sdk': 0.6.2(encoding@0.1.13)
      encoding: 0.1.13
      lodash: 4.17.21
      openai: 4.3.1(encoding@0.1.13)
      papaparse: 5.4.1
      pdf-parse: 1.1.1
      replicate: 0.16.1
      typescript: 5.2.2
      uuid: 9.0.0
      wink-nlp: 1.14.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /multishift@2.0.9(@remirror/pm@2.0.8)(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-55vFZ0xLvZV0RO4sUyIS2+DfJREtpTLSLGTgdUfmVvANCm0cRbyNLPmmXWn9iy+NFipecsKxN64qO90KucJWHQ==}
    peerDependencies:
      '@types/react': ^16.14.0 || ^17 || ^18
      react: ^16.14.0 || ^17 || ^18
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core-helpers': 3.0.0
      '@remirror/core-types': 2.0.5(@remirror/pm@2.0.8)
      '@seznam/compose-react-refs': 1.0.6
      '@types/react': 18.2.14
      a11y-status: 2.0.1
      compute-scroll-into-view: 1.0.20
      react: 18.2.0
      tiny-warning: 1.0.3
      w3c-keyname: 2.2.8
    transitivePeerDependencies:
      - '@remirror/pm'
    dev: false

  /mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  /nanoevents@5.1.13:
    resolution: {integrity: sha512-JFAeG9fp0QZnRoESHjkbVFbZ9BkOXkkagUVwZVo/pkSX+Fq1VKlY+5og/8X9CYc6C7vje/CV+bwJ5M2X0+IY9Q==}
    engines: {node: '>=6.0.0'}
    dev: false

  /nanoid@3.3.6:
    resolution: {integrity: sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  /neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}
    dev: false

  /next-themes@0.2.1(next@13.4.7)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-B+AKNfYNIzh0vqQQKqQItTS8evEouKD7H5Hj3kmuPERwddR2TxvDSFZuTj6T7Jfn1oyeUyJMydPl1Bkxkh0W7A==}
    peerDependencies:
      next: '*'
      react: '*'
      react-dom: '*'
    dependencies:
      next: 13.4.7(@babel/core@7.24.3)(react-dom@18.2.0)(react@18.2.0)(sass@1.63.6)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /next-tick@1.1.0:
    resolution: {integrity: sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ==}
    dev: false

  /next@13.4.7(@babel/core@7.24.3)(react-dom@18.2.0)(react@18.2.0)(sass@1.63.6):
    resolution: {integrity: sha512-M8z3k9VmG51SRT6v5uDKdJXcAqLzP3C+vaKfLIAM0Mhx1um1G7MDnO63+m52qPdZfrTFzMZNzfsgvm3ghuVHIQ==}
    engines: {node: '>=16.8.0'}
    hasBin: true
    peerDependencies:
      '@opentelemetry/api': ^1.1.0
      fibers: '>= 3.1.0'
      react: ^18.2.0
      react-dom: ^18.2.0
      sass: ^1.3.0
    peerDependenciesMeta:
      '@opentelemetry/api':
        optional: true
      fibers:
        optional: true
      sass:
        optional: true
    dependencies:
      '@next/env': 13.4.7
      '@swc/helpers': 0.5.1
      busboy: 1.6.0
      caniuse-lite: 1.0.30001600
      postcss: 8.4.14
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      sass: 1.63.6
      styled-jsx: 5.1.1(@babel/core@7.24.3)(react@18.2.0)
      watchpack: 2.4.0
      zod: 3.21.4
    optionalDependencies:
      '@next/swc-darwin-arm64': 13.4.7
      '@next/swc-darwin-x64': 13.4.7
      '@next/swc-linux-arm64-gnu': 13.4.7
      '@next/swc-linux-arm64-musl': 13.4.7
      '@next/swc-linux-x64-gnu': 13.4.7
      '@next/swc-linux-x64-musl': 13.4.7
      '@next/swc-win32-arm64-msvc': 13.4.7
      '@next/swc-win32-ia32-msvc': 13.4.7
      '@next/swc-win32-x64-msvc': 13.4.7
    transitivePeerDependencies:
      - '@babel/core'
      - babel-plugin-macros
    dev: false

  /node-domexception@1.0.0:
    resolution: {integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==}
    engines: {node: '>=10.5.0'}
    dev: false

  /node-ensure@0.0.0:
    resolution: {integrity: sha512-DRI60hzo2oKN1ma0ckc6nQWlHU69RH6xN0sjQTjMpChPfTYvKZdcQFfdYK2RWbJcKyUizSIy/l8OTGxMAM1QDw==}
    dev: false

  /node-fetch@2.6.12(encoding@0.1.13):
    resolution: {integrity: sha512-C/fGU2E8ToujUivIO0H+tpQ6HWo4eEmchoPIoXtxCrVghxdKq+QOHqEZW7tuP3KlV3bC8FRMO5nMCC7Zm1VP6g==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true
    dependencies:
      encoding: 0.1.13
      whatwg-url: 5.0.0
    dev: false

  /node-fetch@2.6.7(encoding@0.1.13):
    resolution: {integrity: sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true
    dependencies:
      encoding: 0.1.13
      whatwg-url: 5.0.0
    dev: false

  /node-forge@1.3.1:
    resolution: {integrity: sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==}
    engines: {node: '>= 6.13.0'}
    dev: false

  /node-loader@2.0.0(webpack@5.91.0):
    resolution: {integrity: sha512-I5VN34NO4/5UYJaUBtkrODPWxbobrE4hgDqPrjB25yPkonFhCmZ146vTH+Zg417E9Iwoh1l/MbRs1apc5J295Q==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^5.0.0
    dependencies:
      loader-utils: 2.0.4
      webpack: 5.91.0
    dev: false

  /node-releases@2.0.13:
    resolution: {integrity: sha512-uYr7J37ae/ORWdZeQ1xxMJe3NtdmqMC/JZK+geofDrkLUApKRHPd18/TxtBOJ4A0/+uUIliorNrfYV6s1b02eQ==}
    dev: false

  /node-releases@2.0.14:
    resolution: {integrity: sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==}
    dev: false

  /normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  /normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    dependencies:
      boolbase: 1.0.0
    dev: false

  /number-is-nan@1.0.1:
    resolution: {integrity: sha512-4jbtZXNAsfZbAHiiqjLPBiCl16dES1zI4Hpzzxw61Tk+loF+sBDBKx1ICKKKwIqQ7M0mFn1TmkN7euSncWgHiQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  /object-hash@3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}

  /object-inspect@1.12.3:
    resolution: {integrity: sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==}
    dev: false

  /object-is@1.1.5:
    resolution: {integrity: sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
    dev: false

  /object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}
    dev: false

  /object.assign@4.1.4:
    resolution: {integrity: sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      has-symbols: 1.0.3
      object-keys: 1.1.1
    dev: false

  /object.omit@3.0.0:
    resolution: {integrity: sha512-EO+BCv6LJfu+gBIF3ggLicFebFLN5zqzz/WWJlMFfkMyGth+oBkhxzDl0wx2W4GkLzuQs/FsSkXZb2IMWQqmBQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 1.0.1
    dev: false

  /object.pick@1.3.0:
    resolution: {integrity: sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: false

  /once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}
    dependencies:
      wrappy: 1.0.2

  /openai@4.3.1(encoding@0.1.13):
    resolution: {integrity: sha512-64iI2LbJLk0Ss4Nv5IrdGFe6ALNnKlMuXoGuH525bJYxdupJfDCAtra/Jigex1z8it0U82M87tR2TMGU+HYeFQ==}
    hasBin: true
    dependencies:
      '@types/node': 18.17.12
      '@types/node-fetch': 2.6.4
      abort-controller: 3.0.0
      agentkeepalive: 4.5.0
      digest-fetch: 1.3.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.6.12(encoding@0.1.13)
    transitivePeerDependencies:
      - encoding
    dev: false

  /opencollective-postinstall@2.0.3:
    resolution: {integrity: sha512-8AV/sCtuzUeTo8gQK5qDZzARrulB3egtLzFgteqB2tcT4Mw7B8Kt7JcDHmltjz6FOAHsvTevk70gZEbhM4ZS9Q==}
    hasBin: true
    dev: false

  /optionator@0.8.3:
    resolution: {integrity: sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.5
    dev: false

  /orderedmap@2.1.1:
    resolution: {integrity: sha512-TvAWxi0nDe1j/rtMcWcIj94+Ffe6n7zhow33h40SKxmsmozs6dz/e+EajymfoFcHd7sxNn8yHM8839uixMOV6g==}
    dev: false

  /outvariant@1.4.0:
    resolution: {integrity: sha512-AlWY719RF02ujitly7Kk/0QlV+pXGFDHrHf9O2OKqyqgBieaPOIeuSkL8sRK6j2WK+/ZAURq2kZsY0d8JapUiw==}
    dev: false

  /p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0
    dev: false

  /p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}
    dependencies:
      p-limit: 3.1.0
    dev: false

  /papaparse@5.4.1:
    resolution: {integrity: sha512-HipMsgJkZu8br23pW15uvo6sib6wne/4woLZPlFf3rpDyMe9ywEXUsuD7+6K9PRkJlVT51j/sCOYDKGGS3ZJrw==}
    dev: false

  /parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: false

  /parenthesis@3.1.8:
    resolution: {integrity: sha512-KF/U8tk54BgQewkJPvB4s/US3VQY68BRDpH638+7O/n58TpnwiwnOtGIOsT2/i+M78s61BBpeC83STB88d8sqw==}
    dev: false

  /parse-entities@2.0.0:
    resolution: {integrity: sha512-kkywGpCcRYhqQIchaWqZ875wzpS/bMKhz5HnN3p7wveJTkTtyAB/AlnS0f8DFSqYW1T82t6yEAkEcB+A1I3MbQ==}
    dependencies:
      character-entities: 1.2.4
      character-entities-legacy: 1.1.4
      character-reference-invalid: 1.1.4
      is-alphanumerical: 1.0.4
      is-decimal: 1.0.4
      is-hexadecimal: 1.0.4
    dev: false

  /parse-entities@4.0.1:
    resolution: {integrity: sha512-SWzvYcSJh4d/SGLIOQfZ/CoNv6BTlI6YEQ7Nj82oDVnRpwe/Z/F1EMx42x3JAOwGBlCjeCH0BRJQbQ/opHL17w==}
    dependencies:
      '@types/unist': 2.0.7
      character-entities: 2.0.2
      character-entities-legacy: 3.0.0
      character-reference-invalid: 2.0.1
      decode-named-character-reference: 1.0.2
      is-alphanumerical: 2.0.1
      is-decimal: 2.0.1
      is-hexadecimal: 2.0.1
    dev: false

  /parse-exponential@1.0.1:
    resolution: {integrity: sha512-QUa7PaOc7O6ei3hb0NmADJGrDYLbPBdcSKFUBGfwlMdHsrg8LOsliPEkpP0qHSKQOyzyyxCB00fxJKcP75Gl7w==}
    engines: {node: '>=0.10.0'}
    dev: false

  /parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.22.10
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4
    dev: false

  /parse5-htmlparser2-tree-adapter@7.0.0:
    resolution: {integrity: sha512-B77tOZrqqfUfnVcOrUvfdLbz4pu4RopLD/4vmu3HUPswwTA8OH0EMW9BlWR2B0RCoiZRAHEUu7IxeP1Pd1UU+g==}
    dependencies:
      domhandler: 5.0.3
      parse5: 7.1.2
    dev: false

  /parse5@6.0.1:
    resolution: {integrity: sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==}
    dev: false

  /parse5@7.1.2:
    resolution: {integrity: sha512-Czj1WaSVpaoj0wbhMzLmWD69anp2WH7FXMB9n1Sy8/ZFF9jolSQVMu1Ij5WIyGmcBmhk7EOndpO4mIpihVqAXw==}
    dependencies:
      entities: 4.5.0
    dev: false

  /path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}
    dev: false

  /path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  /path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  /path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}
    dev: false

  /pdf-parse@1.1.1:
    resolution: {integrity: sha512-v6ZJ/efsBpGrGGknjtq9J/oC8tZWq0KWL5vQrk2GlzLEQPUDB1ex+13Rmidl1neNN358Jn9EHZw5y07FFtaC7A==}
    engines: {node: '>=6.8.1'}
    dependencies:
      debug: 3.2.7
      node-ensure: 0.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}

  /picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  /pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}

  /pirates@4.0.6:
    resolution: {integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==}
    engines: {node: '>= 6'}

  /postcss-import@15.1.0(postcss@8.4.24):
    resolution: {integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0
    dependencies:
      postcss: 8.4.24
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.4

  /postcss-js@4.0.1(postcss@8.4.24):
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.4.24

  /postcss-load-config@4.0.1(postcss@8.4.24):
    resolution: {integrity: sha512-vEJIc8RdiBRu3oRAI0ymerOn+7rPuMvRXslTvZUKZonDHFIczxztIyJ1urxM1x9JXEikvpWWTUUqal5j/8QgvA==}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true
    dependencies:
      lilconfig: 2.1.0
      postcss: 8.4.24
      yaml: 2.3.1

  /postcss-nested@6.0.1(postcss@8.4.24):
    resolution: {integrity: sha512-mEp4xPMi5bSWiMbsgoPfcP74lsWLHkQbZc3sY+jWYd65CUwXrUaTp0fmNpa01ZcETKlIgUdFN/MpS2xZtqL9dQ==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14
    dependencies:
      postcss: 8.4.24
      postcss-selector-parser: 6.0.13

  /postcss-selector-parser@6.0.10:
    resolution: {integrity: sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss-selector-parser@6.0.13:
    resolution: {integrity: sha512-EaV1Gl4mUEV4ddhDnv/xtj7sxwrwxdetHdWUGnT4VJQf+4d05v6lHYZr8N573k5Z0BViss7BDhfWtKS3+sfAqQ==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  /postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  /postcss@8.4.14:
    resolution: {integrity: sha512-E398TUmfAYFPBSdzgeieK2Y1+1cpdxJx8yXbK/m57nRhKSmk1GB2tO4lbLBtlkfPQTDKfe4Xqv1ASWPpayPEig==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.6
      picocolors: 1.0.0
      source-map-js: 1.0.2
    dev: false

  /postcss@8.4.24:
    resolution: {integrity: sha512-M0RzbcI0sO/XJNucsGjvWU9ERWxb/ytp1w6dKtxTKgixdtQDq4rmx/g8W1hnaheq9jgwL/oyEdH5Bc4WwJKMqg==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.6
      picocolors: 1.0.0
      source-map-js: 1.0.2

  /precision@1.0.1:
    resolution: {integrity: sha512-cBMxnM2nzEF1xx75NhhOaKjsDNt92WUZv17t/p3wrvCfA+2RL0twbgfvXvgDbxxsfUUb5C5he5tla8Xa2ny1Ew==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-finite: 1.0.2
      parse-exponential: 1.0.1
    dev: false

  /prelude-ls@1.1.2:
    resolution: {integrity: sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w==}
    engines: {node: '>= 0.8.0'}
    dev: false

  /prismjs@1.27.0:
    resolution: {integrity: sha512-t13BGPUlFDR7wRB5kQDG4jjl7XeuH6jbJGt11JHPL96qwsEHNX2+68tFXqc1/k+/jALsbSWJKUOT/hcYAZ5LkA==}
    engines: {node: '>=6'}
    dev: false

  /process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}
    dev: false

  /prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1
    dev: false

  /property-information@5.6.0:
    resolution: {integrity: sha512-YUHSPk+A30YPv+0Qf8i9Mbfe/C0hdPXk1s1jPVToV8pk8BQtpw10ct89Eo7OWkutrwqvT0eicAxlOg3dOAu8JA==}
    dependencies:
      xtend: 4.0.2
    dev: false

  /property-information@6.2.0:
    resolution: {integrity: sha512-kma4U7AFCTwpqq5twzC1YVIDXSqg6qQK6JN0smOw8fgRy1OkMi0CYSzFmsy6dnqSenamAtj0CyXMUJ1Mf6oROg==}
    dev: false

  /prosemirror-changeset@2.2.1:
    resolution: {integrity: sha512-J7msc6wbxB4ekDFj+n9gTW/jav/p53kdlivvuppHsrZXCaQdVgRghoZbSS3kwrRyAstRVQ4/+u5k7YfLgkkQvQ==}
    dependencies:
      prosemirror-transform: 1.7.4
    dev: false

  /prosemirror-collab@1.3.1:
    resolution: {integrity: sha512-4SnynYR9TTYaQVXd/ieUvsVV4PDMBzrq2xPUWutHivDuOshZXqQ5rGbZM84HEaXKbLdItse7weMGOUdDVcLKEQ==}
    dependencies:
      prosemirror-state: 1.4.3
    dev: false

  /prosemirror-commands@1.5.2:
    resolution: {integrity: sha512-hgLcPaakxH8tu6YvVAaILV2tXYsW3rAdDR8WNkeKGcgeMVQg3/TMhPdVoh7iAmfgVjZGtcOSjKiQaoeKjzd2mQ==}
    dependencies:
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.7.4
    dev: false

  /prosemirror-dropcursor@1.8.1:
    resolution: {integrity: sha512-M30WJdJZLyXHi3N8vxN6Zh5O8ZBbQCz0gURTfPmTIBNQ5pxrdU7A58QkNqfa98YEjSAL1HUyyU34f6Pm5xBSGw==}
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.7.4
      prosemirror-view: 1.18.1
    dev: false

  /prosemirror-gapcursor@1.3.2:
    resolution: {integrity: sha512-wtjswVBd2vaQRrnYZaBCbyDqr232Ed4p2QPtRIUK5FuqHYKGWkEwl08oQM4Tw7DOR0FsasARV5uJFvMZWxdNxQ==}
    dependencies:
      prosemirror-keymap: 1.2.2
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-view: 1.18.1
    dev: false

  /prosemirror-history@1.3.2:
    resolution: {integrity: sha512-/zm0XoU/N/+u7i5zepjmZAEnpvjDtzoPWW6VmKptcAnPadN/SStsBjMImdCEbb3seiNTpveziPTIrXQbHLtU1g==}
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.7.4
      prosemirror-view: 1.31.7
      rope-sequence: 1.3.4
    dev: false

  /prosemirror-history@1.4.0:
    resolution: {integrity: sha512-UUiGzDVcqo1lovOPdi9YxxUps3oBFWAIYkXLu3Ot+JPv1qzVogRbcizxK3LhHmtaUxclohgiOVesRw5QSlMnbQ==}
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.8.0
      prosemirror-view: 1.33.2
      rope-sequence: 1.3.4
    dev: false

  /prosemirror-inputrules@1.2.1:
    resolution: {integrity: sha512-3LrWJX1+ULRh5SZvbIQlwZafOXqp1XuV21MGBu/i5xsztd+9VD15x6OtN6mdqSFI7/8Y77gYUbQ6vwwJ4mr6QQ==}
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.7.4
    dev: false

  /prosemirror-inputrules@1.4.0:
    resolution: {integrity: sha512-6ygpPRuTJ2lcOXs9JkefieMst63wVJBgHZGl5QOytN7oSZs3Co/BYbc3Yx9zm9H37Bxw8kVzCnDsihsVsL4yEg==}
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.8.0
    dev: false

  /prosemirror-keymap@1.2.2:
    resolution: {integrity: sha512-EAlXoksqC6Vbocqc0GtzCruZEzYgrn+iiGnNjsJsH4mrnIGex4qbLdWWNza3AW5W36ZRrlBID0eM6bdKH4OStQ==}
    dependencies:
      prosemirror-state: 1.4.3
      w3c-keyname: 2.2.8
    dev: false

  /prosemirror-markdown@1.11.2:
    resolution: {integrity: sha512-Eu5g4WPiCdqDTGhdSsG9N6ZjACQRYrsAkrF9KYfdMaCmjIApH75aVncsWYOJvEk2i1B3i8jZppv3J/tnuHGiUQ==}
    dependencies:
      markdown-it: 13.0.1
      prosemirror-model: 1.19.3
    dev: false

  /prosemirror-markdown@1.12.0:
    resolution: {integrity: sha512-6F5HS8Z0HDYiS2VQDZzfZP6A0s/I0gbkJy8NCzzDMtcsz3qrfqyroMMeoSjAmOhDITyon11NbXSzztfKi+frSQ==}
    dependencies:
      markdown-it: 14.1.0
      prosemirror-model: 1.19.4
    dev: false

  /prosemirror-menu@1.2.2:
    resolution: {integrity: sha512-437HIWTq4F9cTX+kPfqZWWm+luJm95Aut/mLUy+9OMrOml0bmWDS26ceC6SNfb2/S94et1sZ186vLO7pDHzxSw==}
    dependencies:
      crelt: 1.0.6
      prosemirror-commands: 1.5.2
      prosemirror-history: 1.3.2
      prosemirror-state: 1.4.3
    dev: false

  /prosemirror-menu@1.2.4:
    resolution: {integrity: sha512-S/bXlc0ODQup6aiBbWVsX/eM+xJgCTAfMq/nLqaO5ID/am4wS0tTCIkzwytmao7ypEtjj39i7YbJjAgO20mIqA==}
    dependencies:
      crelt: 1.0.6
      prosemirror-commands: 1.5.2
      prosemirror-history: 1.4.0
      prosemirror-state: 1.4.3
    dev: false

  /prosemirror-model@1.19.3:
    resolution: {integrity: sha512-tgSnwN7BS7/UM0sSARcW+IQryx2vODKX4MI7xpqY2X+iaepJdKBPc7I4aACIsDV/LTaTjt12Z56MhDr9LsyuZQ==}
    dependencies:
      orderedmap: 2.1.1
    dev: false

  /prosemirror-model@1.19.4:
    resolution: {integrity: sha512-RPmVXxUfOhyFdayHawjuZCxiROsm9L4FCUA6pWI+l7n2yCBsWy9VpdE1hpDHUS8Vad661YLY9AzqfjLhAKQ4iQ==}
    dependencies:
      orderedmap: 2.1.1
    dev: false

  /prosemirror-paste-rules@2.0.7(prosemirror-model@1.19.3)(prosemirror-state@1.4.3)(prosemirror-view@1.31.7):
    resolution: {integrity: sha512-0vQ+S8wksW1c9W88pzNEg3T4XHihl1QJy+i8fuF2HpCEkNkKM44P8x71XKh8QBdANuGQynDVKCVJRCvJfB5F4g==}
    peerDependencies:
      prosemirror-model: ^1.19.0
      prosemirror-state: ^1.4.2
      prosemirror-view: ^1.31.2
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core-constants': 2.0.2
      '@remirror/core-helpers': 3.0.0
      escape-string-regexp: 4.0.0
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-view: 1.31.7
    dev: false

  /prosemirror-resizable-view@2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-TgCaOWaMdR/R34AYsxHtHuQlC9rtHTuSBEgcmD5V9ldVe1+Ul72xz9G73xo9wLhVpTwt3YxzpenTcRWPZIofow==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core-helpers': 3.0.0
      '@remirror/core-utils': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      prosemirror-model: 1.19.3
      prosemirror-view: 1.31.7
    transitivePeerDependencies:
      - '@remirror/pm'
      - '@types/node'
      - jsdom
    dev: false

  /prosemirror-schema-basic@1.2.2:
    resolution: {integrity: sha512-/dT4JFEGyO7QnNTe9UaKUhjDXbTNkiWTq/N4VpKaF79bBjSExVV2NXmJpcM7z/gD7mbqNjxbmWW5nf1iNSSGnw==}
    dependencies:
      prosemirror-model: 1.19.3
    dev: false

  /prosemirror-schema-list@1.3.0:
    resolution: {integrity: sha512-Hz/7gM4skaaYfRPNgr421CU4GSwotmEwBVvJh5ltGiffUJwm7C8GfN/Bc6DR1EKEp5pDKhODmdXXyi9uIsZl5A==}
    dependencies:
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.7.4
    dev: false

  /prosemirror-state@1.4.3:
    resolution: {integrity: sha512-goFKORVbvPuAQaXhpbemJFRKJ2aixr+AZMGiquiqKxaucC6hlpHNZHWgz5R7dS4roHiwq9vDctE//CZ++o0W1Q==}
    dependencies:
      prosemirror-model: 1.19.3
      prosemirror-transform: 1.7.4
      prosemirror-view: 1.31.7
    dev: false

  /prosemirror-suggest@2.0.6(prosemirror-model@1.19.3)(prosemirror-state@1.4.3)(prosemirror-view@1.31.7):
    resolution: {integrity: sha512-plcfFPyLFQ/egCn6DR+Ml3ONFhO+tR3Wt3yO5aJo6dnhoyudKiRQsf0rgQDPoWBPc8L7l9opUfDWFXHrDQYvkg==}
    peerDependencies:
      prosemirror-model: ^1.19.0
      prosemirror-state: ^1.4.2
      prosemirror-view: ^1.31.2
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core-constants': 2.0.2
      '@remirror/core-helpers': 3.0.0
      '@remirror/types': 1.0.1
      escape-string-regexp: 4.0.0
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-view: 1.31.7
    dev: false

  /prosemirror-tables@1.3.4:
    resolution: {integrity: sha512-z6uLSQ1BLC3rgbGwZmpfb+xkdvD7W/UOsURDfognZFYaTtc0gsk7u/t71Yijp2eLflVpffMk6X0u0+u+MMDvIw==}
    dependencies:
      prosemirror-keymap: 1.2.2
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.7.4
      prosemirror-view: 1.31.7
    dev: false

  /prosemirror-tables@1.3.7:
    resolution: {integrity: sha512-oEwX1wrziuxMtwFvdDWSFHVUWrFJWt929kVVfHvtTi8yvw+5ppxjXZkMG/fuTdFo+3DXyIPSKfid+Be1npKXDA==}
    dependencies:
      prosemirror-keymap: 1.2.2
      prosemirror-model: 1.19.4
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.8.0
      prosemirror-view: 1.33.2
    dev: false

  /prosemirror-trailing-node@2.0.7(prosemirror-model@1.19.3)(prosemirror-state@1.4.3)(prosemirror-view@1.31.7):
    resolution: {integrity: sha512-8zcZORYj/8WEwsGo6yVCRXFMOfBo0Ub3hCUvmoWIZYfMP26WqENU0mpEP27w7mt8buZWuGrydBewr0tOArPb1Q==}
    peerDependencies:
      prosemirror-model: ^1.19.0
      prosemirror-state: ^1.4.2
      prosemirror-view: ^1.31.2
    dependencies:
      '@remirror/core-constants': 2.0.2
      '@remirror/core-helpers': 3.0.0
      escape-string-regexp: 4.0.0
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-view: 1.31.7
    dev: false

  /prosemirror-trailing-node@2.0.8(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2):
    resolution: {integrity: sha512-ujRYhSuhQb1Jsarh1IHqb2KoSnRiD7wAMDGucP35DN7j5af6X7B18PfdPIrbwsPTqIAj0fyOvxbuPsWhNvylmA==}
    peerDependencies:
      prosemirror-model: ^1.19.0
      prosemirror-state: ^1.4.2
      prosemirror-view: ^1.31.2
    dependencies:
      '@remirror/core-constants': 2.0.2
      escape-string-regexp: 4.0.0
      prosemirror-model: 1.19.4
      prosemirror-state: 1.4.3
      prosemirror-view: 1.33.2
    dev: false

  /prosemirror-transform@1.7.4:
    resolution: {integrity: sha512-GO38mvqJ2yeI0BbL5E1CdHcly032Dlfn9nHqlnCHqlNf9e9jZwJixxp6VRtOeDZ1uTDpDIziezMKbA41LpAx3A==}
    dependencies:
      prosemirror-model: 1.19.3
    dev: false

  /prosemirror-transform@1.8.0:
    resolution: {integrity: sha512-BaSBsIMv52F1BVVMvOmp1yzD3u65uC3HTzCBQV1WDPqJRQ2LuHKcyfn0jwqodo8sR9vVzMzZyI+Dal5W9E6a9A==}
    dependencies:
      prosemirror-model: 1.19.4
    dev: false

  /prosemirror-view@1.18.1:
    resolution: {integrity: sha512-TZd8byDRfdopLiokBY7T27msCSfWqqRxWs/LnBbdI030F+iI2kS+tO59/XFnpZxMLFKlJgOgGGhM9SzD1Nwdxw==}
    dependencies:
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.7.4
    dev: false

  /prosemirror-view@1.31.7:
    resolution: {integrity: sha512-Pr7w93yOYmxQwzGIRSaNLZ/1uM6YjnenASzN2H6fO6kGekuzRbgZ/4bHbBTd1u4sIQmL33/TcGmzxxidyPwCjg==}
    dependencies:
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.7.4
    dev: false

  /prosemirror-view@1.33.2:
    resolution: {integrity: sha512-HYMowWXC4pPaTbOxdb8ewMlA/QyttZ9QWJmfzLOzoU5y3sNFqllbhGZ3xsYdnQWi9+iLY55TUDNP+dlqF+VWTw==}
    dependencies:
      prosemirror-model: 1.19.4
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.8.0
    dev: false

  /proto3-json-serializer@1.1.1:
    resolution: {integrity: sha512-AwAuY4g9nxx0u52DnSMkqqgyLHaW/XaPLtaAo3y/ZCfeaQB/g4YDH4kb8Wc/mWzWvu0YjOznVnfn373MVZZrgw==}
    engines: {node: '>=12.0.0'}
    requiresBuild: true
    dependencies:
      protobufjs: 7.2.6
    dev: false
    optional: true

  /protobufjs-cli@1.1.1(protobufjs@7.2.4):
    resolution: {integrity: sha512-VPWMgIcRNyQwWUv8OLPyGQ/0lQY/QTQAVN5fh+XzfDwsVw1FZ2L3DM/bcBf8WPiRz2tNpaov9lPZfNcmNo6LXA==}
    engines: {node: '>=12.0.0'}
    hasBin: true
    requiresBuild: true
    peerDependencies:
      protobufjs: ^7.0.0
    dependencies:
      chalk: 4.1.2
      escodegen: 1.14.3
      espree: 9.6.1
      estraverse: 5.3.0
      glob: 8.1.0
      jsdoc: 4.0.2
      minimist: 1.2.8
      protobufjs: 7.2.4
      semver: 7.5.4
      tmp: 0.2.1
      uglify-js: 3.17.4
    dev: false
    optional: true

  /protobufjs@6.11.3:
    resolution: {integrity: sha512-xL96WDdCZYdU7Slin569tFX712BxsxslWwAfAhCYjQKGTq7dAU91Lomy6nLLhh/dyGhk/YH4TwTSRxTzhuHyZg==}
    hasBin: true
    requiresBuild: true
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/base64': 1.1.2
      '@protobufjs/codegen': 2.0.4
      '@protobufjs/eventemitter': 1.1.0
      '@protobufjs/fetch': 1.1.0
      '@protobufjs/float': 1.0.2
      '@protobufjs/inquire': 1.1.0
      '@protobufjs/path': 1.1.2
      '@protobufjs/pool': 1.1.0
      '@protobufjs/utf8': 1.1.0
      '@types/long': 4.0.2
      '@types/node': 20.3.3
      long: 4.0.0
    dev: false

  /protobufjs@7.2.4:
    resolution: {integrity: sha512-AT+RJgD2sH8phPmCf7OUZR8xGdcJRga4+1cOaXJ64hvcSkVhNcRHOwIxUatPH15+nj59WAGTDv3LSGZPEQbJaQ==}
    engines: {node: '>=12.0.0'}
    requiresBuild: true
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/base64': 1.1.2
      '@protobufjs/codegen': 2.0.4
      '@protobufjs/eventemitter': 1.1.0
      '@protobufjs/fetch': 1.1.0
      '@protobufjs/float': 1.0.2
      '@protobufjs/inquire': 1.1.0
      '@protobufjs/path': 1.1.2
      '@protobufjs/pool': 1.1.0
      '@protobufjs/utf8': 1.1.0
      '@types/node': 20.3.3
      long: 5.2.3
    dev: false

  /protobufjs@7.2.6:
    resolution: {integrity: sha512-dgJaEDDL6x8ASUZ1YqWciTRrdOuYNzoOf27oHNfdyvKqHr5i0FV7FSLU+aIeFjyFgVxrpTOtQUi0BLLBymZaBw==}
    engines: {node: '>=12.0.0'}
    requiresBuild: true
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/base64': 1.1.2
      '@protobufjs/codegen': 2.0.4
      '@protobufjs/eventemitter': 1.1.0
      '@protobufjs/fetch': 1.1.0
      '@protobufjs/float': 1.0.2
      '@protobufjs/inquire': 1.1.0
      '@protobufjs/path': 1.1.2
      '@protobufjs/pool': 1.1.0
      '@protobufjs/utf8': 1.1.0
      '@types/node': 20.3.3
      long: 5.2.3
    dev: false
    optional: true

  /proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}
    dev: false

  /pseudomap@1.0.2:
    resolution: {integrity: sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==}
    dev: false

  /punycode.js@2.3.1:
    resolution: {integrity: sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA==}
    engines: {node: '>=6'}
    dev: false

  /punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}
    dev: false

  /querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}
    dev: false

  /queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  /queue@6.0.2:
    resolution: {integrity: sha512-iHZWu+q3IdFZFX36ro/lKBkSvfkztY5Y7HMiPlOUjhupPcG2JMfst2KKEpu5XndviX/3UhFbRngUPNKtgvtZiA==}
    dependencies:
      inherits: 2.0.4
    dev: false

  /randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /react-color@2.19.3(react@18.2.0):
    resolution: {integrity: sha512-LEeGE/ZzNLIsFWa1TMe8y5VYqr7bibneWmvJwm1pCn/eNmrabWDh659JSPn9BuaMpEfU83WTOJfnCcjDZwNQTA==}
    peerDependencies:
      react: '*'
    dependencies:
      '@icons/material': 0.2.4(react@18.2.0)
      lodash: 4.17.21
      lodash-es: 4.17.21
      material-colors: 1.2.6
      prop-types: 15.8.1
      react: 18.2.0
      reactcss: 1.2.3(react@18.2.0)
      tinycolor2: 1.6.0
    dev: false

  /react-daisyui@4.0.0(daisyui@3.1.7)(react-dom@18.2.0)(react@18.2.0)(tailwindcss@3.3.2):
    resolution: {integrity: sha512-kE+Ybw6GD00AQ6fGm0JRUUF7t3P127D2+XFVRwhEHqgdwLo8K/XhuWA69ilNZSLTklgrnghaUh8H1mtubk3fmg==}
    peerDependencies:
      daisyui: ^3.0.22
      react: '>=16'
      react-dom: '>=16'
      tailwindcss: '>=3.2.7'
    dependencies:
      daisyui: 3.1.7(postcss@8.4.24)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      tailwindcss: 3.3.2
    dev: false

  /react-day-picker@8.8.1(date-fns@2.30.0)(react@18.2.0):
    resolution: {integrity: sha512-U7RsRoRI5pyMXhKq54hS9yM11WEGkPf8hIdrxIM/sefgmQjuxazqgwcZFMiPZW/K9vtmzLZFf9bLW0wVsGYd5w==}
    peerDependencies:
      date-fns: ^2.28.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      date-fns: 2.30.0
      react: 18.2.0
    dev: false

  /react-devtools-inline@4.4.0:
    resolution: {integrity: sha512-ES0GolSrKO8wsKbsEkVeiR/ZAaHQTY4zDh1UW8DImVmm8oaGLl3ijJDvSGe+qDRKPZdPRnDtWWnSvvrgxXdThQ==}
    dependencies:
      es6-symbol: 3.1.3
    dev: false

  /react-diff-view@3.1.0(react@18.2.0):
    resolution: {integrity: sha512-xIouC5F8gSXJ4DNlZWjyGyqRT9HVQ6v6Fo2ko+XDQpRmsJjYcd32YiQnCV1kwXOjoWPfhTCouyFT3dgyOZpRgA==}
    peerDependencies:
      react: '>=16.14.0'
    dependencies:
      classnames: 2.3.2
      diff-match-patch: 1.0.5
      gitdiff-parser: 0.3.1
      lodash: 4.17.21
      react: 18.2.0
      shallow-equal: 3.1.0
      warning: 4.0.3
    dev: false

  /react-dnd-html5-backend@16.0.1:
    resolution: {integrity: sha512-Wu3dw5aDJmOGw8WjH1I1/yTH+vlXEL4vmjk5p+MHxP8HuHJS1lAGeIdG/hze1AvNeXWo/JgULV87LyQOr+r5jw==}
    dependencies:
      dnd-core: 16.0.1
    dev: false

  /react-dnd@16.0.1(@types/node@20.3.3)(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-QeoM/i73HHu2XF9aKksIUuamHPDvRglEwdHL4jsp784BgUuWcg6mzfxT0QDdQz8Wj0qyRKx2eMg8iZtWvU4E2Q==}
    peerDependencies:
      '@types/hoist-non-react-statics': '>= 3.3.1'
      '@types/node': '>= 12'
      '@types/react': '>= 16'
      react: '>= 16.14'
    peerDependenciesMeta:
      '@types/hoist-non-react-statics':
        optional: true
      '@types/node':
        optional: true
      '@types/react':
        optional: true
    dependencies:
      '@react-dnd/invariant': 4.0.2
      '@react-dnd/shallowequal': 4.0.2
      '@types/node': 20.3.3
      '@types/react': 18.2.14
      dnd-core: 16.0.1
      fast-deep-equal: 3.1.3
      hoist-non-react-statics: 3.3.2
      react: 18.2.0
    dev: false

  /react-dom@18.2.0(react@18.2.0):
    resolution: {integrity: sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==}
    peerDependencies:
      react: ^18.2.0
    dependencies:
      loose-envify: 1.4.0
      react: 18.2.0
      scheduler: 0.23.0
    dev: false

  /react-error-boundary@3.1.4(react@18.2.0):
    resolution: {integrity: sha512-uM9uPzZJTF6wRQORmSrvOIgt4lJ9MC1sNgEOj2XGsDTRE4kmpWxg7ENK9EWNKJRMAOY9z0MuF4yIfl6gp4sotA==}
    engines: {node: '>=10', npm: '>=6'}
    peerDependencies:
      react: '>=16.13.1'
    dependencies:
      '@babel/runtime': 7.22.10
      react: 18.2.0
    dev: false

  /react-fast-compare@2.0.4:
    resolution: {integrity: sha512-suNP+J1VU1MWFKcyt7RtjiSWUjvidmQSlqu+eHslq+342xCbGTYmC0mEhPCOHxlW0CywylOC1u2DFAT+bv4dBw==}
    dev: false

  /react-firebase-hooks@5.1.1(firebase@9.23.0)(react@18.2.0):
    resolution: {integrity: sha512-y2UpWs82xs+39q5Rc/wq316ca52QsC0n8m801V+yM4IC4hbfOL4yQPVSh7w+ydstdvjN9F+lvs1WrO2VYxpmdA==}
    peerDependencies:
      firebase: '>= 9.0.0'
      react: '>= 16.8.0'
    dependencies:
      firebase: 9.23.0(encoding@0.1.13)
      react: 18.2.0
    dev: false

  /react-hook-form@7.45.2(react@18.2.0):
    resolution: {integrity: sha512-9s45OdTaKN+4NSTbXVqeDITd/nwIg++nxJGL8+OD5uf1DxvhsXQ641kaYHk5K28cpIOTYm71O/fYk7rFaygb3A==}
    engines: {node: '>=12.22.0'}
    peerDependencies:
      react: ^16.8.0 || ^17 || ^18
    dependencies:
      react: 18.2.0
    dev: false

  /react-icons@4.10.1(react@18.2.0):
    resolution: {integrity: sha512-/ngzDP/77tlCfqthiiGNZeYFACw85fUjZtLbedmJ5DTlNDIwETxhwBzdOJ21zj4iJdvc0J3y7yOsX3PpxAJzrw==}
    peerDependencies:
      react: '*'
    dependencies:
      react: 18.2.0
    dev: false

  /react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}
    dev: false

  /react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}
    dev: false

  /react-is@18.2.0:
    resolution: {integrity: sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==}
    dev: false

  /react-loader-spinner@5.3.4(@babel/core@7.24.3)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-G2vw4ssX+RDZ/vfaeva06yfNqyFViv/u+tVZ3kFLy5TKNlNx2DbuwreBSpRtPespQA+VxinxUJsigwLwG9erOg==}
    peerDependencies:
      react: ^16.0.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.0.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 18.2.0
      styled-components: 5.3.11(@babel/core@7.24.3)(react-dom@18.2.0)(react-is@18.2.0)(react@18.2.0)
      styled-tools: 1.7.2
    transitivePeerDependencies:
      - '@babel/core'
    dev: false

  /react-remove-scroll-bar@2.3.4(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-63C4YQBUt0m6ALadE9XV56hV8BgJWDmmTPY758iIJjfQKt2nYwoUrPk0LXRXcB/yIj82T1/Ixfdpdk68LwIB0A==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
      react-style-singleton: 2.2.1(@types/react@18.2.14)(react@18.2.0)
      tslib: 2.6.1
    dev: false

  /react-remove-scroll-bar@2.3.6(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-DtSYaao4mBmX+HDo5YWYdBWQwYIQQshUV/dVxFxK+KM26Wjwp1gZ6rv6OC3oujI6Bfu6Xyg3TwK533AQutsn/g==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
      react-style-singleton: 2.2.1(@types/react@18.2.14)(react@18.2.0)
      tslib: 2.6.1
    dev: false

  /react-remove-scroll@2.5.4(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-xGVKJJr0SJGQVirVFAUZ2k1QLyO6m+2fy0l8Qawbp5Jgrv3DeLalrfMNBFSlmz5kriGGzsVBtGVnf4pTKIhhWA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
      react-remove-scroll-bar: 2.3.4(@types/react@18.2.14)(react@18.2.0)
      react-style-singleton: 2.2.1(@types/react@18.2.14)(react@18.2.0)
      tslib: 2.6.1
      use-callback-ref: 1.3.0(@types/react@18.2.14)(react@18.2.0)
      use-sidecar: 1.1.2(@types/react@18.2.14)(react@18.2.0)
    dev: false

  /react-remove-scroll@2.5.5(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-ImKhrzJJsyXJfBZ4bzu8Bwpka14c/fQt0k+cyFp/PBhTfyDnU5hjOtM4AG/0AMyy8oKzOTR0lDgJIM7pYXI0kw==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
      react-remove-scroll-bar: 2.3.4(@types/react@18.2.14)(react@18.2.0)
      react-style-singleton: 2.2.1(@types/react@18.2.14)(react@18.2.0)
      tslib: 2.6.1
      use-callback-ref: 1.3.0(@types/react@18.2.14)(react@18.2.0)
      use-sidecar: 1.1.2(@types/react@18.2.14)(react@18.2.0)
    dev: false

  /react-remove-scroll@2.6.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-I2U4JVEsQenxDAKaVa3VZ/JeJZe0/2DxPWL8Tj8yLKctQJQiZM52pn/GWFpSp8dftjM3pSAHVJZscAnC/y+ySQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
      react-remove-scroll-bar: 2.3.6(@types/react@18.2.14)(react@18.2.0)
      react-style-singleton: 2.2.1(@types/react@18.2.14)(react@18.2.0)
      tslib: 2.6.1
      use-callback-ref: 1.3.0(@types/react@18.2.14)(react@18.2.0)
      use-sidecar: 1.1.2(@types/react@18.2.14)(react@18.2.0)
    dev: false

  /react-style-singleton@2.2.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-ZWj0fHEMyWkHzKYUr2Bs/4zU6XLmq9HsgBURm7g5pAVfyn49DgUiNgY2d4lXRlYSiCif9YBGpQleewkcqddc7g==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      get-nonce: 1.0.1
      invariant: 2.2.4
      react: 18.2.0
      tslib: 2.6.1
    dev: false

  /react-textarea-autosize@8.3.4(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-CdtmP8Dc19xL8/R6sWvtknD/eCXkQr30dtvC4VmGInhRsfF8X/ihXCq6+9l9qbxmKRiq407/7z5fxE7cVWQNgQ==}
    engines: {node: '>=10'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      '@babel/runtime': 7.22.10
      react: 18.2.0
      use-composed-ref: 1.3.0(react@18.2.0)
      use-latest: 1.2.1(@types/react@18.2.14)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /react-toastify@9.1.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-fPfb8ghtn/XMxw3LkxQBk3IyagNpF/LIKjOBflbexr2AWxAH1MJgvnESwEwBn9liLFXgTKWgBSdZpw9m4OTHTg==}
    peerDependencies:
      react: '>=16'
      react-dom: '>=16'
    dependencies:
      clsx: 1.2.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /react-transition-group@4.4.5(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'
    dependencies:
      '@babel/runtime': 7.22.10
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /react@18.2.0:
    resolution: {integrity: sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /reactcss@1.2.3(react@18.2.0):
    resolution: {integrity: sha512-KiwVUcFu1RErkI97ywr8nvx8dNOpT03rbnma0SSalTYjkrPYaEajR4a/MRt6DZ46K6arDRbWMNHF+xH7G7n/8A==}
    peerDependencies:
      react: '*'
    dependencies:
      lodash: 4.17.21
      react: 18.2.0
    dev: false

  /read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}
    dependencies:
      pify: 2.3.0

  /readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}
    requiresBuild: true
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2
    dev: false
    optional: true

  /readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1

  /redux@4.2.1:
    resolution: {integrity: sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==}
    dependencies:
      '@babel/runtime': 7.22.10
    dev: false

  /reflect-metadata@0.1.13:
    resolution: {integrity: sha512-Ts1Y/anZELhSsjMcU605fU9RE4Oi3p5ORujwbIKXfWa+0Zxs510Qrmrce5/Jowq3cHSZSJqBjypxmHarc+vEWg==}
    dev: false

  /refractor@3.6.0:
    resolution: {integrity: sha512-MY9W41IOWxxk31o+YvFCNyNzdkc9M20NoZK5vq6jkv4I/uh2zkWcfudj0Q1fovjUQJrNewS9NMzeTtqPf+n5EA==}
    dependencies:
      hastscript: 6.0.0
      parse-entities: 2.0.0
      prismjs: 1.27.0
    dev: false

  /regenerator-runtime@0.14.0:
    resolution: {integrity: sha512-srw17NI0TUWHuGa5CFGGmhfNIeja30WMBfbslPNhf6JrqQlLN5gcrvig1oqPxiVaXb0oW0XRKtH6Nngs5lKCIA==}
    dev: false

  /regexp.prototype.flags@1.5.0:
    resolution: {integrity: sha512-0SutC3pNudRKgquxGoRGIz946MZVHqbNfPjBdxeOhBrdgDKlRoXmYLQN9xRbrR09ZXWeGAdPuif7egofn6v5LA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      functions-have-names: 1.2.3
    dev: false

  /rehype-minify-whitespace@5.0.1:
    resolution: {integrity: sha512-PPp4lWJiBPlePI/dv1BeYktbwkfgXkrK59MUa+tYbMPgleod+4DvFK2PLU0O0O60/xuhHfiR9GUIUlXTU8sRIQ==}
    dependencies:
      '@types/hast': 2.3.5
      hast-util-embedded: 2.0.1
      hast-util-is-element: 2.1.3
      hast-util-whitespace: 2.0.1
      unified: 10.1.2
      unist-util-is: 5.2.1
    dev: false

  /rehype-parse@8.0.4:
    resolution: {integrity: sha512-MJJKONunHjoTh4kc3dsM1v3C9kGrrxvA3U8PxZlP2SjH8RNUSrb+lF7Y0KVaUDnGH2QZ5vAn7ulkiajM9ifuqg==}
    dependencies:
      '@types/hast': 2.3.5
      hast-util-from-parse5: 7.1.2
      parse5: 6.0.1
      unified: 10.1.2
    dev: false

  /rehype-remark@9.1.2:
    resolution: {integrity: sha512-c0fG3/CrJ95zAQ07xqHSkdpZybwdsY7X5dNWvgL2XqLKZuqmG3+vk6kP/4miCnp+R+x/0uKKRSpfXb9aGR8Z5w==}
    dependencies:
      '@types/hast': 2.3.5
      '@types/mdast': 3.0.12
      hast-util-to-mdast: 8.4.1
      unified: 10.1.2
    dev: false

  /rehype-stringify@9.0.3:
    resolution: {integrity: sha512-kWiZ1bgyWlgOxpqD5HnxShKAdXtb2IUljn3hQAhySeak6IOQPPt6DeGnsIh4ixm7yKJWzm8TXFuC/lPfcWHJqw==}
    dependencies:
      '@types/hast': 2.3.5
      hast-util-to-html: 8.0.4
      unified: 10.1.2
    dev: false

  /remark-gfm@3.0.1:
    resolution: {integrity: sha512-lEFDoi2PICJyNrACFOfDD3JlLkuSbOa5Wd8EPt06HUdptv8Gn0bxYTdbU/XXQ3swAPkEaGxxPN9cbnMHvVu1Ig==}
    dependencies:
      '@types/mdast': 3.0.12
      mdast-util-gfm: 2.0.2
      micromark-extension-gfm: 2.0.3
      unified: 10.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /remark-parse@10.0.2:
    resolution: {integrity: sha512-3ydxgHa/ZQzG8LvC7jTXccARYDcRld3VfcgIIFs7bI6vbRSxJJmzgLEIIoYKyrfhaY+ujuWaf/PJiMZXoiCXgw==}
    dependencies:
      '@types/mdast': 3.0.12
      mdast-util-from-markdown: 1.3.1
      unified: 10.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /remark-rehype@10.1.0:
    resolution: {integrity: sha512-EFmR5zppdBp0WQeDVZ/b66CWJipB2q2VLNFMabzDSGR66Z2fQii83G5gTBbgGEnEEA0QRussvrFHxk1HWGJskw==}
    dependencies:
      '@types/hast': 2.3.5
      '@types/mdast': 3.0.12
      mdast-util-to-hast: 12.3.0
      unified: 10.1.2
    dev: false

  /remark-stringify@10.0.3:
    resolution: {integrity: sha512-koyOzCMYoUHudypbj4XpnAKFbkddRMYZHwghnxd7ue5210WzGw6kOBwauJTRUMq16jsovXx8dYNvSSWP89kZ3A==}
    dependencies:
      '@types/mdast': 3.0.12
      mdast-util-to-markdown: 1.5.0
      unified: 10.1.2
    dev: false

  /remirror@2.0.36(@remirror/pm@2.0.8)(@types/node@20.3.3):
    resolution: {integrity: sha512-oGOeEaYFILgxNrL33qCEJ/kGE+wNhYgcCiHwuTXOHFCmPnHn32+l0aZaQ2FRHeMW602i2wpkwy8HWWsBFz5PXg==}
    peerDependencies:
      '@remirror/pm': ^2.0.8
      '@types/prettier': ^2.7.2
      prettier: ^2.8.8
    peerDependenciesMeta:
      '@types/prettier':
        optional: true
      prettier:
        optional: true
    dependencies:
      '@babel/runtime': 7.22.10
      '@remirror/core': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/core-constants': 2.0.2
      '@remirror/core-helpers': 3.0.0
      '@remirror/core-types': 2.0.5(@remirror/pm@2.0.8)
      '@remirror/core-utils': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/dom': 2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-annotation': 2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-bidi': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-blockquote': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-bold': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-callout': 2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-code': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-code-block': 2.0.18(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-codemirror5': 2.0.13(@remirror/pm@2.0.8)(@types/codemirror@5.60.8)(@types/node@20.3.3)(codemirror@5.65.14)
      '@remirror/extension-collaboration': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-columns': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-diff': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-doc': 2.1.5(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-drop-cursor': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-embed': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-emoji': 2.0.17(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-entity-reference': 2.2.6(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-epic-mode': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-events': 2.1.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-font-family': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-font-size': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-gap-cursor': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-hard-break': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-heading': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-history': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-horizontal-rule': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-image': 2.1.10(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-italic': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-link': 2.0.17(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-list': 2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-markdown': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-mention': 2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-mention-atom': 2.0.17(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-node-formatting': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-paragraph': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-placeholder': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-positioner': 2.1.8(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-search': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-shortcuts': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-strike': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-sub': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-sup': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-tables': 2.3.1(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-text': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-text-case': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-text-color': 2.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-text-highlight': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-trailing-node': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-underline': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-whitespace': 2.0.13(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/extension-yjs': 3.0.15(@remirror/pm@2.0.8)(@types/node@20.3.3)(yjs@13.6.7)
      '@remirror/icons': 2.0.3
      '@remirror/pm': 2.0.8
      '@remirror/preset-core': 2.0.16(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/preset-formatting': 2.0.14(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/preset-wysiwyg': 2.0.19(@remirror/pm@2.0.8)(@types/node@20.3.3)
      '@remirror/theme': 2.0.9(@remirror/pm@2.0.8)
      '@types/codemirror': 5.60.8
      '@types/refractor': 3.0.2
      codemirror: 5.65.14
      refractor: 3.6.0
      yjs: 13.6.7
    transitivePeerDependencies:
      - '@types/node'
      - jsdom
      - supports-color
    dev: false

  /remove-accents@0.4.2:
    resolution: {integrity: sha512-7pXIJqJOq5tFgG1A2Zxti3Ht8jJF337m4sowbuHsW30ZnkQFnDzy9qBNhgzX8ZLW4+UBcXiiR7SwR6pokHsxiA==}
    dev: false

  /replicate@0.16.1:
    resolution: {integrity: sha512-7GlY2IRJd4CwXLSO+eTNCRjYFlT10pgHQjYnwCBczr4+qdErIZp4sAEuRFfhzXNU3Bi+sYJJLpcddnUOFxStNg==}
    engines: {git: '>=2.11.0', node: '>=18.0.0', npm: '>=7.19.0', yarn: '>=1.7.0'}
    dev: false

  /require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}
    dev: false

  /requizzle@0.2.4:
    resolution: {integrity: sha512-JRrFk1D4OQ4SqovXOgdav+K8EAhSB/LJZqCz8tbX0KObcdeM15Ss59ozWMBWmmINMagCwmqn4ZNryUGpBsl6Jw==}
    requiresBuild: true
    dependencies:
      lodash: 4.17.21
    dev: false
    optional: true

  /resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}
    dev: false

  /resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}
    dev: false

  /resolve@1.22.4:
    resolution: {integrity: sha512-PXNdCiPqDqeUou+w1C2eTQbNfxKSuMxqTCuvlmmMsk1NWHL5fRrhY6Pl0qEYYc6+QqGClco1Qj8XnjPego4wfg==}
    hasBin: true
    dependencies:
      is-core-module: 2.13.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  /retry-request@5.0.2:
    resolution: {integrity: sha512-wfI3pk7EE80lCIXprqh7ym48IHYdwmAAzESdbU8Q9l7pnRCk9LEhpbOTNKjz6FARLm/Bl5m+4F0ABxOkYUujSQ==}
    engines: {node: '>=12'}
    requiresBuild: true
    dependencies:
      debug: 4.3.4(supports-color@5.5.0)
      extend: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /retry@0.13.1:
    resolution: {integrity: sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==}
    engines: {node: '>= 4'}
    requiresBuild: true
    dev: false
    optional: true

  /reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  /rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    hasBin: true
    requiresBuild: true
    dependencies:
      glob: 7.2.3
    dev: false
    optional: true

  /rope-sequence@1.3.4:
    resolution: {integrity: sha512-UT5EDe2cu2E/6O4igUr5PSFs23nvvukicWHx6GnOPlHAiiYbzNuCRQCuiUdHJQcqKalLKlrYJnjY0ySGsXNQXQ==}
    dev: false

  /round-precision@1.0.0:
    resolution: {integrity: sha512-L2a0XDSNeaaBTEGmzuENMK4T8c0HqKYeS3pCDurW4MRPo8O6LeCLqVPWUt5+xW9rrEcG9QaYrAFcApEFXKziyw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-finite: 1.0.2
      is-integer: 1.0.7
    dev: false

  /round@2.0.1:
    resolution: {integrity: sha512-wzT6PF3wNEd2PCLTBQxteheeSwViBrD89E1XZjl4sj505C4LwTpqOQSNXLEROHDQw35NoylYbMxoUhgf2hb4qw==}
    dependencies:
      precision: 1.0.1
      round-precision: 1.0.0
    dev: false

  /run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3

  /sade@1.8.1:
    resolution: {integrity: sha512-xal3CZX1Xlo/k4ApwCFrHVACi9fBqJ7V+mwhBsuf/1IOKbBy098Fex+Wa/5QMubw09pSZ/u8EY8PWgevJsXp1A==}
    engines: {node: '>=6'}
    dependencies:
      mri: 1.2.0
    dev: false

  /safari-14-idb-fix@1.0.6:
    resolution: {integrity: sha512-oTEQOdMwRX+uCtWCKT1nx2gAeSdpr8elg/2gcaKUH00SJU2xWESfkx11nmXwTRHy7xfQoj1o4TTQvdmuBosTnA==}
    dev: false

  /safe-array-concat@1.0.0:
    resolution: {integrity: sha512-9dVEFruWIsnie89yym+xWTAYASdpw3CJV7Li/6zBewGf9z2i1j31rP6jnY0pHEO4QZh6N0K11bFjWmdR8UGdPQ==}
    engines: {node: '>=0.4'}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
      has-symbols: 1.0.3
      isarray: 2.0.5
    dev: false

  /safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}
    dev: false

  /safe-regex-test@1.0.0:
    resolution: {integrity: sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
      is-regex: 1.1.4
    dev: false

  /safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}
    dev: false

  /sass@1.63.6:
    resolution: {integrity: sha512-MJuxGMHzaOW7ipp+1KdELtqKbfAWbH7OLIdoSMnVe3EXPMTmxTmlaZDCTsgIpPCs3w99lLo9/zDKkOrJuT5byw==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      chokidar: 3.5.3
      immutable: 4.3.2
      source-map-js: 1.0.2
    dev: false

  /sax@1.3.0:
    resolution: {integrity: sha512-0s+oAmw9zLl1V1cS9BtZN7JAd0cW5e0QH4W3LWEK6a4LaLEA2OTpGYWDY+6XasBLtz6wkm3u1xRw95mRuJ59WA==}
    dev: false

  /scheduler@0.23.0:
    resolution: {integrity: sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /schema-utils@3.3.0:
    resolution: {integrity: sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
    dev: false

  /semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true
    dev: false

  /semver@7.5.4:
    resolution: {integrity: sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0
    dev: false

  /serialize-javascript@6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==}
    dependencies:
      randombytes: 2.1.0
    dev: false

  /shallow-equal@3.1.0:
    resolution: {integrity: sha512-pfVOw8QZIXpMbhBWvzBISicvToTiM5WBF1EeAUZDDSb5Dt29yl4AYbyywbJFSEsRUMr7gJaxqCdr4L3tQf9wVg==}
    dev: false

  /shallowequal@1.1.0:
    resolution: {integrity: sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==}
    dev: false

  /side-channel@1.0.4:
    resolution: {integrity: sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
      object-inspect: 1.12.3
    dev: false

  /source-map-js@1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}

  /source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    dev: false

  /source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /space-separated-tokens@1.1.5:
    resolution: {integrity: sha512-q/JSVd1Lptzhf5bkYm4ob4iWPjx0KiRe3sRFBNrVqbJkFaBm5vbbowy1mymoPNLRa52+oadOhJ+K49wsSeSjTA==}
    dev: false

  /space-separated-tokens@2.0.2:
    resolution: {integrity: sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==}
    dev: false

  /static-browser-server@1.0.3:
    resolution: {integrity: sha512-ZUyfgGDdFRbZGGJQ1YhiM930Yczz5VlbJObrQLlk24+qNHVQx4OlLcYswEUo3bIyNAbQUIUR9Yr5/Hqjzqb4zA==}
    dependencies:
      '@open-draft/deferred-promise': 2.2.0
      dotenv: 16.3.1
      mime-db: 1.52.0
      outvariant: 1.4.0
    dev: false

  /stream-events@1.0.5:
    resolution: {integrity: sha512-E1GUzBSgvct8Jsb3v2X15pjzN1tYebtbLaMg+eBOUOAxgbLoSbT2NS91ckc5lJD1KfLjId+jXJRgo0qnV5Nerg==}
    requiresBuild: true
    dependencies:
      stubs: 3.0.0
    dev: false
    optional: true

  /stream-shift@1.0.1:
    resolution: {integrity: sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ==}
    requiresBuild: true
    dev: false
    optional: true

  /streamsearch@1.1.0:
    resolution: {integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==}
    engines: {node: '>=10.0.0'}
    dev: false

  /strict-event-emitter@0.4.6:
    resolution: {integrity: sha512-12KWeb+wixJohmnwNFerbyiBrAlq5qJLwIt38etRtKtmmHyDSoGlIqFE9wx+4IwG0aDjI7GV8tc8ZccjWZZtTg==}
    dev: false

  /string-template@1.0.0:
    resolution: {integrity: sha512-SLqR3GBUXuoPP5MmYtD7ompvXiG87QjT6lzOszyXjTM86Uu7At7vNnt2xgyTLq5o9T4IxTYFyGxcULqpsmsfdg==}
    dev: false

  /string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: false

  /string.prototype.matchall@4.0.8:
    resolution: {integrity: sha512-6zOCOcJ+RJAQshcTvXPHoxoQGONa3e/Lqx90wUA+wEzX78sg5Bo+1tQo4N0pohS0erG9qtCqJDjNCQBjeWVxyg==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      get-intrinsic: 1.2.1
      has-symbols: 1.0.3
      internal-slot: 1.0.5
      regexp.prototype.flags: 1.5.0
      side-channel: 1.0.4
    dev: false

  /string.prototype.trim@1.2.7:
    resolution: {integrity: sha512-p6TmeT1T3411M8Cgg9wBTMRtY2q9+PNy9EV1i2lIXUN/btt763oIfxwN3RR8VU6wHX8j/1CFy0L+YuThm6bgOg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: false

  /string.prototype.trimend@1.0.6:
    resolution: {integrity: sha512-JySq+4mrPf9EsDBEDYMOb/lM7XQLulwg5R/m1r0PXEFqrV0qHvl58sdTilSXtKOflCsK2E8jxf+GKC0T07RWwQ==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: false

  /string.prototype.trimstart@1.0.6:
    resolution: {integrity: sha512-omqjMDaY92pbn5HOX7f9IccLA+U1tA9GvtU4JrodiXFfYB7jPzzHpRzpglLAjtUV6bB557zwClJezTqnAiYnQA==}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: false

  /string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}
    requiresBuild: true
    dependencies:
      safe-buffer: 5.2.1
    dev: false
    optional: true

  /stringify-entities@4.0.3:
    resolution: {integrity: sha512-BP9nNHMhhfcMbiuQKCqMjhDP5yBCAxsPu4pHFFzJ6Alo9dZgY4VLDPutXqIjpRiMoKdp7Av85Gr73Q5uH9k7+g==}
    dependencies:
      character-entities-html4: 2.1.0
      character-entities-legacy: 3.0.0
    dev: false

  /stringz@2.1.0:
    resolution: {integrity: sha512-KlywLT+MZ+v0IRepfMxRtnSvDCMc3nR1qqCs3m/qIbSOWkNZYT8XHQA31rS3TnKp0c5xjZu3M4GY/2aRKSi/6A==}
    dependencies:
      char-regex: 1.0.2
    dev: false

  /strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: false

  /strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}
    requiresBuild: true
    dev: false
    optional: true

  /strnum@1.0.5:
    resolution: {integrity: sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==}
    requiresBuild: true
    dev: false
    optional: true

  /stubs@3.0.0:
    resolution: {integrity: sha512-PdHt7hHUJKxvTCgbKX9C1V/ftOcjJQgz8BZwNfV5c4B6dcGqlpelTbJ999jBGZ2jYiPAwcX5dP6oBwVlBlUbxw==}
    requiresBuild: true
    dev: false
    optional: true

  /style-mod@4.1.0:
    resolution: {integrity: sha512-Ca5ib8HrFn+f+0n4N4ScTIA9iTOQ7MaGS1ylHcoVqW9J7w2w8PzN6g9gKmTYgGEBH8e120+RCmhpje6jC5uGWA==}
    dev: false

  /styled-components@5.3.11(@babel/core@7.24.3)(react-dom@18.2.0)(react-is@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-uuzIIfnVkagcVHv9nE0VPlHPSCmXIUGKfJ42LNjxCCTDTL5sgnJ8Z7GZBq0EnLYGln77tPpEpExt2+qa+cZqSw==}
    engines: {node: '>=10'}
    peerDependencies:
      react: '>= 16.8.0'
      react-dom: '>= 16.8.0'
      react-is: '>= 16.8.0'
    dependencies:
      '@babel/helper-module-imports': 7.22.5
      '@babel/traverse': 7.22.10(supports-color@5.5.0)
      '@emotion/is-prop-valid': 1.2.1
      '@emotion/stylis': 0.8.5
      '@emotion/unitless': 0.7.5
      babel-plugin-styled-components: 2.1.4(@babel/core@7.24.3)(styled-components@5.3.11)
      css-to-react-native: 3.2.0
      hoist-non-react-statics: 3.3.2
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 18.2.0
      shallowequal: 1.1.0
      supports-color: 5.5.0
    transitivePeerDependencies:
      - '@babel/core'
    dev: false

  /styled-jsx@5.1.1(@babel/core@7.24.3)(react@18.2.0):
    resolution: {integrity: sha512-pW7uC1l4mBZ8ugbiZrcIsiIvVx1UmTfw7UkC3Um2tmfUq9Bhk8IiyEIPl6F8agHgjzku6j0xQEZbfA5uSgSaCw==}
    engines: {node: '>= 12.0.0'}
    peerDependencies:
      '@babel/core': '*'
      babel-plugin-macros: '*'
      react: '>= 16.8.0 || 17.x.x || ^18.0.0-0'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      babel-plugin-macros:
        optional: true
    dependencies:
      '@babel/core': 7.24.3
      client-only: 0.0.1
      react: 18.2.0
    dev: false

  /styled-tools@1.7.2:
    resolution: {integrity: sha512-IjLxzM20RMwAsx8M1QoRlCG/Kmq8lKzCGyospjtSXt/BTIIcvgTonaxQAsKnBrsZNwhpHzO9ADx5te0h76ILVg==}
    dev: false

  /stylis@4.2.0:
    resolution: {integrity: sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==}
    dev: false

  /sucrase@3.34.0:
    resolution: {integrity: sha512-70/LQEZ07TEcxiU2dz51FKaE6hCTWC6vr7FOk3Gr0U60C3shtAN+H+BFr9XlYe5xqf3RA8nrc+VIwzCfnxuXJw==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      commander: 4.1.1
      glob: 7.1.6
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  /supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: false

  /supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: false

  /supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}
    dependencies:
      has-flag: 4.0.0
    dev: false

  /supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  /svgmoji@3.2.0:
    resolution: {integrity: sha512-tjmdQhIju2ZQ81FLBlPngg1aWMOhQjP9ErXb2ROikM0aBGA/hqI0/DN/5J0sDsXzJPHmODpSFhWfiSsUieU3bA==}
    dependencies:
      '@babel/runtime': 7.22.10
      '@svgmoji/blob': 3.2.0
      '@svgmoji/core': 3.2.0
      '@svgmoji/noto': 3.2.0
      '@svgmoji/openmoji': 3.2.0
      '@svgmoji/twemoji': 3.2.0
    dev: false

  /tabbable@6.2.0:
    resolution: {integrity: sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==}
    dev: false

  /tailwind-merge@1.14.0:
    resolution: {integrity: sha512-3mFKyCo/MBcgyOTlrY8T7odzZFx+w+qKSMAmdFzRvqBfLlSigU6TZnlFHK0lkMwj9Bj8OYU+9yW9lmGuS0QEnQ==}
    dev: false

  /tailwindcss-animate@1.0.6(tailwindcss@3.3.2):
    resolution: {integrity: sha512-4WigSGMvbl3gCCact62ZvOngA+PRqhAn7si3TQ3/ZuPuQZcIEtVap+ENSXbzWhpojKB8CpvnIsrwBu8/RnHtuw==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders'
    dependencies:
      tailwindcss: 3.3.2
    dev: false

  /tailwindcss@3.3.2:
    resolution: {integrity: sha512-9jPkMiIBXvPc2KywkraqsUfbfj+dHDb+JPWtSJa9MLFdrPyazI7q6WX2sUrm7R9eVR7qqv3Pas7EvQFzxKnI6w==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.5.3
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.1
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.19.1
      lilconfig: 2.1.0
      micromatch: 4.0.5
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.0.0
      postcss: 8.4.24
      postcss-import: 15.1.0(postcss@8.4.24)
      postcss-js: 4.0.1(postcss@8.4.24)
      postcss-load-config: 4.0.1(postcss@8.4.24)
      postcss-nested: 6.0.1(postcss@8.4.24)
      postcss-selector-parser: 6.0.13
      postcss-value-parser: 4.2.0
      resolve: 1.22.4
      sucrase: 3.34.0
    transitivePeerDependencies:
      - ts-node

  /tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}
    dev: false

  /teeny-request@8.0.3(encoding@0.1.13):
    resolution: {integrity: sha512-jJZpA5He2y52yUhA7pyAGZlgQpcB+xLjcN0eUFxr9c8hP/H7uOXbBNVo/O0C/xVfJLJs680jvkFgVJEEvk9+ww==}
    engines: {node: '>=12'}
    requiresBuild: true
    dependencies:
      http-proxy-agent: 5.0.0
      https-proxy-agent: 5.0.1
      node-fetch: 2.6.12(encoding@0.1.13)
      stream-events: 1.0.5
      uuid: 9.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color
    dev: false
    optional: true

  /terser-webpack-plugin@5.3.10(webpack@5.91.0):
    resolution: {integrity: sha512-BKFPWlPDndPs+NGGCr1U59t0XScL5317Y0UReNrHaw9/FwhPENlq6bfgs+4yPfyP51vqC1bQ4rp1EfXW5ZSH9w==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      jest-worker: 27.5.1
      schema-utils: 3.3.0
      serialize-javascript: 6.0.2
      terser: 5.29.2
      webpack: 5.91.0
    dev: false

  /terser@5.29.2:
    resolution: {integrity: sha512-ZiGkhUBIM+7LwkNjXYJq8svgkd+QK3UUr0wJqY4MieaezBSAIPgbSPZyIx0idM6XWK5CMzSWa8MJIzmRcB8Caw==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.11.3
      commander: 2.20.3
      source-map-support: 0.5.21
    dev: false

  /text-decoding@1.0.0:
    resolution: {integrity: sha512-/0TJD42KDnVwKmDK6jj3xP7E2MG7SHAOG4tyTgyUCRPdHwvkquYNLEQltmdMa3owq3TkddCVcTsoctJI8VQNKA==}
    dev: false

  /thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}
    dependencies:
      thenify: 3.3.1

  /thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}
    dependencies:
      any-promise: 1.3.0

  /throttle-debounce@3.0.1:
    resolution: {integrity: sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg==}
    engines: {node: '>=10'}
    dev: false

  /tiny-warning@1.0.3:
    resolution: {integrity: sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==}
    dev: false

  /tinycolor2@1.6.0:
    resolution: {integrity: sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==}
    dev: false

  /tippy.js@6.3.7:
    resolution: {integrity: sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ==}
    dependencies:
      '@popperjs/core': 2.11.8
    dev: false

  /tmp@0.2.1:
    resolution: {integrity: sha512-76SUhtfqR2Ijn+xllcI5P1oyannHNHByD80W1q447gU3mp9G9PSpGdWmjUOHRDPiHYacIk66W7ubDTuPF3BEtQ==}
    engines: {node: '>=8.17.0'}
    requiresBuild: true
    dependencies:
      rimraf: 3.0.2
    dev: false
    optional: true

  /to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}
    dev: false

  /to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0

  /tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}
    dev: false

  /trim-lines@3.0.1:
    resolution: {integrity: sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==}
    dev: false

  /trim-trailing-lines@2.1.0:
    resolution: {integrity: sha512-5UR5Biq4VlVOtzqkm2AZlgvSlDJtME46uV0br0gENbwN4l5+mMKT4b9gJKqWtuL2zAIqajGJGuvbCbcAJUZqBg==}
    dev: false

  /trough@2.1.0:
    resolution: {integrity: sha512-AqTiAOLcj85xS7vQ8QkAV41hPDIJ71XJB4RCUrzo/1GM2CQwhkJGaf9Hgr7BOugMRpgGUrqRg/DrBDl4H40+8g==}
    dev: false

  /ts-interface-checker@0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}

  /tslib@2.5.0:
    resolution: {integrity: sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==}
    dev: false

  /tslib@2.6.1:
    resolution: {integrity: sha512-t0hLfiEKfMUoqhG+U1oid7Pva4bbDPHYfJNiB7BiIjRkj1pyC++4N3huJfqY6aRH6VTB0rvtzQwjM4K6qpfOig==}
    dev: false

  /turndown-plugin-gfm@1.0.2:
    resolution: {integrity: sha512-vwz9tfvF7XN/jE0dGoBei3FXWuvll78ohzCZQuOb+ZjWrs3a0XhQVomJEb2Qh4VHTPNRO4GPZh0V7VRbiWwkRg==}
    dev: false

  /turndown@7.1.2:
    resolution: {integrity: sha512-ntI9R7fcUKjqBP6QU8rBK2Ehyt8LAzt3UBT9JR9tgo6GtuKvyUzpayWmeMKJw1DPdXzktvtIT8m2mVXz+bL/Qg==}
    dependencies:
      domino: 2.1.6
    dev: false

  /type-check@0.3.2:
    resolution: {integrity: sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
    dev: false

  /type-fest@1.4.0:
    resolution: {integrity: sha512-yGSza74xk0UG8k+pLh5oeoYirvIiWo5t0/o3zHHAO2tRDiZcxWP7fywNlXhqb6/r6sWvwi+RsyQMWhVLe4BVuA==}
    engines: {node: '>=10'}
    dev: false

  /type-fest@2.19.0:
    resolution: {integrity: sha512-RAH822pAdBgcNMAfWnCBU3CFZcfZ/i1eZjwFU/dsLKumyuuP3niueg2UAukXYF0E2AAoc82ZSSf9J0WQBinzHA==}
    engines: {node: '>=12.20'}
    dev: false

  /type@1.2.0:
    resolution: {integrity: sha512-+5nt5AAniqsCnu2cEQQdpzCAh33kVx8n0VoFidKpB1dVVLAN/F+bgVOqOJqOnEnrhp222clB5p3vUlD+1QAnfg==}
    dev: false

  /type@2.7.2:
    resolution: {integrity: sha512-dzlvlNlt6AXU7EBSfpAscydQ7gXB+pPGsPnfJnZpiNJBDj7IaJzQlBZYGdEi4R9HmPdBv2XmWJ6YUtoTa7lmCw==}
    dev: false

  /typed-array-buffer@1.0.0:
    resolution: {integrity: sha512-Y8KTSIglk9OZEr8zywiIHG/kmQ7KWyjseXs1CbSo8vC42w7hg2HgYTxSWwP0+is7bWDc1H+Fo026CpHFwm8tkw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      get-intrinsic: 1.2.1
      is-typed-array: 1.1.12
    dev: false

  /typed-array-byte-length@1.0.0:
    resolution: {integrity: sha512-Or/+kvLxNpeQ9DtSydonMxCx+9ZXOswtwJn17SNLvhptaXYDJvkFFP5zbfU/uLmvnBJlI4yrnXRxpdWH/M5tNA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      for-each: 0.3.3
      has-proto: 1.0.1
      is-typed-array: 1.1.12
    dev: false

  /typed-array-byte-offset@1.0.0:
    resolution: {integrity: sha512-RD97prjEt9EL8YgAgpOkf3O4IF9lhJFr9g0htQkm0rchFp/Vx7LW5Q8fSXXub7BXAODyUQohRMyOc3faCPd0hg==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      for-each: 0.3.3
      has-proto: 1.0.1
      is-typed-array: 1.1.12
    dev: false

  /typed-array-length@1.0.4:
    resolution: {integrity: sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==}
    dependencies:
      call-bind: 1.0.2
      for-each: 0.3.3
      is-typed-array: 1.1.12
    dev: false

  /typescript@5.1.6:
    resolution: {integrity: sha512-zaWCozRZ6DLEWAWFrVDz1H6FVXzUSfTy5FUMWsQlU8Ym5JP9eO4xkTIROFCQvhQf61z6O/G6ugw3SgAnvvm+HA==}
    engines: {node: '>=14.17'}
    hasBin: true
    dev: false

  /typescript@5.2.2:
    resolution: {integrity: sha512-mI4WrpHsbCIcwT9cF4FZvr80QUeKvsUsUvKDoR+X/7XHQH98xYD8YHZg7ANtz2GtZt/CBq2QJ0thkGJMHfqc1w==}
    engines: {node: '>=14.17'}
    hasBin: true
    dev: false

  /uc.micro@1.0.6:
    resolution: {integrity: sha512-8Y75pvTYkLJW2hWQHXxoqRgV7qb9B+9vFEtidML+7koHUFapnVJAZ6cKs+Qjz5Aw3aZWHMC6u0wJE3At+nSGwA==}
    dev: false

  /uc.micro@2.1.0:
    resolution: {integrity: sha512-ARDJmphmdvUk6Glw7y9DQ2bFkKBHwQHLi2lsaH6PPmz/Ka9sFOBsBluozhDltWmnv9u/cF6Rt87znRTPV+yp/A==}
    dev: false

  /uglify-js@3.17.4:
    resolution: {integrity: sha512-T9q82TJI9e/C1TAxYvfb16xO120tMVFZrGA3f9/P4424DNu6ypK103y0GPFVa17yotwSyZW5iYXgjYHkGrJW/g==}
    engines: {node: '>=0.8.0'}
    hasBin: true
    requiresBuild: true
    dev: false
    optional: true

  /unbox-primitive@1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}
    dependencies:
      call-bind: 1.0.2
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2
    dev: false

  /underscore@1.13.6:
    resolution: {integrity: sha512-+A5Sja4HP1M08MaXya7p5LvjuM7K6q/2EaC0+iovj/wOcMsTzMvDFbasi/oSapiwOlt252IqsKqPjCl7huKS0A==}
    requiresBuild: true
    dev: false
    optional: true

  /unidiff@1.0.4:
    resolution: {integrity: sha512-ynU0vsAXw0ir8roa+xPCUHmnJ5goc5BTM2Kuc3IJd8UwgaeRs7VSD5+eeaQL+xp1JtB92hu/Zy/Lgy7RZcr1pQ==}
    dependencies:
      diff: 5.1.0
    dev: false

  /unified@10.1.2:
    resolution: {integrity: sha512-pUSWAi/RAnVy1Pif2kAoeWNBa3JVrx0MId2LASj8G+7AiHWoKZNTomq6LG326T68U7/e263X6fTdcXIy7XnF7Q==}
    dependencies:
      '@types/unist': 2.0.7
      bail: 2.0.2
      extend: 3.0.2
      is-buffer: 2.0.5
      is-plain-obj: 4.1.0
      trough: 2.1.0
      vfile: 5.3.7
    dev: false

  /unist-util-find-after@4.0.1:
    resolution: {integrity: sha512-QO/PuPMm2ERxC6vFXEPtmAutOopy5PknD+Oq64gGwxKtk4xwo9Z97t9Av1obPmGU0IyTa6EKYUfTrK2QJS3Ozw==}
    dependencies:
      '@types/unist': 2.0.7
      unist-util-is: 5.2.1
    dev: false

  /unist-util-generated@2.0.1:
    resolution: {integrity: sha512-qF72kLmPxAw0oN2fwpWIqbXAVyEqUzDHMsbtPvOudIlUzXYFIeQIuxXQCRCFh22B7cixvU0MG7m3MW8FTq/S+A==}
    dev: false

  /unist-util-is@5.2.1:
    resolution: {integrity: sha512-u9njyyfEh43npf1M+yGKDGVPbY/JWEemg5nH05ncKPfi+kBbKBJoTdsogMu33uhytuLlv9y0O7GH7fEdwLdLQw==}
    dependencies:
      '@types/unist': 2.0.7
    dev: false

  /unist-util-position-from-estree@1.1.2:
    resolution: {integrity: sha512-poZa0eXpS+/XpoQwGwl79UUdea4ol2ZuCYguVaJS4qzIOMDzbqz8a3erUCOmubSZkaOuGamb3tX790iwOIROww==}
    dependencies:
      '@types/unist': 2.0.7
    dev: false

  /unist-util-position@4.0.4:
    resolution: {integrity: sha512-kUBE91efOWfIVBo8xzh/uZQ7p9ffYRtUbMRZBNFYwf0RK8koUMx6dGUfwylLOKmaT2cs4wSW96QoYUSXAyEtpg==}
    dependencies:
      '@types/unist': 2.0.7
    dev: false

  /unist-util-remove-position@4.0.2:
    resolution: {integrity: sha512-TkBb0HABNmxzAcfLf4qsIbFbaPDvMO6wa3b3j4VcEzFVaw1LBKwnW4/sRJ/atSLSzoIg41JWEdnE7N6DIhGDGQ==}
    dependencies:
      '@types/unist': 2.0.7
      unist-util-visit: 4.1.2
    dev: false

  /unist-util-stringify-position@3.0.3:
    resolution: {integrity: sha512-k5GzIBZ/QatR8N5X2y+drfpWG8IDBzdnVj6OInRNWm1oXrzydiaAT2OQiA8DPRRZyAKb9b6I2a6PxYklZD0gKg==}
    dependencies:
      '@types/unist': 2.0.7
    dev: false

  /unist-util-visit-parents@5.1.3:
    resolution: {integrity: sha512-x6+y8g7wWMyQhL1iZfhIPhDAs7Xwbn9nRosDXl7qoPTSCy0yNxnKc+hWokFifWQIDGi154rdUqKvbCa4+1kLhg==}
    dependencies:
      '@types/unist': 2.0.7
      unist-util-is: 5.2.1
    dev: false

  /unist-util-visit@4.1.2:
    resolution: {integrity: sha512-MSd8OUGISqHdVvfY9TPhyK2VdUrPgxkUtWSuMHF6XAAFuL4LokseigBnZtPnJMu+FbynTkFNnFlyjxpVKujMRg==}
    dependencies:
      '@types/unist': 2.0.7
      unist-util-is: 5.2.1
      unist-util-visit-parents: 5.1.3
    dev: false

  /unraw@2.0.1:
    resolution: {integrity: sha512-tdOvLfRzHolwYcHS6HIX860MkK9LQ4+oLuNwFYL7bpgTEO64PZrcQxkisgwJYCfF8sKiWLwwu1c83DvMkbefIQ==}
    dev: false

  /update-browserslist-db@1.0.11(browserslist@4.21.10):
    resolution: {integrity: sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.21.10
      escalade: 3.1.1
      picocolors: 1.0.0
    dev: false

  /update-browserslist-db@1.0.13(browserslist@4.23.0):
    resolution: {integrity: sha512-xebP81SNcPuNpPP3uzeW1NYXxI3rxyJzF3pD6sH4jE7o/IX+WtSpwnVU+qIsDPyk0d3hmFQ7mjqc6AtV604hbg==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.23.0
      escalade: 3.1.2
      picocolors: 1.0.0
    dev: false

  /uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.3.1
    dev: false

  /use-callback-ref@1.3.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-3FT9PRuRdbB9HfXhEq35u4oZkvpJ5kuYbpqhCfmiZyReuRgpnhDlbr2ZEnnuS0RrJAPn6l23xjFg9kpDM+Ms7w==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
      tslib: 2.6.1
    dev: false

  /use-composed-ref@1.3.0(react@18.2.0):
    resolution: {integrity: sha512-GLMG0Jc/jiKov/3Ulid1wbv3r54K9HlMW29IWcDFPEqFkSO2nS0MuefWgMJpeHQ9YJeXDL3ZUF+P3jdXlZX/cQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /use-isomorphic-layout-effect@1.1.2(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-49L8yCO3iGT/ZF9QttjwLF/ZD9Iwto5LnH5LmEdk/6cFmXddqi2ulF0edxTwjj+7mqvpVVGQWvbXZdn32wRSHA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
    dev: false

  /use-latest@1.2.1(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-xA+AVm/Wlg3e2P/JiItTziwS7FK92LWrDB0p+hgXloIMuVCeJJ8v6f0eeHyPZaJrM+usM1FkFfbNCrJGs8A/zw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
      use-isomorphic-layout-effect: 1.1.2(@types/react@18.2.14)(react@18.2.0)
    dev: false

  /use-previous@1.2.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-tK7Ne779nqTKGeh0rsFvxnQcEqePFRYlM0rfmNy9JH+h+2ndja7P0017nda0Q1gkqfcOD//pKZbDyyLIUH2s+Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
      use-isomorphic-layout-effect: 1.1.2(@types/react@18.2.14)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /use-sidecar@1.1.2(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-epTbsLuzZ7lPClpz2TyryBfztm7m+28DlEv2ZCQ3MDr5ssiwyOwGH/e5F9CkfWjJ1t4clvI58yF822/GUkjjhw==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.9.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.2.14
      detect-node-es: 1.1.0
      react: 18.2.0
      tslib: 2.6.1
    dev: false

  /use-sync-external-store@1.2.0(react@18.2.0):
    resolution: {integrity: sha512-eEgnFxGQ1Ife9bzYs6VLi8/4X6CObHMw9Qr9tPY43iKwsPw8xE8+EFsf/2cFZ5S3esXgpWgtSCtLNS41F+sKPA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  /util@0.12.5:
    resolution: {integrity: sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==}
    dependencies:
      inherits: 2.0.4
      is-arguments: 1.1.1
      is-generator-function: 1.0.10
      is-typed-array: 1.1.12
      which-typed-array: 1.1.11
    dev: false

  /uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true
    dev: false

  /uuid@9.0.0:
    resolution: {integrity: sha512-MXcSTerfPa4uqyzStbRoTgt5XIe3x5+42+q1sDuy3R5MDk66URdLMOZe5aPX/SQd+kuYAh0FdP/pO28IkQyTeg==}
    hasBin: true
    dev: false

  /uvu@0.5.6:
    resolution: {integrity: sha512-+g8ENReyr8YsOc6fv/NVJs2vFdHBnBNdfE49rshrTzDWOlUx4Gq7KOS2GD8eqhy2j+Ejq29+SbKH8yjkAqXqoA==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      dequal: 2.0.3
      diff: 5.1.0
      kleur: 4.1.5
      sade: 1.8.1
    dev: false

  /validator@13.11.0:
    resolution: {integrity: sha512-Ii+sehpSfZy+At5nPdnyMhx78fEoPDkR2XW/zimHEL3MyGJQOCQ7WeP20jPYRz7ZCpcKLB21NxuXHF3bxjStBQ==}
    engines: {node: '>= 0.10'}
    dev: false

  /vfile-location@4.1.0:
    resolution: {integrity: sha512-YF23YMyASIIJXpktBa4vIGLJ5Gs88UB/XePgqPmTa7cDA+JeO3yclbpheQYCHjVHBn/yePzrXuygIL+xbvRYHw==}
    dependencies:
      '@types/unist': 2.0.7
      vfile: 5.3.7
    dev: false

  /vfile-message@3.1.4:
    resolution: {integrity: sha512-fa0Z6P8HUrQN4BZaX05SIVXic+7kE3b05PWAtPuYP9QLHsLKYR7/AlLW3NtOrpXRLeawpDLMsVkmk5DG0NXgWw==}
    dependencies:
      '@types/unist': 2.0.7
      unist-util-stringify-position: 3.0.3
    dev: false

  /vfile@5.3.7:
    resolution: {integrity: sha512-r7qlzkgErKjobAmyNIkkSpizsFPYiUPuJb5pNW1RB4JcYVZhs4lIbVqk8XPk033CV/1z8ss5pkax8SuhGpcG8g==}
    dependencies:
      '@types/unist': 2.0.7
      is-buffer: 2.0.5
      unist-util-stringify-position: 3.0.3
      vfile-message: 3.1.4
    dev: false

  /w3c-keyname@2.2.8:
    resolution: {integrity: sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ==}
    dev: false

  /warning@4.0.3:
    resolution: {integrity: sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /watchpack@2.4.0:
    resolution: {integrity: sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==}
    engines: {node: '>=10.13.0'}
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
    dev: false

  /watchpack@2.4.1:
    resolution: {integrity: sha512-8wrBCMtVhqcXP2Sup1ctSkga6uc2Bx0IIvKyT7yTFier5AXHooSI+QyQQAtTb7+E0IUCCKyTFmXqdqgum2XWGg==}
    engines: {node: '>=10.13.0'}
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
    dev: false

  /web-namespaces@2.0.1:
    resolution: {integrity: sha512-bKr1DkiNa2krS7qxNtdrtHAmzuYGFQLiQ13TsorsdT6ULTkPLKuu5+GsFpDlg6JFjUTwX2DyhMPG2be8uPrqsQ==}
    dev: false

  /web-streams-polyfill@4.0.0-beta.3:
    resolution: {integrity: sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==}
    engines: {node: '>= 14'}
    dev: false

  /webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}
    dev: false

  /webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}
    dev: false

  /webpack@5.91.0:
    resolution: {integrity: sha512-rzVwlLeBWHJbmgTC/8TvAcu5vpJNII+MelQpylD4jNERPwpBJOE2lEcko1zJX3QJeLjTTAnQxn/OJ8bjDzVQaw==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.5
      '@webassemblyjs/ast': 1.12.1
      '@webassemblyjs/wasm-edit': 1.12.1
      '@webassemblyjs/wasm-parser': 1.12.1
      acorn: 8.11.3
      acorn-import-assertions: 1.9.0(acorn@8.11.3)
      browserslist: 4.23.0
      chrome-trace-event: 1.0.3
      enhanced-resolve: 5.16.0
      es-module-lexer: 1.4.2
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 3.3.0
      tapable: 2.2.1
      terser-webpack-plugin: 5.3.10(webpack@5.91.0)
      watchpack: 2.4.1
      webpack-sources: 3.2.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js
    dev: false

  /websocket-driver@0.7.4:
    resolution: {integrity: sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==}
    engines: {node: '>=0.8.0'}
    dependencies:
      http-parser-js: 0.5.8
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4
    dev: false

  /websocket-extensions@0.1.4:
    resolution: {integrity: sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==}
    engines: {node: '>=0.8.0'}
    dev: false

  /whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1
    dev: false

  /which-boxed-primitive@1.0.2:
    resolution: {integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==}
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4
    dev: false

  /which-typed-array@1.1.11:
    resolution: {integrity: sha512-qe9UWWpkeG5yzZ0tNYxDmd7vo58HDBc39mZ0xWWpolAGADdFOzkfamWLDxkOWcvHQKVmdTyQdLD4NOfjLWTKew==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.0
    dev: false

  /wink-nlp@1.14.3:
    resolution: {integrity: sha512-lvY5iCs3T8I34F8WKS70+2P0U9dWLn3vdPf/Z+m2VK14N7OmqnPzmHfh3moHdusajoQ37Em39z0IZB9K4x/96A==}
    dev: false

  /word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: false

  /wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  /xml2js@0.6.2:
    resolution: {integrity: sha512-T4rieHaC1EXcES0Kxxj4JWgaUQHDk+qwHcYOCFHfiwKz7tOVPLq7Hjq9dM1WCMhylqMEfP7hMcOIChvotiZegA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      sax: 1.3.0
      xmlbuilder: 11.0.1
    dev: false

  /xmlbuilder@11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}
    dev: false

  /xmlcreate@2.0.4:
    resolution: {integrity: sha512-nquOebG4sngPmGPICTS5EnxqhKbCmz5Ox5hsszI2T6U5qdrJizBc+0ilYSEjTSzU0yZcmvppztXe/5Al5fUwdg==}
    requiresBuild: true
    dev: false
    optional: true

  /xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}
    dev: false

  /y-prosemirror@1.0.20(prosemirror-model@1.19.3)(prosemirror-state@1.4.3)(prosemirror-view@1.31.7)(y-protocols@1.0.5)(yjs@13.6.7):
    resolution: {integrity: sha512-LVMtu3qWo0emeYiP+0jgNcvZkqhzE/otOoro+87q0iVKxy/sMKuiJZnokfJdR4cn9qKx0Un5fIxXqbAlR2bFkA==}
    peerDependencies:
      prosemirror-model: ^1.7.1
      prosemirror-state: ^1.2.3
      prosemirror-view: ^1.9.10
      y-protocols: ^1.0.1
      yjs: ^13.3.2
    dependencies:
      lib0: 0.2.80
      prosemirror-model: 1.19.3
      prosemirror-state: 1.4.3
      prosemirror-view: 1.31.7
      y-protocols: 1.0.5
      yjs: 13.6.7
    dev: false

  /y-prosemirror@1.0.20(prosemirror-model@1.19.4)(prosemirror-state@1.4.3)(prosemirror-view@1.33.2)(y-protocols@1.0.5)(yjs@13.6.14):
    resolution: {integrity: sha512-LVMtu3qWo0emeYiP+0jgNcvZkqhzE/otOoro+87q0iVKxy/sMKuiJZnokfJdR4cn9qKx0Un5fIxXqbAlR2bFkA==}
    peerDependencies:
      prosemirror-model: ^1.7.1
      prosemirror-state: ^1.2.3
      prosemirror-view: ^1.9.10
      y-protocols: ^1.0.1
      yjs: ^13.3.2
    dependencies:
      lib0: 0.2.80
      prosemirror-model: 1.19.4
      prosemirror-state: 1.4.3
      prosemirror-view: 1.33.2
      y-protocols: 1.0.5
      yjs: 13.6.14
    dev: false

  /y-protocols@1.0.5:
    resolution: {integrity: sha512-Wil92b7cGk712lRHDqS4T90IczF6RkcvCwAD0A2OPg+adKmOe+nOiT/N2hvpQIWS3zfjmtL4CPaH5sIW1Hkm/A==}
    dependencies:
      lib0: 0.2.80
    dev: false

  /y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}
    dev: false

  /yallist@2.1.2:
    resolution: {integrity: sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==}
    dev: false

  /yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}
    dev: false

  /yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}
    requiresBuild: true
    dev: false

  /yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}
    dev: false

  /yaml@2.3.1:
    resolution: {integrity: sha512-2eHWfjaoXgTBC2jNM1LRef62VQa0umtvRiDSk6HSzW7RvS5YtkabJrwYLLEKWBc8a5U2PTSCs+dJjUTJdlHsWQ==}
    engines: {node: '>= 14'}

  /yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}
    dev: false

  /yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}
    dev: false

  /yargs@16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9
    dev: false

  /yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}
    dependencies:
      cliui: 8.0.1
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1
    dev: false

  /yjs@13.6.14:
    resolution: {integrity: sha512-D+7KcUr0j+vBCUSKXXEWfA+bG4UQBviAwP3gYBhkstkgwy5+8diOPMx0iqLIOxNo/HxaREUimZRxqHGAHCL2BQ==}
    engines: {node: '>=16.0.0', npm: '>=8.0.0'}
    dependencies:
      lib0: 0.2.93
    dev: false

  /yjs@13.6.7:
    resolution: {integrity: sha512-mCZTh4kjvUS2DnaktsYN6wLH3WZCJBLqrTdkWh1bIDpA/sB/GNFaLA/dyVJj2Hc7KwONuuoC/vWe9bwBBosZLQ==}
    engines: {node: '>=16.0.0', npm: '>=8.0.0'}
    dependencies:
      lib0: 0.2.80
    dev: false

  /yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}
    requiresBuild: true
    dev: false

  /zod@3.21.4:
    resolution: {integrity: sha512-m46AKbrzKVzOzs/DZgVnG5H55N1sv1M8qZU3A8RIKbs3mrACDNeIOeilDymVb2HdmP8uwshOCF4uJ8uM9rCqJw==}
    dev: false

  /zustand-computed@1.3.5(react@18.2.0)(zustand@4.4.0):
    resolution: {integrity: sha512-FDSpAfx6GLl4L6WkJNuut1deph7WzTXRslMTCl5lPiufL49ct/T4jE7IPJF8k1WWRuT4YBaNeN3liORaCTkGZw==}
    peerDependencies:
      react: ^18.2.0
      zustand: ^4.1.4
    dependencies:
      react: 18.2.0
      zustand: 4.4.0(@types/react@18.2.14)(react@18.2.0)
    dev: false

  /zustand@4.4.0(@types/react@18.2.14)(react@18.2.0):
    resolution: {integrity: sha512-2dq6wq4dSxbiPTamGar0NlIG/av0wpyWZJGeQYtUOLegIUvhM2Bf86ekPlmgpUtS5uR7HyetSiktYrGsdsyZgQ==}
    engines: {node: '>=12.7.0'}
    peerDependencies:
      '@types/react': '>=16.8'
      immer: '>=9.0'
      react: '>=16.8'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true
    dependencies:
      '@types/react': 18.2.14
      react: 18.2.0
      use-sync-external-store: 1.2.0(react@18.2.0)
    dev: false

  /zwitch@2.0.4:
    resolution: {integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==}
    dev: false
