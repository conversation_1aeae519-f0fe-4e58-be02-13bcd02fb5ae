import {
  addDoc,
  doc,
  getDoc,
  query,
  onSnapshot,
  collection,
  Query,
  DocumentData,
  where,
  orderBy,
  limit,
} from "firebase/firestore";
import { db } from "@/services/firebase.config";
import { User } from "firebase/auth";
import { useEffect } from "react";
import { Tenant } from "@/models/tenant";

export const loadUserData = async (
  user: User,
  setUserName: any,
  setTenantsList: any
) => {
  const userDocument = doc(db, "users", user?.uid || "");
  const userValue = await getDoc(userDocument);

  if (userValue) {
    setUserName(userValue.data()?.name || "");
  }

  const q = query(collection(db, "tenants"));
  const unsubscribe = onSnapshot(q, (querySnapshot) => {
    const tenants = [] as any;
    querySnapshot.forEach((doc) => {
      tenants.push({
        id: doc.id,
        ...doc.data(),
      });
    });
    setTenantsList(tenants);
  });

  return [unsubscribe];
};

export const getDocumentsQuery = (selectedClient: any): Query<DocumentData> => {
  return query(collection(db, "tenants", selectedClient.value, "documents"));
};

export const getChatsQuery = (selectedClient: any): Query<DocumentData> => {
  return query(
    collection(db, "tenants", selectedClient.value, "chatSessions"),
    orderBy("lastUpdated", "desc"),
    limit(20)
  );
};

export const getReportsQuery = (
  selectedClient: any
): Query<DocumentData> | null => {
  if (!selectedClient) {
    return null;
  }

  return query(
    collection(db, "tenants", selectedClient.value, "reports"),
    orderBy("createdAt", "desc"),
    limit(50)
  );
};

const addTenant = async (tenant: Tenant) => {
  const docRef = await addDoc(collection(db, "tenants"), tenant);
};

export { addTenant };
