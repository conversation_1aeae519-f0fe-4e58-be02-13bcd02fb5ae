from llama_index.core.settings import Settings
from repositories.pages_repository import PagesRepository
from google.cloud.firestore import Client
from utilities.opengraph_fetcher import OpenGraphFetcher
from services.LLMService import LLMService
import json

class LinksFetcher:
    """Utility class for detecting if a message is chitchat"""

    def __init__(self, database: Client, llm_service: LLMService):
        self.database = database
        self.llm_service = llm_service

    def get_links(self, tenant_id: str, text: str, response: str):
        
        try:
        
            pages_repo = PagesRepository(self.database)
            
            all_pages = pages_repo.get_pages(tenant_id)
            all_pages_keys = all_pages.keys()
            
            all_pages_bullets = ""
            
            for page_key in all_pages_keys:
                page = all_pages[page_key]
                all_pages_bullets += f"""
                - {page["title"]} : {page["url"]}
                """
            
            prompt = f"""
            Consider the following sources:
            {all_pages_bullets}
            
            ======
            And consider this question and reply:
            Question: {text}
            Reply: {response}
            
            Find the best matching links and page names. You can put up to three links.
            
            Only give the links, nothing else, in the following JSON format:
            {{
            "links": [
                {{
                "url": "https://url.com/the-page",
                "name": "The Page"
                }},
                {{
                "url": "https://url.com/the-page",
                "name": "The Page"
                }},
                {{
                "url": "https://url.com/the-page",
                "name": "The Page"
                }}
            ]
            }}
            
            - If the page name contains the name of the website, remove that part (eg. "The Page - Website Name", "Wikipedia: The Page" should be "The Page").
            - Do NOT use ```json```, directly put the JSON object.

            """
            
            links_response = self.llm_service.chat(prompt, [], "gpt-4o-mini")
            
            response_cleaned = links_response.replace("```json", "")
            response_cleaned = response_cleaned.replace("```", "")
            
            json_data = json.loads(response_cleaned)
            
            # Fetch and add the OpenGraph images
            # links_with_images = OpenGraphFetcher.get_og_images(json_data)
            
            return self.verify_links(json_data, all_pages)
        
        except Exception as e:
            # Return empty list if there is an error
            return {'links': []}
        
    def verify_links(self, links_data, all_pages):
        """
        Filters out and returns only the links that exist in the original list of pages.
        
        Parameters:
            links_data (dict): A dictionary containing a list of links to verify.
            all_pages (dict): A dictionary containing all the pages from the repository with their URLs as keys.
        
        Returns:
            dict: A dictionary containing only the valid links.
        """
        valid_links = []
        all_pages_urls = set(page['url'] for page in all_pages.values())

        for link in links_data['links']:
            if link['url'] in all_pages_urls:
                valid_links.append({
                    'url': link['url'],
                    'name': link['name']
                })

        return {'links': valid_links}
