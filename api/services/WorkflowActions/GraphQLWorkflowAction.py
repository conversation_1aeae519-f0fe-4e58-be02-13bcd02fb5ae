from services.VectorDBManager import VectorDBManager


class SearchCategoryWorkflowAction:
  def __init__(self,
               vector_db_manager: VectorDBManager,
               ):
    self.vector_db_manager = vector_db_manager
      

  def execute(self, tenant_id, user_message: str, workflowId: str) -> str:
    """
    Search all categories for the user message and return the top 3 results.
    """
    
    