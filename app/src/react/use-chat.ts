import { useState, useRef, useCallback } from 'react';
import axios, { CancelTokenSource } from 'axios';
import { Message } from '@/models/message';

type CreateMessage = {
  content: string,
  role: 'user' | 'assistant',
  createdAt: Date,
}

type UseChatHelpers = {
  messages: Message[];
  error: undefined | Error;
  append: (message: Message | CreateMessage) => Promise<void>;
  reload: () => Promise<void>;
  stop: () => void;
  setMessages: (messages: Message[]) => void;
  input: string;
  setInput: (input: string) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  isLoading: boolean;
}

function useChat(api: string): UseChatHelpers {
  const [messages, setMessages] = useState<Message[]>([]);
  const [error, setError] = useState<undefined | Error>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [input, setInput] = useState<string>('');
  const cancelTokenSourceRef = useRef<CancelTokenSource | null>(null);

  const append = useCallback(
    async (message: Message | CreateMessage) => {
      setIsLoading(true);
      try {
        const response = await axios.post(api, {
          messages: [...messages, message],
        }, {
          cancelToken: cancelTokenSourceRef.current?.token
        });
  
        setMessages([...messages, response.data]);
      } catch (err) {
        if (err instanceof Error) {
          setError(err);
        } else {
          setError(new Error("An unexpected error occurred"));
        }
      } finally {
        setIsLoading(false);
      }
    },
    [messages, api]
  );

  const reload = useCallback(
    async () => {
      setIsLoading(true);
      try {
        const response = await axios.get(api, {
          cancelToken: cancelTokenSourceRef.current?.token
        });
  
        setMessages(response.data);
      } catch (err) {
        if (err instanceof Error) {
          setError(err);
        } else {
          setError(new Error("An unexpected error occurred"));
        }
      } finally {
        setIsLoading(false);
      }
    },
    [api]
  );

  const stop = useCallback(() => {
    if (cancelTokenSourceRef.current) {
      cancelTokenSourceRef.current.cancel();
      cancelTokenSourceRef.current = null;
    }
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  const handleSubmit = useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      if (!input) return;

      append({
        content: input,
        role: 'user',
        createdAt: new Date()
      });
      setInput('');
    },
    [input, append]
  );

  return {
    messages,
    error,
    append,
    reload,
    stop,
    setMessages,
    input,
    setInput,
    handleInputChange,
    handleSubmit,
    isLoading,
  };
}

export default useChat;
