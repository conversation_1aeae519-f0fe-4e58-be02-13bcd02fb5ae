import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import BeautifulDom from "beautiful-dom";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getCurrentPath() {
  let pathName = "";
  if (typeof window !== "undefined") {
    pathName = window.location.pathname;
  }
  return pathName;
}

export function getAPIUrl() {
  console.log("process.env.API_ENDPOINT", process.env);

  return process.env.NEXT_PUBLIC_API_ENDPOINT;
}

export function capitalise(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export function prettyDate(date: Date) {
  /* Rules are: 

  If today:
  - Today, 12:00AM

  If yesterday:
  - Yesterday, 12:00AM

  If this week:
  - Monday, 12:00AM

  If this year:
  - 1st January, 12:00AM

  If last year or before:
  - 1st January 2020, 12:00AM

  */

  const now = new Date();

  const isToday =
    date.getDate() === now.getDate() &&
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear();

  const isYesterday =
    date.getDate() === now.getDate() - 1 &&
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear();

  const isThisWeek =
    date.getDate() >= now.getDate() - now.getDay() &&
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear();

  const isThisYear = date.getFullYear() === now.getFullYear();

  const isLastYearOrBefore = date.getFullYear() < now.getFullYear();

  const day = date.toLocaleString("en-GB", { weekday: "long" });
  const month = date.toLocaleString("en-GB", { month: "long" });
  const dayOfMonth = date.toLocaleString("en-GB", { day: "numeric" });
  const year = date.toLocaleString("en-GB", { year: "numeric" });
  const time = date.toLocaleString("en-GB", {
    hour: "numeric",
    minute: "numeric",
  });

  if (isToday) {
    return `Today, ${time}`;
  }

  if (isYesterday) {
    return `Yesterday, ${time}`;
  }

  if (isThisWeek) {
    return `${day}, ${time}`;
  }

  if (isThisYear) {
    return `${dayOfMonth} ${month}, ${time}`;
  }

  if (isLastYearOrBefore) {
    return `${dayOfMonth} ${month} ${year}, ${time}`;
  }

  return date.toLocaleString("en-GB");
}

export function firebaseUniqueId() {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let autoId = "";

  for (let i = 0; i < 20; i++) {
    autoId += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return autoId;
}

/**
 * Checks if a value is null or undefined.
 * If the value is a string, it also checks if it is empty or whitespace.
 * If the value is an array, it checks if it is empty.
 * If the value is an object, it checks if it has no keys.
 * @param value The value to check
 */
export function isNullOrWhitespace(value: any): boolean {
  if (value === null || value === undefined) {
    return true;
  }

  // Check if value is an array before an object,
  // since an array is also considered an object.
  if (Array.isArray(value)) {
    return value.length === 0;
  }

  if (typeof value === "string") {
    return value.trim() === "";
  }

  if (typeof value === "object") {
    return Object.keys(value).length === 0;
  }

  return false;
}

export function cleanHtml(html: string): string {
  return html;
}
