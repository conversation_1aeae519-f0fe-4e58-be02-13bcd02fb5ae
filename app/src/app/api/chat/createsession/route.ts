import { jsonResponse } from "@/lib/apiUtils";
import { database } from "@/config/firebaseAdmin";
import * as admin from "firebase-admin";
import { getFirebaseApiEndpoints } from "@/services/api-connector/api-connector";

export async function POST(request: Request) {
  const jsonData = await request.json();
  const tenantId = jsonData.tenantId;
  const initialQuestion = jsonData.initialQuestion;

  const endpoints = getFirebaseApiEndpoints();

  const createSessionResponse = await endpoints.createSession({
    initialQuestion,
    tenantId,
  });

  return jsonResponse(200, createSessionResponse);
}
