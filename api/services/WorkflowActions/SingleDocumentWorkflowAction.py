from google.cloud.firestore import Client



class SingleDocumentWorkflowAction:
  def __init__(self,
               db: Client,
               tenant_id: str,
               ):
    self.db = db
    self.tenant_id = tenant_id
      

  def execute(self, documentId) -> str:
    doc_ref = (self.db
               .collection('tenants')
               .document(self.tenant_id)
               .collection('documents')
               .document(documentId)
    )
    doc = doc_ref.get()

    data = doc.to_dict()
    return f"""{data.get('title')}
  ----------------
  
  {data.get('data')}
  """
    