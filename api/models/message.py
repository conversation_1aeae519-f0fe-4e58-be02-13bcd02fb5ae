from typing import Optional
from enum import Enum, auto

class Role(Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    
    @classmethod
    def from_str(cls, role: str):
        if role == "user":
            return Role.USER
        elif role == "assistant":
            return Role.ASSISTANT
        elif role == "system":
            return Role.SYSTEM
        else:
            raise ValueError(f"Invalid role: {role}")

class MessageMetadata:
    def __init__(self, page: str):
        self.page = page

class Message:
    def __init__(self, id: str, message: str, role: Role, createdAt: any, metadata: Optional[MessageMetadata] = None, reported: Optional[bool] = None):
        self.id = id
        self.message = message
        self.role = role
        self.createdAt = createdAt
        self.metadata = metadata
        self.reported = reported
