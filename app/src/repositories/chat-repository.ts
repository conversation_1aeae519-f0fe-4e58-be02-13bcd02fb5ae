import {
  addDoc,
  doc,
  getDoc,
  query,
  onSnapshot,
  collection,
  Query,
  DocumentData,
  getDocs,
} from "firebase/firestore";
import { db } from "@/services/firebase.config";
import { User } from "firebase/auth";
import { useEffect } from "react";

export const loadChat = async (tenantId: string, chatSessionId: string) => {

  const userDocument = collection(doc(db, "tenants", tenantId, "chatSessions", chatSessionId), "messages");
  const messages = await getDocs(userDocument);

  return messages.docs.map((doc) => {
    return {
      id: doc.id,
      ...doc.data()
    }
  });  
};


