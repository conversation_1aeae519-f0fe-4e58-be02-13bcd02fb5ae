import requests
from llama_index.core.readers.base import BaseReader
from llama_index.core.schema import Document
import hashlib
from typing import Any, List

class CardDataReader(BaseReader):

    def __init__(self):
        """
        Initialize with GraphQL URL, headers, and query.
        """
        self.graphql_url = "https://dollaracademy.org.uk/wallapi/graphql"
        self.headers = {
            "Content-Type": "application/graphql",
        }
        order = "{orderby: TERM_ORDER}"
        self.payload = "query GetCardData {\r\n    subjects(first: 1000, where: {orderby: TERM_ORDER}) {\r\n        nodes {\r\n        name\r\n        slug\r\n        termTaxonomyId\r\n        subjectImage {\r\n            overlayOpacity\r\n            image {\r\n            node {\r\n                mediaItemUrl\r\n            }\r\n            }\r\n        }\r\n        subjectType {\r\n            subjectType\r\n        }\r\n        }\r\n    },\r\n    \r\n    forms(first: 1000, where: {orderby: TERM_ORDER}) {\r\n        nodes {\r\n        name\r\n        slug\r\n        termTaxonomyId\r\n        yearFormInformation {\r\n            content\r\n        }\r\n        }\r\n    },\r\n    \r\n    cards(first: 1000) {\r\n        nodes {\r\n        title\r\n        content\r\n        slug\r\n        databaseId\r\n        terms {\r\n            nodes {\r\n            name\r\n            termTaxonomyId\r\n            taxonomyName\r\n            }\r\n        }\r\n        cardDetails {\r\n            content\r\n            waysOfThinkingAndLearning\r\n            toolsForWorking\r\n            thePupil\r\n            level\r\n        }\r\n        }\r\n    },\r\n}"
    
    def _execute_graphql_query(self):
        """
        Sends a GraphQL query to the URL and returns the result.
        """
        response = requests.request("POST", 
          self.graphql_url, 
          headers=self.headers, 
          data=self.payload)
        response.raise_for_status()  # Raise an error for failed requests
        return response.json()

    def load_data(self, *args: Any, **load_kwargs: Any) -> List[Document]:
        """
        Loads data by executing a GraphQL query and returns a list of Document objects.
        """
        results = self._execute_graphql_query()
        card_data = results.get('data', {}).get('cards', {}).get('nodes', [])

        report_data = []
        for result in card_data:
            # Extracting the basic card details
            title = result.get('title', 'Untitled')
            content = result.get('content', '')

            # Flattening the terms
            terms = ', '.join(
                term.get('name', '') for term in result.get('terms', {}).get('nodes', [])
            )

            # Extracting card details
            card_details = result.get('cardDetails', {})
            ways_of_thinking = card_details.get('waysOfThinkingAndLearning', 'Not specified')
            tools_for_working = card_details.get('toolsForWorking', 'Not specified')
            pupil_focus = card_details.get('thePupil', 'Not specified')
            level = card_details.get('level', 'Not specified')

            s_content = f"Content: {content}" if content else ""
            s_terms = f"Terms: {terms}" if terms else ""
            s_ways_of_thinking = f"Ways of Thinking and Learning: {ways_of_thinking}" if ways_of_thinking else ""
            s_tools_for_working = f"Tools for Working: {tools_for_working}" if tools_for_working else ""
            s_pupil_focus = f"Focus on the Pupil: {pupil_focus}" if pupil_focus else ""
            s_level = f"Level: {level}" if level else ""

            # Constructing the document content
            the_content = f"""
            Title: {title}
            {s_content}
            {s_terms}
            {s_ways_of_thinking}
            {s_tools_for_working}
            {s_pupil_focus}
            {s_level}
            """

            # Creating the Document object
            doc_hash = hashlib.md5(the_content.encode()).hexdigest()
            document = Document(text=the_content, doc_id=result.get('slug', 'unknown'), doc_hash=doc_hash)
            report_data.append(document)

        return report_data
