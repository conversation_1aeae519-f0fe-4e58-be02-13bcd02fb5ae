import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useEffect, useState } from "react";

import { doc, getDoc, updateDoc } from "firebase/firestore";
import { db } from "@/services/firebase.config";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import useDashboardStore from "@/stores/dashboard.store";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import ColourPicker from "@/components/ui/colour-picker";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import FormSkeleton from "./FormSkeleton";
import { Textarea } from "@/components/ui/textarea";

export type FieldDetail = {
  label: string;
  description: string;
  formSchema: z.ZodType<any>;
  selectValues?: string[];
  defaultValue?: string | boolean | number;
};

export type FieldDetails = {
  [key: string]: FieldDetail;
};

function parseSVG(svgString: string, fill = "#000"): JSX.Element | null {
  if (typeof svgString !== "string" || !svgString.includes("<svg ")) {
    return null;
  }
  return <div dangerouslySetInnerHTML={{ __html: svgString }} />;
}

export default function GenericConfig({
  fieldDetails,
  collectionString,
  buttonName,
  baseCollectionId = "",
  baseCollection = "tenants",
}: {
  fieldDetails: FieldDetails;
  collectionString: string;
  buttonName: string;
  baseCollection?: string;
  baseCollectionId?: string;
}) {
  const formSchemaObj = Object.keys(fieldDetails).reduce((acc, key) => {
    acc[key] = fieldDetails[key].formSchema;
    return acc;
  }, {} as Record<string, z.ZodType<any>>);

  const apiKeysFormSchema = z.object(formSchemaObj);
  type ApiKeysFormValues = z.infer<typeof apiKeysFormSchema>;

  const [config, setConfig] = useState<ApiKeysFormValues | null>(null);

  const collectionId =
    baseCollectionId !== ""
      ? baseCollectionId
      : useDashboardStore((state) => state.selectedClient)?.value;

  const [loaded, setLoaded] = useState(false);

  const defaultValues = Object.keys(fieldDetails).reduce((acc, key) => {
    acc[key] = fieldDetails[key].defaultValue || "";
    return acc;
  }, {} as Record<string, string | number | boolean>);

  const form = useForm<ApiKeysFormValues>({
    resolver: zodResolver(apiKeysFormSchema),
    defaultValues,
    mode: "onChange",
  });

  useEffect(() => {
    const fetchConfig = async () => {
      console.log({ collectionId, baseCollection });
      const docRef = doc(db, baseCollection, collectionId);
      const docSnap = await getDoc(docRef);

      let fetchedConfig = defaultValues;

      if (
        docSnap.exists() &&
        docSnap.data() &&
        docSnap.data()[collectionString]
      ) {
        fetchedConfig = {
          ...defaultValues,
          ...docSnap.data()[collectionString],
        };
      }

      form.reset(fetchedConfig);
      setConfig(fetchedConfig);
      setLoaded(true);
    };

    fetchConfig();
  }, [collectionId]);

  const onSubmit = async (data: ApiKeysFormValues) => {
    const docRef = doc(db, baseCollection, collectionId);
    await updateDoc(docRef, { [collectionString]: data });
    toast({
      title: "Success",
      description: "Configuration updated successfully.",
    });
  };

  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    setValue: Function,
    fieldName: string
  ) => {
    const numberValue = parseFloat(e.target.value);
    if (!isNaN(numberValue)) {
      setValue(fieldName, numberValue);
    }
  };

  const { setValue } = form;

  return (
    <>
      {!loaded && <FormSkeleton />}
      {loaded && config && (
        <Form {...form}>
          <form className="space-y-8" onSubmit={form.handleSubmit(onSubmit)}>
            {Object.keys(apiKeysFormSchema.shape).map((key) => {
              // we don't display the label if it's a boolean
              const noShowLabel =
                fieldDetails[key].formSchema instanceof z.ZodBoolean;

              const instanceofName =
                fieldDetails[key].formSchema.constructor &&
                fieldDetails[key].formSchema.constructor.name;

              console.log("NAME: ", { instanceofName });

              return (
                <FormField
                  key={key}
                  control={form.control}
                  name={key as keyof ApiKeysFormValues}
                  render={({ field }: any) => (
                    <FormItem>
                      {!noShowLabel && (
                        <FormLabel>{fieldDetails[key].label}</FormLabel>
                      )}
                      <FormControl>
                        {
                          // Check if it's a select field
                          fieldDetails[key].selectValues ? (
                            <Select
                              defaultValue={field.value}
                              onValueChange={field.onChange}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder={`Select a ${key}`} />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectGroup>
                                  <SelectLabel>
                                    {fieldDetails[key].label}
                                  </SelectLabel>
                                  {fieldDetails[key]?.selectValues?.map(
                                    (value, index) => (
                                      <SelectItem
                                        key={`key-${index}`}
                                        value={value}
                                      >
                                        {typeof value === "string" &&
                                        value.includes("<svg ") ? (
                                          <div className="flex items-center gap-2">
                                            {" "}
                                            {parseSVG(value)} Style {index + 1}
                                          </div>
                                        ) : (
                                          value
                                        )}
                                      </SelectItem>
                                    )
                                  )}
                                </SelectGroup>
                              </SelectContent>
                            </Select>
                          ) : fieldDetails[key].formSchema instanceof
                            z.ZodBoolean ? (
                            // Check if the Zod schema is of Boolean type
                            <div className="flex items-center space-x-2">
                              <Switch id={`switch-${key}`} {...field} />
                              <Label htmlFor={`switch-${key}`}>
                                {fieldDetails[key].label}
                              </Label>
                            </div>
                          ) : fieldDetails[key].formSchema instanceof
                              z.ZodString &&
                            key.toLowerCase().includes("colour") ? (
                            <ColourPicker
                              initialColor={field.value || "#fff"}
                              onChange={(colour: string) => {
                                field.onChange(colour);
                              }}
                            />
                          ) : fieldDetails[key].formSchema instanceof
                              z.ZodNumber &&
                            key.toLowerCase().includes("temperature") ? (
                            <Input
                              {...field}
                              type="number"
                              min={0}
                              max={1}
                              step={0.01}
                              onChange={(e) =>
                                handleNumberChange(e, setValue, key)
                              }
                            />
                          ) : fieldDetails[key].formSchema instanceof
                              z.ZodNumber &&
                            key.toLowerCase().startsWith("int") ? (
                            <Input
                              {...field}
                              type="number"
                              step={0.1}
                              onChange={(e) =>
                                handleNumberChange(e, setValue, key)
                              }
                            />
                          ) : key.toLowerCase().includes("password") ? (
                            <Input {...field} type="password" />
                          ) : key.includes("List") ? (
                            <Textarea {...field} className="min-h-[600px]" />
                          ) : (
                            <Input {...field} />
                          )
                        }
                      </FormControl>
                      <FormDescription>
                        {fieldDetails[key].description}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              );
            })}
            <Button type="submit">{buttonName}</Button>
          </form>
        </Form>
      )}
    </>
  );
}
