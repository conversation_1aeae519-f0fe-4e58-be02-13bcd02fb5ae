# models/VectorDocument.py

from models.BaseDocument import BaseDocument
from llama_index.core import Document as LlamaDocument

class VectorDocument(BaseDocument):
    def __init__(self, title: str, content: str, categoryId: str, url: str):
        self.title = title
        self.content = content
        self.categoryId = categoryId
        self.url = url

    def to_llama_document(self):
        metadata = {
            "title": self.title,
            "category_id": self.categoryId,
            "url": self.url,
        }
        return LlamaDocument(text=self.content, metadata=metadata)
