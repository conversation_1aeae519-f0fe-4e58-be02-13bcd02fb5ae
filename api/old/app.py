
import json
import uuid
from quart import Quart, Request, Response, request, jsonify
from quart import make_response

import os
import io
from firebase_admin import initialize_app
import firebase_admin
from models.report_request import ReportRequest
from firebase_admin import firestore
# import openai

#set the openai key globally

import openai

from models.chat_response import ChatResponse

from services.chat_manager import ChatManager   
from services.model_service import ModelService
from user_agents import parse
from quart_cors import cors

from datetime import datetime, timedelta
from llama_index.core.chat_engine.types import StreamingAgentChatResponse
from llama_index.core.callbacks.global_handlers import set_global_handler

set_global_handler("simple")

allowed_origins = [
    "https://multiply-chatbot.web.app",
    "http://localhost:3000",
    "http://localhost:61270",
    "https://app.chatnav.co.uk",
    "https://sleepeezee.com",
    "https://sleepeezee.multiapps.net",
    "http://sleepeezee.prox/",
    "https://dollar.multiapps.net/",
    "*"
]
app = Quart(__name__)
app = cors(app, allow_origin=allowed_origins)

# Initialize Firebase
cwd = os.path.dirname(os.path.abspath(__file__))
cert_path = os.path.join(cwd, "credentials/firebase.json")
cred = firebase_admin.credentials.Certificate(cert_path)
firebase_app = firebase_admin.initialize_app(cred)




@app.route('/create_session', methods=['POST'])
async def create_session() -> Response:
    
    request_data = await request.get_json()
    
    query_tenantid = request_data["tenantId"]
    initial_question = request_data["initialQuestion"]

    ua_string = request.headers.get("User-Agent")
    user_agent = parse(ua_string)
    
    browser = user_agent.browser.family  # e.g. 'Mobile Safari'
    os = user_agent.os.family  # e.g. 'iOS'
    device_type = "Desktop"
    if user_agent.is_mobile:
        device_type = "Mobile"
    elif user_agent.is_tablet:
        device_type = "Tablet"

    user_ip = request.remote_addr
    
    firestore_now = firestore.SERVER_TIMESTAMP

    # Corrected 'data' dictionary
    data = {
        'created': firestore_now,
        'lastUpdated': firestore_now,
        'messageCount': 0,
        'userIp': user_ip,
        'browser': browser,
        'os': os,
        'deviceType': device_type,
        'initialQuestion': initial_question
    }
    
    database = firestore.client()

    update_time, chat_session_ref = (
      database.collection('tenants')
      .document(query_tenantid)
      .collection('chatSessions')
      .add(data))

    return jsonify({"sessionId": chat_session_ref.id, "status": "success"}), 200

@app.route('/chat', methods=['POST'])
async def chat() -> Response:
    request_data = await request.get_json()
    
    query_text = request_data["text"]
    
    query_tenantid = request_data["tenantId"]
    query_sessionid = request_data["sessionId"]
    query_metadata = request_data.get("metadata", None)
    
    from services.agent_service import AgentService
    
    agent_service = AgentService(firestore.client())
    
    response = await agent_service.agent_chat(tenant_id=query_tenantid, 
                                             chat_session_id=query_sessionid, 
                                             text=query_text )
    
    text_response = response.response
    
    return jsonify({"status": "success"}), 200
    

    # return await process_chat_request(query_history, query_text, query_tenantid, query_sessionid, query_metadata)

async def process_chat_request(query_history, query_text, query_tenantid, query_sessionid, query_metadata):
    # query_history comes back as a JSON string, but we need it as an array of objects
    history = []
    if query_history is not None:
        history = query_history

    chat_manager: ChatManager = ChatManager.create(query_tenantid)

    if query_text is None:
        return "No text found, please include a 'text' key in the request body.", 400

    query = f"""{query_text}"""
    
    from llama_index.core.llms import ChatMessage
    proper_chat_history = []
    for message in history:
        if "role" in message and "message" in message:
            msg = ChatMessage(content=message["message"], role=message["role"])
            proper_chat_history.append(msg)
    
    
    return chat_manager.agent_chat(
        chat_history=proper_chat_history, text=query, chat_session_id=query_sessionid
    ), 200
    
    
@app.route('/train_reports', methods=['POST'])
async def trainreports() -> Response:
    request_data = await request.get_json()
    query_tenantid = request_data
    
    database = firestore.client()
    
    try:
        ModelService.trainGPT4(query_tenantid, database)
    
        return jsonify({"status": "success"}), 200
    
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500
    

@app.route('/system_chat', methods=['POST'])
async def system_chat() -> Response:
    request_data = await request.get_json()
    query_text = request_data["text"]
    query_systemMessages = request_data["systemMessages"]
    query_tenantid = request_data["tenantId"]

    chat_manager: ChatManager = ChatManager.create(query_tenantid)

    chat_response = chat_manager.chatSystem(query_text, query_systemMessages)

    return jsonify({"response": chat_response, "status": "success"}), 200
    
@app.route('/retrain', methods=['POST'])
async def retrain() -> Response:
    request_data = await request.get_json()
    query_tenantid = request_data["tenantId"]

    result: bool = False

    try:
        chat_manager: ChatManager = ChatManager.create(query_tenantid)
        result = chat_manager.retrainAI()
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

    if result:
        return jsonify({"status": "success"}), 200

    return jsonify({"status": "error"}), 500


@app.route('/report', methods=['POST'])
async def report() -> Response:
    
    request_data = await request.get_json()
    
    from services.agent_service import AgentService
    
    agent_service = AgentService(firestore.client())
    
    await agent_service.report(ReportRequest.from_dict(request_data) )
    
    return jsonify({"status": "success"}), 200
    

@app.route("/categorise", methods=["POST"])
async def categorise() -> Response:
    request_data = await request.get_json()
    query_tenantid = request_data["tenantId"]

    chat_manager: ChatManager = ChatManager.create(query_tenantid)

    response = chat_manager.categoriseAll(query_tenantid)

    return jsonify(response), 200

@app.route('/check_spam', methods=['POST'])
async def check_spam() -> Response:
    
    from openai import OpenAI
    
    llm = OpenAI("gpt-3.5-turbo", 0.1, api_key="********************************************************")
    
    call_api_key = "4f8aa797b2cf4b4d84e4f16bff09bebf"
    
    request_data = await request.get_json()
    query_text = request_data["text"]
    
    bearer_token = request.headers.get("Authorization")
    
    if bearer_token is None or bearer_token != f"Bearer {call_api_key}":
        return jsonify({"status": "error", "message": "No authorization token found"}), 401
    
    completion = llm.chat.completions.create(
        model="gpt-4",
        messages=[
            {"role": "system", "content": "You are a spam and profanity detecting system. Your only answers are 'yes' or 'no'. You can understand all languages."},
            {"role": "user", "content": f"""
            Consider the following text:
                {query_text}
                Is this text spam or profanity? If so, answer 'yes'. If not, answer 'no'.
            """ }
        ],
        max_tokens=1,
    )
    
    response = completion.choices[0].message.content
    
    # to string
    response = str(response)
    response = response.lower()
    
    return jsonify({"status": "success", "response": response}), 200

@app.route('/categories', methods=['GET'])
async def categories() -> Response:
    
    query_tenantid = request.args.get("tenantId")
    

    database = firestore.client()

    from services.categories_service import get_all_categories
    
    categories = get_all_categories(database, query_tenantid)

    return jsonify(categories), 200
    
    

@app.route('/speech_to_text', methods=['POST'])
async def speech_to_text():

    
    # openaikey = (
    #   database.collection('tenants')
    #   .document(query_tenantid)
    #   .get()).to_dict()["openai"]["openAIKey"]
    
    openai.api_key = "********************************************************"
    
    uploaded_file = (await request.files)["file"]

    audio_bytes = uploaded_file.read()

    # Create a file-like object from the bytes
    audio_file_in_memory = io.BytesIO(audio_bytes)
    audio_file_in_memory.name = "audio.wav"

    # Convert audio to text using OpenAI's STT
    transcript = openai.Audio.transcribe("whisper-1", file=audio_file_in_memory)
    text = transcript.get('text')
    
    return jsonify({"original_text": text, "corrected_text": text}), 200
    

    # Process the text using GPT-4
    system_prompt = ("You are a helpful assistant. Your task is to correct "
                        "any discrepancies in the transcribed text. Only add necessary punctuation "
                        "such as periods, commas, and capitalization, and use only the context provided. Do use line breaks if and when necessary.")
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[
            {
                "role": "system",
                "content": system_prompt
            },
            {
                "role": "user",
                "content": text
            }
        ]
    )
    corrected_text = response['choices'][0]['message']['content']

    return jsonify({"original_text": text, "corrected_text": corrected_text}), 200


@app.route('/sttchat', methods=['POST'])
async def sttchat():

    
    
    openai.api_key = "********************************************************"
    
    uploaded_file = (await request.files)["file"]

    audio_bytes = uploaded_file.read()

    # Create a file-like object from the bytes
    audio_file_in_memory = io.BytesIO(audio_bytes)
    audio_file_in_memory.name = "audio.wav"
    
    # Convert audio to text using OpenAI's STT
    transcript = openai.Audio.transcribe("whisper-1", file=audio_file_in_memory)
    text = transcript.get('text')
    
    request_data = await request.form
    
    query_history = request_data.get("history", None)
    query_tenantid = request_data["tenantId"]
    query_sessionid = request_data["sessionId"]
    query_metadata = request_data.get("metadata", None)


    return await process_chat_request(query_history, text, query_tenantid, query_sessionid, query_metadata) 
    
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)