"use client";
import GenericConfig, {
  FieldDetails,
} from "@/components/Dashboard/Widgets/GenericConfig";
import React from "react";
import { z } from "zod";


const CONFIG_DETAILS: FieldDetails = {
  url: {
    label: "URL",
    description: "Your website URL",
    formSchema: z.string(), // Assuming color is input as a string (e.g., HEX or RGB).
  },
  username: {
    label: "Username",
    description: "The wordpress username (should be an application user with read access to the wordpress site)",
    formSchema: z.string(), // Assuming color is input as a string (e.g., HEX or RGB).
  },
  password: {
    label: "Password",
    description: "The wordpress password (should be an application user with read access to the wordpress site)",
    formSchema: z.string(), // Assuming color is input as a string (e.g., HEX or RGB).
  },
  consumerKey: {
    label: "Consumer Key",
    description: "Your consumer key (in the WooCommerce API settings)",
    formSchema: z.string(), // Assuming color is input as a string (e.g., HEX or RGB).
  },
  consumerSecret: {
    label: "Consumer Secret",
    description: "Your consumer secret (in the WooCommerce API settings)",
    formSchema: z.string(), // Assuming color is input as a string (e.g., HEX or RGB).
  }
};

export default function WordPressConnectorPage() {
  return (
    <div>
      {
        <GenericConfig
          fieldDetails={CONFIG_DETAILS}
          collectionString="wordpressConfig"
          buttonName="Save WordPress Configuration"
        />
      }
    </div>
  )
}
