from google.cloud.firestore import Client
import json
from functools import lru_cache


def get_flattened_categories(database: Client, tenant_id: str) -> str:
  categories_ref = (database
      .collection('tenants')
      .document(tenant_id)
      .collection('reportCategories')
    )
    
  categories_value = categories_ref.get()
  
  # If there are no categories, don't do anything and return
  if len(categories_value) == 0:
    return "Uncategorised"
  
  # This list should be a JSON string
  # let's first do a .get() on each category, and then add it to the list
  categories = []
  for category in categories_value:
    categories.append({
      "id": category.id,
      "description": category.to_dict().get("description", None),
      "label": category.to_dict().get("label", None),
    })
  
  categories.append({
    "id": "Uncategorised",
    "description": "This category is used when no other category applies. It is also used for any content that is purely functional to the website, such as thank you pages, contact forms, etc.",
    "label": "Uncategorised",
  })
  
  return json.dumps(categories)    

@lru_cache(maxsize=128)
def get_all_categories(database: Client, tenant_id: str):
  
  categories_ref = (database
      .collection('tenants')
      .document(tenant_id)
      .collection('reportCategories')
    )
    
  categories_value = categories_ref.get()
  
  # If there are no categories, don't do anything and return
  if len(categories_value) == 0:
    return "Uncategorised"
  
  # This list should be a JSON string
  # let's first do a .get() on each category, and then add it to the list
  categories = []
  for category in categories_value:
    categories.append({
      "id": category.id,
      "description": category.to_dict().get("description", None),
      "label": category.to_dict().get("label", None),
    })
  
  return categories