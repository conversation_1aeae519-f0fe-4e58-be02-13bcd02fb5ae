from typing import Any, Callable, Dict, List, Union

from llama_index.core.schema import Document
from models.tenant import Tenant
from repositories.tenant_repository import TenantRepository
from readers.knowledgebase_reader import KBDocumentsReader  # Replace with the actual import
from readers.woocommerce_reader import W<PERSON><PERSON><PERSON><PERSON>ceReader  # Replace with the actual import
from readers.wordpress_reader import WordpressReader  # Replace with the actual import
from readers.feedback_reader import Feedback<PERSON>eader  # Replace with the actual import
from readers.website_reader import WebsiteReader  # Replace with the actual import
from llama_index.core.readers.base import BaseReader

from google.cloud.firestore import Client
reader_config: Dict[str, Callable[[Tenant, Any, str], BaseReader]] = {
    "kb": lambda tenant, database, tenant_id: KBDocumentsReader(database, tenant_id),
    "website": lambda tenant, database, tenant_id, category_id: WebsiteReader(database, tenant_id, category_id),
    # "wc": lambda tenant, database, tenant_id: WooCommerceReader(
    #     tenant.wordpressConfig, database, tenant_id
    # ),
    # "wp": lambda tenant, database, tenant_id: WordpressReader(
    #     tenant.wordpressConfig
    # )
}

class ReaderFactory:

    @staticmethod
    def get_reader(tenant_id: str, database: Client, reader_name: str) -> BaseReader:
        tenant_repository = TenantRepository(database)
        tenant = tenant_repository.get_tenant(tenant_id)
        create_reader = reader_config.get(reader_name)
        
        if not create_reader:
            raise ValueError(f"Reader {reader_name} not found")
        
        return create_reader(tenant, database, tenant_id)

    @staticmethod
    def get_readers(tenant_id: str, database: Client) -> List[BaseReader]:
        tenant_repository = TenantRepository(database)
        tenant = tenant_repository.get_tenant(tenant_id)
        readers = []
        for reader_name in reader_config.keys():
            create_reader = reader_config.get(reader_name)
            if not create_reader:
                raise ValueError(f"Reader {reader_name} not found")
            readers.append(create_reader(tenant, database, tenant_id))
        return readers
    
    @staticmethod
    def get_reader(tenant_id: str, database: Client, reader_name: str, category_id: str) -> BaseReader:
        tenant_repository = TenantRepository(database)
        tenant = tenant_repository.get_tenant(tenant_id)
        create_reader = reader_config.get(reader_name)
        
        if not create_reader:
            raise ValueError(f"Reader {reader_name} not found")
        
        return create_reader(tenant, database, tenant_id, category_id)

    @staticmethod
    def read_all(tenant_id: str, database: Client) -> List[Document]:
        readers = ReaderFactory.get_readers(tenant_id, database)
        
        results = []
        
        for reader in readers:
            results.extend(reader.load_data())
            
        return results
        
      
    