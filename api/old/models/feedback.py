from typing import List, Dict

class FeedbackRequest:
    def __init__(self, tenant_id: str, positiveFeedback: bool, messages: List[Dict[str, str]]):
        self.tenant_id = tenant_id
        self.positiveFeedback = positiveFeedback
        self.messages = messages

class FeedbackResponse:
    def __init__(self, category: str, correctReplyAccordingToData: str, dataMissingInContext: bool, confidenceIndex: int):
        self.category = category
        self.correctReplyAccordingToData = correctReplyAccordingToData
        self.dataMissingInContext = dataMissingInContext
        self.confidenceIndex = confidenceIndex
