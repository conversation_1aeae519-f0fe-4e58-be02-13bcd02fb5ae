"use client";

import GenericConfig, {
  FieldDetails,
} from "@/components/Dashboard/Widgets/GenericConfig";
import { z } from "zod";

const ANALYTICS_CONFIG_DETAILS: FieldDetails = {
  gaId: {
    label: "Google Analytics ID",
    description: "Enter your Google Analytics measurement ID (e.g., G-XXXXXXXXXX)",
    formSchema: z.string(),
    defaultValue: "",
  },
};

export default function AnalyticsConfig() {
  return (
    <>
      <GenericConfig
        fieldDetails={ANALYTICS_CONFIG_DETAILS}
        collectionString="analytics"
        buttonName="Save Analytics Configuration"
      />
    </>
  );
}