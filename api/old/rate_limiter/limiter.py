import functools
from datetime import datetime, timedelta
from quart import request, jsonify, sleep

# Dictionary to store IP addresses and their last request time
ip_last_request_time = {}

# Dictionary to store IP addresses and their request count in the past minute
ip_request_count = {}

# Time between allowed requests
ALLOWED_INTERVAL = timedelta(seconds=6)

# Maximum requests an IP can make in a minute before getting blacklisted
MAX_REQUESTS_PER_MINUTE = 20

# Load IPs from blacklist.txt into a set
BLACKLIST = set()
with open('blacklist.txt', 'r') as file:
    BLACKLIST = set(file.read().splitlines())

def throttle_ip():
    """Decorator to throttle requests based on IP and delay processing if needed."""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            global ip_last_request_time, ip_request_count

            ip = request.remote_addr

            # If IP is in the blacklist, block the request
            if ip in BLACKLIST:
                return jsonify({"error": "Your IP is blacklisted"}), 403

            current_time = datetime.now()

            # If the IP has made a request before, calculate the time since the last request
            if ip in ip_last_request_time:
                time_since_last_request = current_time - ip_last_request_time[ip]
                
                # If the time since the last request is less than the allowed interval, delay processing
                if time_since_last_request < ALLOWED_INTERVAL:
                    await sleep((ALLOWED_INTERVAL - time_since_last_request).total_seconds())

                # Update the request count for this IP
                if ip in ip_request_count:
                    ip_request_count[ip] += 1
                else:
                    ip_request_count[ip] = 1

                # If the IP has exceeded the request limit, blacklist it
                if ip_request_count[ip] > MAX_REQUESTS_PER_MINUTE:
                    with open('blacklist.txt', 'a') as file:
                        file.write(f"{ip}\n")
                    BLACKLIST.add(ip)
                    return jsonify({"error": "Too many requests, your IP is blacklisted"}), 429

            else:
                ip_request_count[ip] = 1

            ip_last_request_time[ip] = current_time

            return await func(*args, **kwargs)

        return wrapper
    return decorator
