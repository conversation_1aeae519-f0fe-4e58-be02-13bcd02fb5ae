import re
import requests
from xml.etree import ElementTree
from functools import lru_cache
from firebase_admin import firestore
from google.cloud.firestore_v1 import Client
from services.model_service import ModelService
from services.categories_service import get_flattened_categories
from llama_index.core.readers import Document
from llama_index.core.llms import ChatMessage

import time
import json
import hashlib

class LinkValidator:
    
    last_fetched = 0

    def __init__(self, database: Client, model_service: ModelService):
        self.database = database
        self.model_service = model_service
        self.authorised_links_cache = None

    def extract_links_from_text(text: str) -> list:
        
        url_regex = r'https?://(?:(?:[a-zA-Z0-9\-._~:\/?#\[\\]@!$&\'()*+,;=]|%[0-9a-fA-F]{2,4})+)+'
        return re.findall(url_regex, text)

    @lru_cache(maxsize=128)
    def get_sitemaps_from_firestore(self, tenant_id: str) -> list:
        """
        Retrieve all XML sitemaps from Firestore for the given tenant_id.
        """
        sitemaps_ref = self.database.collection(f'tenants/{tenant_id}/sitemaps')
        sitemaps = sitemaps_ref.stream()
        return [doc.to_dict() for doc in sitemaps]

    @lru_cache(maxsize=128)
    def get_links_from_sitemap(self, sitemap_url: str) -> list:
        """
        Parse the XML sitemap to retrieve all the links.
        """
        response = requests.get(sitemap_url)
        root = ElementTree.fromstring(response.content)
        namespace = {'ns': 'http://www.sitemaps.org/schemas/sitemap/0.9'}
        urls = [url.text for url in root.findall('ns:url/ns:loc', namespace)]
        return urls
      
      
    def generic_chat(self, prompt: str, system_messages: [ChatMessage]) -> str:
    
        engine = self.model_service.getGenericChatEngine([], True)
        gpt_response = engine.chat(prompt, system_messages)
        return gpt_response.response
      
    def get_all_authorised_links(self, tenant_id):
        current_time = time.time()
        
        # Check if cache is empty or 24 hours have passed since the last fetch
        if not self.authorised_links_cache or (current_time - LinkValidator.last_fetched) > 86400:  # 86400 seconds = 24 hours
            self.authorised_links_cache = self._fetch_authorised_links_from_firestore(tenant_id)
            LinkValidator.last_fetched = current_time

        return self.authorised_links_cache


    def _fetch_authorised_links_from_firestore(self, tenant_id: str) -> set:
        """
        Retrieve and parse all XML sitemaps for the given tenant_id to get all authorised links.
        """
        sitemaps = self.get_sitemaps_from_firestore(tenant_id)
        all_links = set()
        for sitemap in sitemaps:
            sitemap_links = self.get_links_from_sitemap(sitemap['sitemap'])
            all_links.update(sitemap_links)
        return all_links

    def validate_links(self, message: str, tenant_id: str) -> (bool, list):
        """
        Validate if all links in the message are authorised for the given tenant_id.
        """
        url_regex = r'https?:\/\/[^\s"\')<]+'
        links = re.findall(url_regex, message)
        # Remove trailing ' or " from links
        links = [link[:-1] if link[-1] in ['\'', '"'] else link for link in links]
        # ensure links all end with trailing slash
        links = [link + '/' if link[-1] != '/' else link for link in links]
        
        authorised_links = self.get_all_authorised_links(tenant_id)
        invalid_links = [link for link in links if link not in authorised_links]
        return len(invalid_links) == 0, invalid_links
    
    @lru_cache(maxsize=128)
    def get_valid_product_names_from_firestore(self, tenant_id: str) -> set:
        tenant_ref = self.database.collection('tenants').document(tenant_id)
        tenant = tenant_ref.get().to_dict()
        
        product_names = tenant.get('productNames', [])
        
        # This is a list separated by \n
        additional_products = tenant.get('productsList', "")
        
        merged_product_names = product_names + additional_products['productList'].split('\n')
        
        
        return set(merged_product_names)

    
    def validate_product(self, message: str, tenant_id: str) -> bool:
        # Fetch valid product names from Firestore
        valid_product_names = self.get_valid_product_names_from_firestore(tenant_id)
        
        # Convert the message to lowercase and use regex to strip special characters
        cleaned_message = re.sub(r'[^\w\s]', '', message.lower())
        
        # Iterate over the valid product names and see if any of them exist in the cleaned message
        for product in valid_product_names:
            cleaned_product = re.sub(r'[^\w\s]', '', product.lower())
            if cleaned_product in cleaned_message:
                return True
                
        return False
    
    def get_available_products(self, tenant_id: str) -> set:
        
        product_names = self.get_valid_product_names_from_firestore(tenant_id)
        
        product_name_list = []
        for product in product_names:
            product_name_list.append(product)
        
        return product_name_list


    from typing import List

    def rectify_message(self, question: str, message: str, tenant_id: str, system_messages: List[ChatMessage] = [], json_format: str = "") -> str:
        """
        Use GPT to generate a rectified message embedding the authorised links.
        """
        
        authorised_links = self.get_all_authorised_links(tenant_id)
        valid_product_names = self.get_valid_product_names_from_firestore(tenant_id)
        
        categories = get_flattened_categories(self.database, tenant_id)
        
        link_string = ', '.join(authorised_links)
        
        system_prompts = [
            f"All links should be in HTML format.",
            f"Always reply in the manner of a helpful assistant. If you cannot generate a similar message, give a helpful message.",
            f"Subjective questions, like 'What is the best', shouldn't have hard opinions. The AI assistant is there to help the user make their own choice, not to manipulate them into a sale, when the AI has not been able to try the products by itself.",
            f"When mentioning a product, always prioritise products that start with 'https://sleepeezee.com/product/' in their URL as they can be purchased on the website."
        ]
        
        format = ""
        if json_format == "":
            format = f"""The format of the message should be a JSON object with the following keys:
        {{
            "message": "The message you generated",
            "category": "The label of the category you think this message belongs to",
        }}"""
        else:
            format = json_format
            
        local_system_messages = [ChatMessage(content=prompt, role="system") for prompt in system_prompts]
        
        all_system_messages = system_messages + local_system_messages
        
        prompt = f"""
        Context:
        *******************************************************
        The authorised links are: {link_string}.,
        The authorised product names are: {valid_product_names},
        You can only use the following categories: {categories},
        *******************************************************
        The original question was: '{question}'
        Given the assistant message: '{message}', please generate a similar message using only these authorised links and/or these authorised product names. 
        
        When mentioning a product, always prioritise products that start with https://sleepeezee.com/product/ in their URL as they can be purchased on the website.
        
        {format}
        """
        gpt_response = self.generic_chat(prompt, system_messages=all_system_messages)
        
        return gpt_response
    
    def get_category_label(self, category_id: str, tenant_id: str) -> str:
        category_ref = (self.database.collection("tenants")
                        .document(tenant_id)
                        .collection("reportCategories")
                        .document(category_id))

        category_doc = category_ref.get()
        return category_doc.to_dict().get('label', 'Unknown')

    def get_categories(self, tenant_id: str) -> [str]:
        
        # First, get a reference to the 'reportCategories' collection for the specified tenant.
        categories_ref = (self.database.collection("tenants")
                          .document(tenant_id)
                          .collection("reportCategories"))

        # Then, retrieve all categories.
        categories_docs = categories_ref.stream()

        # Extract 'label' from each category document and create an array of category names.
        category_names = [doc.get('label') for doc in categories_docs if doc.get('label')]
        
        return category_names

    def add_to_gpt4_index(self, question: str, message: str, tenant_id: str):
        """
        Add the message to the GPT-4 index for the given tenant_id.
        """
        
        json_message = json.loads(message)
        
        index_ref = self.database.collection(f'tenants/{tenant_id}/gpt4Index')

        # Check if question already exists in Firestore
        existing_question = index_ref.where('question', '==', question).get()
        if len(existing_question) > 0:
            # print(f"Question '{question}' already exists in the index.")
            return  # Exit function if the question already exists
        
        index_ref.add({
            'question': question,
            'message': json_message['message'],
            'category': json_message['category'],
            'createdAt': firestore.SERVER_TIMESTAMP
        })
        
        index_ref = self.database.collection(f'tenants/{tenant_id}/gpt4Index')
        index_ref.add({
            'question': question,
            'message': json_message['message'],
            'category': json_message['category'],
            'createdAt': firestore.SERVER_TIMESTAMP
        })
        
        ModelService.trainGPT4(tenant_id, self.database)
        
        ModelService.set_needs_retraining(tenant_id, True)
        
        
            
        

    def validate_and_rectify(self, question: str, message: str, tenant_id: str, attempts: int = 2, mentions_product: bool = False) -> str:
        """
        Recursively validate and rectify a message for the given tenant_id up to a specified number of attempts.
        """
        was_rectified = False
        for _ in range(attempts):
            
            is_correct_product = True
            if mentions_product:
                is_correct_product = self.validate_product(message, tenant_id)
                mentions_product = False
            
            is_valid, invalid_links = self.validate_links(message, tenant_id)
            if is_valid and is_correct_product:
                
                if was_rectified:
                    self.add_to_gpt4_index(question, message, tenant_id)
                
                return message
            message = self.rectify_message(question, message, tenant_id)
            was_rectified = True
            
            # Remove any quotes from the start or end of the message. Either simple or double quotes.
            message = message.lstrip('\'"').rstrip('\'"')
            
            
        return "I'm sorry, I'm having some issues fetching this information for you. But I'm happy to assist you in any other matter."
