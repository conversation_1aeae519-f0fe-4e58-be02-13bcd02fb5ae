import Container from "@/components/general/Container";
import { SidebarNav } from "@/components/general/sidebar-nav";
import { Separator } from "@/components/ui/separator";
import { Toaster } from "@/components/ui/toaster";
import React from "react";

export default function FeedbackLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <Container className="my-12 border rounded-lg shadow">
        <div className="hidden p-10 pb-16 space-y-6 md:block">
          <div className="space-y-0.5">
            <h2 className="text-2xl font-bold tracking-tight">
              Feedback Management
            </h2>
            <p className="text-muted-foreground">
              Manage feedback from the chatbot in order to refine the AI's
              responses.
            </p>
          </div>
          <Separator className="my-6" />
          <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
            <div className="flex-1 ">{children}</div>
          </div>
        </div>
      </Container>
      <Toaster />
    </>
  );
}
