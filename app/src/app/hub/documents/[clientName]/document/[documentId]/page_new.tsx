// "use client";
// import React, { useEffect, useMemo, useRef, useState } from "react";
// import DocumentNavigator from "@/components/Documents/DocumentNavigator";
// import { doc, getDoc, updateDoc } from "firebase/firestore";
// import { db } from "@/services/firebase.config";
// import { useCollection, useDocumentData } from "react-firebase-hooks/firestore";
// import { ChatNavEditor } from "@/components/Documents/chatnav-editor";
// import { MDXEditorMethods } from "@mdxeditor/editor";
// import { toast } from "@/components/ui/use-toast";
// import { Toaster } from "@/components/ui/toaster";

// type Props = {
//   params: {
//     documentId: string;
//     clientName: string;
//   };
// };

// export default function DocumentPage({
//   params: { documentId, clientName },
// }: Props) {
//   const [markdown, setMarkdown] = useState("");
//   const [editingTitle, setEditingTitle] = useState(false);
//   const [title, setTitle] = useState("");

//   const titleRef = useRef<HTMLInputElement>(null);
//   const editorRef = useRef<MDXEditorMethods>(null);

//   const docRef = doc(db, "tenants", clientName, "documents", documentId);

//   useEffect(() => {
//     if (!clientName || !documentId) return;

//     const execute = async () => {
//       console.log("execute");
//       const docRef = doc(db, "tenants", clientName, "documents", documentId);

//       const data = await getDoc(docRef);

//       if (data.exists()) {
//         setTitle(data.data().title);
//         setMarkdown(data.data().data);
//       }
//     };

//     execute();
//   }, [clientName, documentId]);

//   return (
//     <div className="grid grid-cols-12 gap-4">
//       <div className="col-span-12 border-r md:col-span-2">
//         <DocumentNavigator clientName={clientName} />
//       </div>
//       <div className="col-span-12 md:col-span-10">
//         <h1
//           className="my-4 text-4xl font-bold"
//           onClick={() => {
//             setEditingTitle(true);
//             setTimeout(() => {
//               titleRef.current && titleRef.current.focus();
//             }, 100);
//           }}
//         >
//           <input
//             className={`w-full p-2`}
//             type="text"
//             style={{
//               display: editingTitle ? "block" : "none",
//             }}
//             ref={titleRef}
//             value={title}
//             onChange={(e) => setTitle(e.target.value)}
//             onBlur={() => {
//               setEditingTitle(false);
//               updateDoc(docRef, {
//                 title: title,
//                 lastModified: new Date().toISOString(),
//               });
//             }}
//           />
//           <span
//             className={`${
//               title == "New Document" ? "text-muted italic" : "text-primary"
//             } inline-block p-2`}
//             style={{
//               display: editingTitle ? "none" : "inline-block",
//             }}
//           >
//             {title}
//           </span>
//         </h1>
//         <ChatNavEditor
//           markdown={markdown}
//           ref={editorRef}
//           save={async () => {
//             await updateDoc(docRef, {
//               data: markdown,
//               lastModified: new Date().toISOString(),
//             });

//             toast({
//               title: "Saved",
//               description: "Your document has been saved.",
//             });
//           }}
//           setMarkdown={setMarkdown}
//         />
//       </div>
//       <Toaster />
//     </div>
//   );
// }
