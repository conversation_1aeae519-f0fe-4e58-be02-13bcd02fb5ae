# Use an official Python runtime as a parent image
FROM python:3.11

# Set the working directory in the container
WORKDIR /app

# First, copy only the requirements.txt file to leverage Docker cache
COPY ./requirements.txt /app/

RUN pip install --upgrade pip
# Install dependencies
RUN pip install -r requirements.txt

# Now, copy the rest of your application
COPY . /app

# Make port 5000 available to the world outside this container
EXPOSE 5000

# Run the application
CMD ["hypercorn", "app:app", "--bind", "0.0.0.0:5000"]
