import { useEffect } from "react";
import { ConfigItem, FormConfig, IFirebaseDTO } from "./firebase-datatable";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

function FormInput({
  configItem,
  setDTO,
  dto,
  label,
}: {
  configItem: ConfigItem;
  setDTO: (dto: IFirebaseDTO) => void;
  dto: IFirebaseDTO;
  label: string;
}) {
  return (
    dto && (
      <div className="flex flex-col gap-2">
        <label className="text-sm font-bold">{label}</label>
        {configItem.type === "text" && (
          <Input
            value={dto[configItem.key as string] as string}
            onChange={(e) =>
              setDTO({
                ...dto,
                [configItem.key]: e.target.value,
              })
            }
          />
        )}
        {configItem.type === "textarea" && (
          <Textarea
            value={dto[configItem.key as string] as string}
            onChange={(e) =>
              setDTO({
                ...dto,
                [configItem.key]: e.target.value,
              })
            }
          />
        )}
      </div>
    )
  );
}

export function DTOForm({
  dto,
  setDTO,
  config,
}: {
  dto: IFirebaseDTO;
  setDTO: (dto: IFirebaseDTO) => void;
  config: FormConfig;
}) {
  useEffect(() => {
    if (!dto) {
      const properties = Object.keys(config);
      // Must be a key:value pair array
      const defaultValues = properties.map((key) => {
        return {
          [key]: config[key].defaultValue as string,
        };
      });

      const defaultDTO = Object.assign(
        {
          id: "",
          order: "0",
        },
        ...defaultValues
      ) as IFirebaseDTO;

      setDTO(defaultDTO);
    }
  }, [dto]);

  return (
    <>
      {config &&
        Object.keys(config).map((key) => {
          const configItem = config[key];

          return (
            <FormInput
              key={key}
              configItem={configItem}
              setDTO={setDTO}
              dto={dto}
              label={configItem.label}
            />
          );
        })}
    </>
  );
}
