"use client"
import React from 'react';
import <PERSON>act<PERSON>low, { MiniMap, Controls, Background, addEdge, BackgroundVariant } from '@xyflow/react';

export default function WorkflowRenderer({ nodes, edges, setNodes, setEdges }) {
  const onConnect = (params) => {
    const sourceNode = nodes.find(node => node.id === params.source);
    const targetNode = nodes.find(node => node.id === params.target);

    if (params.source === params.target || !sourceNode || !targetNode) return;

    const sourceIsTrigger = sourceNode.data.type.startsWith('trigger');
    const targetIsTrigger = targetNode.data.type.startsWith('trigger');

    if (targetIsTrigger && !sourceIsTrigger) return; // Prevent actions from connecting to triggers

    setEdges((eds) => addEdge({ ...params, deletable: true, editable: true }, eds));
  };

  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={setNodes}
      onEdgesChange={setEdges}
      onConnect={onConnect}
      onEdgesDelete={(deletedEdges) => {
        setEdges((eds) => eds.filter(edge => !deletedEdges.some(deletedEdge => deletedEdge.id === edge.id)));
      }}
      onNodesDelete={(deletedNodes) => {
        setNodes((nds) => nds.filter(node => !deletedNodes.some(deletedNode => deletedNode.id === node.id)));
      }}
      nodesDraggable
      nodesConnectable
      elementsSelectable
      edgeUpdaterRadius={10}
    >
      <Controls />
      <MiniMap />
      <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
    </ReactFlow>
  );
}
