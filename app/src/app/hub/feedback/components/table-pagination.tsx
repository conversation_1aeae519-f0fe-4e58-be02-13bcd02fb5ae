import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from "@radix-ui/react-icons";
import { Table } from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TablePaginationProps {
  maxPages: number; // Maximum number of pages
  currentPage: number; // Current page number
  onChange: (newPage: number) => void; // Callback function to notify parent component when the page changes
}

export function TablePagination({
  maxPages,
  currentPage,
  onChange,
}: TablePaginationProps) {
  const getCanPreviousPage = () => {
    return currentPage > 1;
  };

  const getCanNextPage = () => {
    return currentPage < maxPages;
  };

  return (
    <div className="flex items-center justify-end px-2 my-4">
      <div className="flex items-center justify-end space-x-6 lg:space-x-8">
        <div className="flex-1 text-sm text-muted-foreground"></div>
        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          Page {currentPage}
          of {maxPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden w-8 h-8 p-0 lg:flex"
            onClick={() => onChange(1)}
            disabled={!getCanPreviousPage()}
          >
            <span className="sr-only">Go to first page</span>
            <DoubleArrowLeftIcon className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            className="w-8 h-8 p-0"
            onClick={() => onChange(currentPage - 1)}
            disabled={!getCanPreviousPage()}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeftIcon className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            className="w-8 h-8 p-0"
            onClick={() => onChange(currentPage + 1)}
            disabled={!getCanNextPage()}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRightIcon className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            className="hidden w-8 h-8 p-0 lg:flex"
            onClick={() => onChange(maxPages)}
            disabled={!getCanNextPage()}
          >
            <span className="sr-only">Go to last page</span>
            <DoubleArrowRightIcon className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
