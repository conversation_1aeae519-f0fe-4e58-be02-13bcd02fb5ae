from services.LlamaIndexService import LlamaIndexService


class SearchCategoryWorkflowAction:
  def __init__(self,
               llama_index_service: LlamaIndexService,
               ):
    self.llama_index_service = llama_index_service
      

  def execute(self, tenant_id, user_message: str, categoryId: str) -> str:
    """
    Search all categories for the user message and return the top 3 results.
    """
    search_results = self.llama_index_service.search(tenant_id, user_message, categoryId)
    # Take the top 3 search results
    top_results = search_results[:3]
    response = "\r\n - ".join([f"Title: {result['title']}, Content: {result['content']}." for result in top_results])
    
    return response
    