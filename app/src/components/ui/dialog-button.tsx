import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTrigger,
} from "./dialog";
import { Button } from "./button";
import { toast } from "./use-toast";
import { cn } from "@/lib/utils";

export default function DialogButton({
  children,
  button,
  headerText,
  okayText,
  variant,
  closedToastTitle,
  closedToastText,
  onOpenChange,
  callback,
  fullButton,
  className,
  small = false,
  ...props
}: {
  children: React.ReactNode;
  button?: React.ReactNode;
  okayText?: string;
  headerText?: string;
  closedToastTitle?: string;
  closedToastText?: string;
  fullButton?: React.ReactNode;
  className?: string;
  small?: boolean;
  onOpenChange?: (open: boolean) => void;
  variant?:
    | "link"
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | null
    | undefined;
  callback?: () => void;
  props?: any;
}) {
  const [open, setOpen] = React.useState(false);

  React.useEffect(() => {
    if (onOpenChange) {
      onOpenChange(open);
    }
  }, [open]);

  return (
    <>
      <Dialog onOpenChange={setOpen} open={open}>
        <DialogTrigger asChild>
          {!fullButton ? (
            <Button
              variant={variant}
              className={cn(small ? "p-1" : "", className)}
            >
              {button}
            </Button>
          ) : (
            fullButton
          )}
        </DialogTrigger>
        <DialogContent className="z-[10000]">
          <DialogHeader>
            <h2 className="text-lg font-bold leading-6 text-foreground">
              {headerText ? headerText : "Dialog"}
            </h2>
          </DialogHeader>
          {children}
          <DialogFooter>
            <Button
              variant={"default"}
              onClick={() => {
                if (closedToastText && closedToastTitle) {
                  toast({
                    title: closedToastTitle,
                    description: closedToastText,
                  });
                }
                setOpen(false);

                if (callback) {
                  callback();
                }
              }}
            >
              {okayText ? okayText : "OK"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
