import { StoreApi, create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

interface ChatbotState {
  isLoading: boolean;
  isStreaming: boolean;
  isParsingJSON: boolean;
  jsonBuffer: string;
  startDateTime: Date | null;
  input: string;
  chatSessionId: string;
  createdSession: boolean;
  quickReplies: string[] | undefined;
  generatedMessage: string;
  utterances: string[];
  messages: any[];
  preMessages: any[];
  initialDocumentsLoaded: boolean;
}

interface ChatbotActions {

  
  setIsLoading: (isLoading: boolean) => void;
  setIsStreaming: (isStreaming: boolean) => void;
  setIsParsingJSON: (isParsingJSON: boolean) => void;
  setJsonBuffer: (jsonBuffer: string) => void;
  setStartDateTime: (startDateTime: Date) => void;
  setInput: (input: string) => void;
  setChatSessionId: (chatSessionId: string) => void;
  setCreatedSession: (createdSession: boolean) => void;
  setQuickReplies: (quickReplies: string[]) => void;
  setGeneratedMessage: (generatedMessage: string) => void;
  setUtterances: (utterances: string[]) => void;
  setMessages: (messages: any[]) => void;
  setPreMessages: (preMessages: any[]) => void;
  setInitialDocumentsLoaded: (initialDocumentsLoaded: boolean) => void;

}

type ChatbotStore = ChatbotState & ChatbotActions ;


// Create the store with strong typing
const useChatbotStore = create<ChatbotStore>((set) => ({
  
  isLoading: false,
  isStreaming: false,
  isParsingJSON: false,
  jsonBuffer: "",
  startDateTime: null,
  input: "",
  chatSessionId: "",
  createdSession: false,
  quickReplies: undefined,
  generatedMessage: "",
  utterances: [],
  messages: [],
  preMessages: [],
  initialDocumentsLoaded: false,

  setIsLoading: (isLoading) => set({ isLoading }),
  setIsStreaming: (isStreaming) => set({ isStreaming }),
  setIsParsingJSON: (isParsingJSON) => set({ isParsingJSON }),
  setJsonBuffer: (jsonBuffer) => set({ jsonBuffer }),
  setStartDateTime: (startDateTime) => set({ startDateTime }),
  setInput: (input) => set({ input }),
  setChatSessionId: (chatSessionId) => set({ chatSessionId }),
  setCreatedSession: (createdSession) => set({ createdSession }),
  setQuickReplies: (quickReplies) => set({ quickReplies }),
  setGeneratedMessage: (generatedMessage) => set({ generatedMessage }),
  setUtterances: (utterances) => set({ utterances }),
  setMessages: (messages) => set({ messages }),
  setPreMessages: (preMessages) => set({ preMessages }),
  setInitialDocumentsLoaded: (initialDocumentsLoaded) => set({ initialDocumentsLoaded }),

}));

export const useChatbotStoreEasy = new Proxy(useChatbotStore, {
  get: function(target: StoreApi<ChatbotStore>, prop: keyof ChatbotStore | keyof StoreApi<ChatbotStore>) {
    const state = target.getState();
    const method = target[prop as keyof StoreApi<ChatbotStore>];
    if (typeof prop === 'string' && prop in state) {
      return state[prop as keyof ChatbotState];
    }
    if (typeof method === 'function') {
      return method;
    }
    return undefined;
  }
});


export default useChatbotStore;
