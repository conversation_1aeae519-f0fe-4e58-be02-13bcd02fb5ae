import requests
from llama_index.core.readers.base import BaseReader
from llama_index.core.schema import Document
import hashlib
from typing import Any, List

class CocurricularReader(BaseReader):

    def __init__(self ):
        """
        Initialize with GraphQL URL, headers, and query.
        """
        self.graphql_url = "https://dollaracademy.org.uk/wallapi/graphql"
        self.headers = {
            "Content-Type": "application/graphql",
        }
        
        self.graphql_url = "https://dollaracademy.org.uk/wallapi/graphql"

        self.payload = "query GetCoCurricularData {\r\n        coCurricularCards(first: 1000) {\r\n          nodes {\r\n            content\r\n            title\r\n            slug\r\n            coCurricular {\r\n              form {\r\n                nodes {\r\n                  name\r\n                  slug\r\n                  termTaxonomyId\r\n                }\r\n              }\r\n              coCurricularSubjectArea {\r\n                nodes {\r\n                  name\r\n                  slug\r\n                }\r\n              }\r\n              image {\r\n                node {\r\n                  mediaItemUrl\r\n                }\r\n              }\r\n              days {\r\n                nodes {\r\n                  name\r\n                  termTaxonomyId\r\n                  slug\r\n                }\r\n              }\r\n              time\r\n              venue\r\n              term {\r\n                nodes {\r\n                  name\r\n                  slug\r\n                  termTaxonomyId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n \r\n        coCurricularSubjectAreas(first: 1000, where: {orderby: TERM_ORDER}) {\r\n          nodes {\r\n            name\r\n            slug\r\n            termTaxonomyId\r\n          }\r\n        },\r\n \r\n        schoolTerms {\r\n          nodes {\r\n            name\r\n            slug\r\n            termTaxonomyId\r\n          }\r\n        },\r\n \r\n      }"
        self.headers = {
          'Content-Type': 'application/graphql'
        }


    def _execute_graphql_query(self):
        """
        Sends a GraphQL query to the URL and returns the result.
        """
        response = requests.request("POST", 
          self.graphql_url, 
          headers=self.headers, 
          data=self.payload)

        response.raise_for_status()  # Raise an error for failed requests
        return response.json()

    def load_data(self, *args: Any, **load_kwargs: Any) -> List[Document]:
        """
        Loads data by executing a GraphQL query and returns a list of Document objects.
        """
        results = self._execute_graphql_query()
        data = results.get('data', {}).get('coCurricularCards', {}).get('nodes', [])

        report_data = []
        for result in data:
            # Extracting information from the result
            title = result.get('title', 'Untitled')
            content = result.get('content', '')

            # Flattening school years
            school_years = ', '.join(
                form.get('name', '') for form in result.get('coCurricular', {}).get('form', {}).get('nodes', [])
            )

            # Flattening subject area
            subject_areas = ', '.join(
                area.get('name', '') for area in result.get('coCurricular', {}).get('coCurricularSubjectArea', {}).get('nodes', [])
            )

            # Flattening days
            days = ', '.join(
                day.get('name', '') for day in result.get('coCurricular', {}).get('days', {}).get('nodes', [])
            )

            # Extracting time, venue, and term
            time = result.get('coCurricular', {}).get('time', 'Not specified')
            venue = result.get('coCurricular', {}).get('venue', 'Not specified')
            terms = ', '.join(
                term.get('name', '') for term in result.get('coCurricular', {}).get('term', {}).get('nodes', [])
            )

            s_content = """Content: {content}""" if content else ""
            s_school_years = """School Years: {school_years}""" if school_years else ""
            s_subject_areas = """Subject Areas: {subject_areas}""" if subject_areas else ""
            s_days = """Days: {days}""" if days else ""
            s_time = """Time: {time}""" if time else ""
            s_venue = """Venue: {venue}""" if venue else ""
            s_terms = """Term: {terms}""" if terms else ""
            # Constructing the document content
            the_content = f"""
            Title: {title}
            {s_content}
            {s_school_years}
            {s_subject_areas}
            {s_days}
            {s_time}
            {s_venue}
            {s_terms}
            
            """

            # Creating the Document object
            doc_hash = hashlib.md5(the_content.encode()).hexdigest()
            document = Document(text=the_content, doc_id=result.get('slug', 'unknown'), doc_hash=doc_hash)
            report_data.append(document)

        return report_data
