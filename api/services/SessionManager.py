# from firebase_admin import firestore

from firebase_admin import firestore

from google.cloud.firestore import Client

class SessionManager:

  database: Client
    
  def __init__(self, database: Client):
    self.database = database
    
  def init_session(self, tenant_id: str, initial_question: str, user_ip: str, browser: str, os: str, device_type: str):
  
    firestore_now = firestore.SERVER_TIMESTAMP

    # Corrected 'data' dictionary
    data = {
        'created': firestore_now,
        'lastUpdated': firestore_now,
        'messageCount': 0,
        'userIp': user_ip,
        'browser': browser,
        'os': os,
        'deviceType': device_type,
        'initialQuestion': initial_question
    }
    
    database = firestore.client()

    update_time, chat_session_ref = (
      database.collection('tenants')
      .document(tenant_id)
      .collection('chatSessions')
      .add(data))

    return chat_session_ref.id