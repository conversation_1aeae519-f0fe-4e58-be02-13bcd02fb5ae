
import json
import os
from typing import Any, List
import asyncio
import time

from collections import namedtuple

import firebase_admin
from firebase_admin import firestore_async
from firebase_admin import firestore

from firebase_admin import firestore
from services.categories_service import get_flattened_categories
from models.report_request import ReportRequest, BasicResponse
from models.chat_response import ChatResponse

from models.tenant import Tenant
from repositories.tenant_repository import TenantRepository
from services.link_validator import LinkValidator
from llama_index.core.llms import ChatMessage

from services.model_service import ModelService
from llama_index.core.chat_engine.types import BaseChatEngine
from google.cloud.firestore import Client
from google.cloud.firestore_v1.base_query import FieldFilter, Or

from llama_index.core.chat_engine.types import StreamingAgentChatResponse

import llama_index

import json


class ChatMessageManager:
  
  def __init__(self, database: Client, tenant_id: str, chat_session_id: str):
    self.database = database
    self.tenant_id = tenant_id
    self.chat_session_id = chat_session_id
    self.step_number = 0
    
    self.chat_session_ref = (
      self.database
          .collection('tenants')
          .document(self.tenant_id)
          .collection('chatSessions')
          .document(self.chat_session_id)
    )
  
  def write_user_message(self, text: str):

    self.chat_session_ref.collection("messages").add(
        {
            "role": "user",
            "message": text,
            "createdAt": firestore.SERVER_TIMESTAMP,
        }
    )
    
    self.increment_message_count()
    
  def increment_message_count(self):
    self.chat_session_ref.update(
        {
            "messageCount": firestore.Increment(2),
            "lastUpdated": firestore.SERVER_TIMESTAMP,
        }
    )
    
  def write_agent_message(self, text: str):
    self.chat_session_ref.collection("messages").add(
        {
            "role": "assistant",
            "message": text,
            "createdAt": firestore.SERVER_TIMESTAMP,
        }
    )
    self.increment_message_count()
    
  def write_links_message(self, text: str):
    self.chat_session_ref.collection("messages").add(
        {
            "role": "links",
            "message": text,
            "createdAt": firestore.SERVER_TIMESTAMP,
        }
    )
    self.increment_message_count()
    
  def write_agent_step(self, text: str):

    if(self.step_number == 0):    
      self.chat_session_ref.collection("messages").add(
          {
              "role": "assistant",
              "message": f"{text}",
              "createdAt": firestore.SERVER_TIMESTAMP,
          }
      )
      self.increment_message_count()
      
    self.step_number += 1
    

  def reset_steps(self):
    self.step_number = 0
    
  
  def write_waiting_message(self, text: str):
    self.chat_session_ref.collection("messages").add(
        {
            "role": "assistant",
            "message": f"[Waiting] {text}",
            "createdAt": firestore.SERVER_TIMESTAMP,
        }
    )
    self.increment_message_count()
    
    