// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
// import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import {
  connectFunctionsEmulator,
  getFunctions,
  httpsCallable,
} from "firebase/functions";

const originalWarn = console.warn;

console.warn = (message, ...args) => {
  const warningsToIgnore = [
    "validateDOMNesting",
    'he pseudo class ":first-child" is potentially unsafe when doing server-side rendering.',
  ];

  if (!warningsToIgnore.some((warning) => message.includes(warning))) {
    originalWarn(message, ...args);
  }
};

// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyDVZ1AVEY9mS7LX2lN3GGt2JJE7NfjXZJw",
  authDomain: "multiply-chatbot.firebaseapp.com",
  projectId: "multiply-chatbot",
  storageBucket: "multiply-chatbot.appspot.com",
  messagingSenderId: "447327165408",
  appId: "1:447327165408:web:3822f312d43bc64e0ba985",
  measurementId: "G-YQN10K2R90",
};

// Initialize Firebase
const firebaseApp = initializeApp(firebaseConfig);
const auth = getAuth(firebaseApp);
const db = getFirestore(firebaseApp);
// const analytics = getAnalytics(firebaseApp);
const functions = getFunctions(firebaseApp, "europe-west1");
// connectFunctionsEmulator(functions, "127.0.0.1", 5001);

auth.useDeviceLanguage();

export { firebaseApp, auth, db, functions, httpsCallable };
