import { database } from "@/config/firebaseAdmin";
import { jsonResponse } from "@/lib/apiUtils";
import { Message } from "@/models/message";
import { Report, ReportRequest } from "@/models/report";
import { getFirebaseApiEndpoints } from "@/services/api-connector/api-connector";
import { fetchGPT4ProcessedFeedback } from "@/services/systemchat-service";
import { Filter } from "firebase-admin/firestore";

export async function POST(request: Request) {
  try {
    const report = await getValidatedReport(request);

    const messageRef = database
      .collection("tenants")
      .doc(report.tenantId)
      .collection("chatSessions")
      .doc(report.chatSessionId)
      .collection("messages")
      .doc(report.id);

    const value = (await messageRef.get()).data();


    if (!value) {
      return jsonResponse(400, {
        success: false,
        message: "Message not found",
      });
    }

    if (value.message === report.message) {
      // Let's check first if a similar reply has been sent before OR if the message has been reported before (by id)
      const similarReports = await database
        .collection("tenants")
        .doc(report.tenantId)
        .collection("reports")
        .where(
          Filter.or(
            Filter.where("message", "==", report.message),
            Filter.where("id", "==", report.id)
          )
        )
        .get();


      if (similarReports.size > 0) {
        return jsonResponse(400, {
          success: false,
          message: "Message has been reported before",
        });
      }

      const processedFeedback = await fetchGPT4ProcessedFeedback({
        messages: [
          {
            role: "user",
            message: report.previousMessage,
          },
          {
            role: "assistant",
            message: report.message,
          },
        ],
        positiveFeedback: report.positiveFeedback,
        tenantId: report.tenantId,
      });

      const reportToAdd = {
        chatSessionId: report.chatSessionId,
        messageId: report.id,
        message: report.message,
        status: "backlog",
        previousMessage: report.previousMessage,
        createdAt: new Date(),
        positiveFeedback: report.positiveFeedback,
      } as Report;

      if (processedFeedback) {
        reportToAdd.category = processedFeedback.category;
        if (processedFeedback.confidenceIndex > 0.8) {
          reportToAdd.correctReply =
            processedFeedback.correctReplyAccordingToData;
        }
      }

      await database
        .collection("tenants")
        .doc(report.tenantId)
        .collection("reports")
        .add(reportToAdd);

      await messageRef.update({
        reported: true,
      });

      return jsonResponse(200, {
        success: true,
        message: "Message was reported successfully",
      });
    } else {
      return jsonResponse(400, {
        success: false,
        message: "Message does not match the one in the chat session",
      });
    }
  } catch (e: any) {
    return jsonResponse(400, { message: e.message });
  }
}

async function getValidatedReport(request: Request): Promise<ReportRequest> {
  const contentType = request.headers.get("content-type");

  if (!contentType || !contentType.includes("application/json")) {
    throw new Error("Content type should be application/json");
  }

  const body = await request.json();

  try {
    const message = body as ReportRequest;

    if (!message) {
      throw new Error("Request body is not a valid JSON");
    }
    return message;
  } catch (e) {
    throw new Error("Request body is not a valid JSON");
  }
}
