import * as React from "react";
import {
  CaretSortIcon,
  CheckIcon,
  PlusCircledIcon,
} from "@radix-ui/react-icons";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Skeleton } from "./skeleton";
import useDashboardStore from "@/stores/dashboard.store";
import { cn } from "@/lib/utils";
import { useEffect } from "react";
import { Textarea } from "./textarea";
import {
  AiFillDelete,
  AiOutlineDelete,
  AiOutlineDeleteRow,
  AiOutlineEdit,
} from "react-icons/ai";
import { DeleteIcon } from "lucide-react";
import DialogButton from "./dialog-button";
import { set } from "zod";

type BaseDocument = {
  id: string;
  label: string;
  description: string;
};

interface SwitcherProps<T extends BaseDocument> {
  documents: T[];
  value?: string;
  save?: (doc: T) => Promise<void>;
  remove?: (id: string) => Promise<void>;
  term?: string;
  onChange?: (doc: T) => void;
  editable?: boolean;
}

export default function Switcher<T extends BaseDocument>({
  documents,
  save,
  remove,
  onChange,
  value,
  term = "document",
  editable = true,
}: SwitcherProps<T>) {
  const [open, setOpen] = React.useState(false);
  const [showNewDocumentDialog, setShowNewDocumentDialog] =
    React.useState(false);
  const [isLoading, setIsLoading] = React.useState(true);

  const [newDocument, setNewDocument] = React.useState<T | null>(null);
  const [editDocument, setEditDocument] = React.useState<T | null>(null);

  const selectedClient = useDashboardStore((state) => state.selectedClient);

  const [selectedDocument, setSelectedDocument] = React.useState<T | null>();

  const createNewDocument = async () => {
    if (!selectedClient || !newDocument) {
      return;
    }

    if (save) {
      await save(newDocument);
    }
  };

  useEffect(() => {
    if (value && documents) {
      const doc = documents.find((doc) => doc.id == value);
      if (doc) {
        setSelectedDocument(doc);
      }
    }
  }, [value, documents]);

  useEffect(() => {
    if (documents) {
      setIsLoading(false);
    }
  }, [documents]);

  const pluralizeTerm = () => {
    // If term ends with 'y', remove the 'y' and add 'ies'
    // If term ends with 's', add 'es'
    // Otherwise, add 's'

    if (term.endsWith("y")) {
      return term.slice(0, -1) + "ies";
    } else if (term.endsWith("s")) {
      return term + "es";
    } else {
      return term + "s";
    }
  };

  const [shouldBeFront, setShouldBeFront] = React.useState(true);

  return (
    <>
      {isLoading && <Skeleton className="min-w-[212px] h-[36px]" />}
      {!isLoading && (
        <Dialog
          open={showNewDocumentDialog}
          onOpenChange={setShowNewDocumentDialog}
        >
          <Popover open={open} onOpenChange={setOpen}>
            {selectedClient && (
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  aria-label={`Select a ${term}`}
                  className={cn(" w-full justify-between")}
                >
                  {selectedDocument?.label || `Select a ${term}`}
                  <CaretSortIcon className="w-4 h-4 ml-auto opacity-50 shrink-0" />
                </Button>
              </PopoverTrigger>
            )}
            <PopoverContent
              className={cn("p-0 shadow-lg z-[9999]")}
              style={{
                display: shouldBeFront ? "block" : "none",
              }}
            >
              <Command className=" z-[100]">
                <CommandList>
                  <CommandInput placeholder={"Search " + term + "..."} />
                  <CommandEmpty>No {pluralizeTerm()} found.</CommandEmpty>
                  <CommandGroup heading={pluralizeTerm()}>
                    {documents.map((doc: T) => (
                      <div className="flex">
                        <CommandItem
                          key={doc.label}
                          onSelect={() => {
                            setSelectedDocument(doc);
                            onChange && onChange(doc);
                            setOpen(false);
                          }}
                          className="flex-1 text-sm"
                        >
                          {doc.label}
                          <CheckIcon
                            className={cn(
                              "ml-auto h-4 w-4",
                              selectedDocument?.id === doc.id
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </CommandItem>
                        {editable && (
                          <>
                            <DialogButton
                              small={true}
                              button={<AiOutlineEdit />}
                              headerText={`Edit ${term}`}
                              okayText="Save"
                              onOpenChange={(open) => {
                                setShouldBeFront(!open);
                                setEditDocument(doc);
                              }}
                              variant={null}
                              closedToastText={`${term} saved`}
                              closedToastTitle="Success!"
                              callback={() => save && save(editDocument as T)}
                            >
                              <div className="py-2 pb-4 space-y-4">
                                <div className="space-y-2">
                                  <Label htmlFor="name">{term} name</Label>
                                  <Input
                                    id="name"
                                    placeholder={term + " name"}
                                    value={editDocument?.label}
                                    onChange={(e) => {
                                      console.log(e.target.value);
                                      setEditDocument({
                                        ...editDocument,
                                        label: e.target.value,
                                      } as T);
                                    }}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="description">
                                    Description
                                  </Label>
                                  <Textarea
                                    id="description"
                                    placeholder="Description"
                                    value={editDocument?.description}
                                    onChange={(e) =>
                                      setEditDocument({
                                        ...editDocument,
                                        description: e.target.value,
                                      } as T)
                                    }
                                  />
                                </div>
                              </div>
                            </DialogButton>
                            <DialogButton
                              small={true}
                              button={<AiOutlineDelete />}
                              headerText="Delete"
                              okayText="Delete"
                              variant={null}
                              closedToastText={`${term} deleted`}
                              onOpenChange={(open) => {
                                setShouldBeFront(!open);
                              }}
                              closedToastTitle="Success!"
                              callback={() => remove && remove(doc.id)}
                            >
                              Are you sure you want to delete this {term}?
                            </DialogButton>
                          </>
                        )}
                      </div>
                    ))}
                  </CommandGroup>
                </CommandList>
                <CommandSeparator />
                <CommandList>
                  <CommandGroup>
                    <DialogTrigger asChild>
                      <CommandItem
                        onSelect={() => {
                          setOpen(false);
                          setShowNewDocumentDialog(true);
                        }}
                      >
                        <PlusCircledIcon className="w-5 h-5 mr-2" />
                        Create new {term}
                      </CommandItem>
                    </DialogTrigger>
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create new {term}</DialogTitle>
              <DialogDescription>
                This will add the {term} to your {pluralizeTerm()}.
              </DialogDescription>
            </DialogHeader>
            <div>
              <div className="py-2 pb-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">{term} name</Label>
                  <Input
                    id="name"
                    placeholder={term + " name"}
                    value={newDocument?.label}
                    onChange={(e) =>
                      setNewDocument({
                        ...newDocument,
                        label: e.target.value,
                      } as T)
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Description"
                    value={newDocument?.description}
                    onChange={(e) =>
                      setNewDocument({
                        ...newDocument,
                        description: e.target.value,
                      } as T)
                    }
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowNewDocumentDialog(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                onClick={() => {
                  createNewDocument();
                  setShowNewDocumentDialog(false);
                }}
              >
                Continue
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
