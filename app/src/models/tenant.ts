export type WordPressConfig = {
  url: string;
  username: string;
  password: string;
  openAIKey: string;
  consumerKey: string;
  consumerSecret: string;
};

export type OpenAIConfig = {
  model: string;
  openAIKey: string;
  temperature: number;
  topK?: number;
};

export type WebsiteConfig = {
  defaultSelector: string;
};

export type WidgetConfig = {
  chatTitle: string;
  assistantName: string;
  buttonColour: string;
  buttonIcon: string;
  buttonIconColour: string;
  buttonPosition: string;
  shadow: string;
};

export type AnalyticsConfig = {
  gaId: string;
};

export type Tenant = {
  id?: string;
  name: string;
  description: string;
  openai?: OpenAIConfig;
  systemMessages?: string[];
  website?: WebsiteConfig;
  widget?: WidgetConfig;
  wordPressConfig?: WordPressConfig;
  analytics?: AnalyticsConfig;
};
