# from firebase_admin import firestore
from typing import Any
from firebase_admin import firestore

from models.tenant import Tenant

from google.cloud.firestore import Client

class TenantRepository:

  database: Client
  
  def __init__(self, database: Client):
    self.database = database

  def get_documents_query(self, tenant_id: str):
    return self.database.collection('tenants').document(tenant_id).collection('documents')

  def get_chats_query(self, tenant_id: str):
    return self.database.collection('tenants').document(tenant_id).collection('chatSessions').order_by('lastUpdated', direction=firestore.Query.DESCENDING).limit(20)

  def get_reports_query(self, tenant_id: str):
    return self.database.collection('tenants').document(tenant_id).collection('reports').order_by('createdAt', direction=firestore.Query.DESCENDING).limit(50)

  def add_tenant(self, tenant: Tenant):
    doc_ref = self.database.collection('tenants').add(tenant.to_dict())
    return doc_ref.id
  
  def get_tenant(self, tenant_id: str) -> Tenant:
    doc_ref = self.database.collection('tenants').document(tenant_id).get()
    
    # Turn into a basic dict first
    tenant = doc_ref.to_dict()
    # Add the ID
    tenant["id"] = doc_ref.id
    
    
    return Tenant.from_dict(tenant)
  
  
