"use client";

import PageContainer from "@/components/ui/page-container";

import { Report } from "@/models/report";
import { getReportsQuery } from "@/repositories/tenant-repository";
import useDashboardStore from "@/stores/dashboard.store";

import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn, prettyDate } from "@/lib/utils";
import Bubble from "@/components/chat/bubble";
import { Button } from "@/components/ui/button";
import DialogButton from "@/components/ui/dialog-button";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  limit,
  onSnapshot,
  orderBy,
  query,
  where,
} from "firebase/firestore";
import { db } from "@/services/firebase.config";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import ReportBubble from "./report-bubble";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { Icons } from "@/components/Icons";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { reauthenticateWithRedirect } from "firebase/auth";
import "./feedback.scss";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useFirestoreSync } from "@/lib/firebase-hooks";
import ReportCard from "./report-card";
import FeedbackTag from "./components/feedback-tag";
import FeedbackFilters, { Filters } from "./components/feeback-filters";
import { TablePagination } from "./components/table-pagination";

function AddToAIButton({
  document,
  addToTrainingData,
  fullButton,
}: {
  document: Report;
  fullButton?: React.ReactNode;
  addToTrainingData: (document: Report) => void;
}) {
  return (
    <DialogButton
      variant={"outline"}
      fullButton={fullButton}
      callback={() => {
        addToTrainingData(document);
      }}
      okayText="Add to training data"
      headerText="Are you sure?"
      closedToastTitle="Success!"
      closedToastText="The AI's answer has been added to the training data."
      button={<span className="text-xs">Add AI reply to training data</span>}
    >
      <p>
        By adding this reply to the training data, you are helping the AI learn
        how to answer this question in the future.
      </p>
      <ReportBubble short={false} document={document} role="user" />
      <ReportBubble short={false} document={document} role="assistant" />
    </DialogButton>
  );
}

function ImproveAIButton({
  document,
  newReply,
  setNewReply,
  addToTrainingData,
  fullButton,
}: {
  document: Report;
  newReply: string | null;
  setNewReply: (newReply: string) => void;
  addToTrainingData: (document: Report, newReply?: string) => void;
  fullButton?: React.ReactNode;
}) {
  return (
    <DialogButton
      variant={"outline"}
      callback={() => {
        addToTrainingData(document, newReply ?? "");
      }}
      okayText="Change and Add to training data"
      fullButton={fullButton}
      headerText="Change Reply"
      closedToastTitle="Success!"
      closedToastText="The AI's answer has been updated and added to the training data."
      button={
        <span className="text-xs">Change reply and add to training data</span>
      }
    >
      <p>
        By adding this reply to the training data, you are helping the AI learn
        how to answer this question in the future.
      </p>
      <ReportBubble short={false} document={document} role="user" />
      <Textarea
        defaultValue={newReply ?? ""}
        onChange={(e) => setNewReply(e.target.value)}
        placeholder="Enter the new reply here..."
      />
    </DialogButton>
  );
}

function RemoveFromQueueButton({
  document,
  removeFromQueue,
  fullButton,
}: {
  document: Report;
  removeFromQueue: (document: Report) => void;
  fullButton?: React.ReactNode;
}) {
  return (
    <DialogButton
      variant={"secondary"}
      fullButton={fullButton}
      callback={() => {
        removeFromQueue(document);
      }}
      okayText="Remove from queue"
      headerText="Are you sure?"
      closedToastTitle="Success!"
      closedToastText="The report has been removed from the queue."
      button={<span className="text-xs">Remove from queue</span>}
    >
      Are you sure you want to remove this report from the queue?
      <ReportBubble short={false} document={document} role="user" />
      <ReportBubble short={false} document={document} role="assistant" />
    </DialogButton>
  );
}

function ManageReply({
  document,
  role,
  short = true,
  addToTrainingData,
  removeFromQueue,
  newReply,
  setNewReply,
}: {
  document: Report | null;
  role: "user" | "assistant";
  short?: boolean;
  addToTrainingData: (document: Report, newReply?: string) => void;
  removeFromQueue: (document: Report) => void;
  newReply: string | null;
  setNewReply: (newReply: string) => void;
}) {
  return (
    <>
      {document && (
        <>
          <ReportBubble short={false} document={document} role="user" />
          <ReportBubble short={false} document={document} role="assistant" />
          <div className="flex gap-2">
            {document.positiveFeedback ? (
              <AddToAIButton
                document={document}
                addToTrainingData={addToTrainingData}
              />
            ) : (
              <ImproveAIButton
                document={document}
                newReply={newReply}
                setNewReply={setNewReply}
                addToTrainingData={addToTrainingData}
              />
            )}
            <RemoveFromQueueButton
              document={document}
              removeFromQueue={removeFromQueue}
            />
          </div>
        </>
      )}
    </>
  );
}

export default function FeedbackPage() {
  const selectedClient = useDashboardStore((state) => state.selectedClient);
  const [newReply, setNewReply] = useState<string | null>(null);
  const [currentFilter, setCurrentFilter] = useState<Filters>({
    category: "",
    status: "",
  });

  const queryConstraints = useMemo(
    () => [orderBy("createdAt", "desc"), limit(21)],
    []
  );
  const {
    paginatedDocuments: documents,
    save: saveDocument,
    remove: deleteDocument,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    currentPage,
    maxPages,
    totalDocuments,
  }

    = useFirestoreSync<Report>(
      `tenants/${selectedClient?.value}/reports`,
      queryConstraints
    );

  const [categories, setCategories] = useState<any[]>([]);

  const processCategories = async () => {
    const docs = await getDocs(
      collection(db, "tenants", selectedClient?.value, "reportCategories")
    );

    if (docs) {
      const categories = docs.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      setCategories(categories);
    }
  };

  useEffect(() => {
    if (selectedClient) {
      processCategories();
    }
  }, [selectedClient]);

  const addToTrainingData = async (document: Report, newReply?: string) => {
    await addDoc(
      collection(db, `tenants/${selectedClient.value}/corrections`),
      {
        ...document,
        correctReply: newReply || document.message,
      }
    );

    await deleteDoc(
      doc(db, `tenants/${selectedClient.value}/reports/${document.id}`)
    );

    setNewReply(null);
  };

  const removeFromQueue = async (document: Report) => {
    await deleteDoc(
      doc(db, `tenants/${selectedClient.value}/reports/${document.id}`)
    );
  };

  const shorten = (message: string) => {
    // Take first 10 words, then add "..." if needed
    const words = message.split(" ");
    if (words.length > 10) {
      return words.slice(0, 10).join(" ") + "...";
    } else {
      return message;
    }
  };

  const [filteredDocuments, setFilteredDocuments] = useState<Report[]>([]);

  useEffect(() => {
    if (!documents || !currentFilter) {
      return;
    }

    const docs = documents.filter((document) => {
      if (currentFilter.category && currentFilter.category !== "") {
        if (document.category !== currentFilter.category) {
          return false;
        }
      }

      if (currentFilter.status && currentFilter.status !== "") {
        if (document.status !== currentFilter.status) {
          return false;
        }
      }

      return true;
    });

    setFilteredDocuments(docs);
  }, [documents, currentFilter]);

  const [selectedDocument, setSelectedDocument] = useState<Report | null>(null);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const reportStatuses = useDashboardStore((state) => state.reportStatuses);

  return (
    <PageContainer>
      <div className="prose">
        <h2>Reports</h2>
      </div>
      <div className="relative">
        <FeedbackFilters
          categories={categories}
          onFilterChange={(filters) => {
            setCurrentFilter(filters);
          }}
        />
        {filteredDocuments.length == 0 ? (
          <div className="mt-4 text-sm text-left text-muted-foreground">
            No reports found.
          </div>
        ) : (
          <>
            <Table className="mt-4">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">Type</TableHead>
                  <TableHead className="w-[150px]">Date</TableHead>
                  <TableHead className="w-[150px]">Status</TableHead>
                  <TableHead className="w-[150px]">Category</TableHead>
                  <TableHead className="w-[250px]">User</TableHead>
                  <TableHead className="">Assistant</TableHead>
                  <TableHead className="w-[150px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categories &&
                  reportStatuses &&
                  filteredDocuments &&
                  filteredDocuments.map((document, index) => {
                    return (
                      <TableRow
                        className={cn(
                          index % 2 == 1 ? "bg-muted/20" : "bg-background",
                          "cursor-pointer relative"
                        )}
                        key={`${document.id}-${index}`}
                        onClick={() => setSelectedDocument(document)}
                        onMouseEnter={() => setHoveredIndex(index)}
                        onMouseLeave={() => setHoveredIndex(null)}
                      >
                        <TableCell className="font-bold">
                          <FeedbackTag report={document} />
                        </TableCell>
                        <TableCell className="text-xs font-medium text-muted-foreground">
                          {prettyDate(document.createdAt.toDate())}
                        </TableCell>
                        <TableCell className="font-bold">
                          {reportStatuses &&
                            reportStatuses.find(
                              (s: any) => s.id == document.status
                            )?.label}
                        </TableCell>
                        <TableCell className="font-bold">
                          {categories &&
                            categories.find((c) => c.id == document.category)
                              ?.label}
                        </TableCell>
                        <TableCell className="text-xs">
                          {shorten(document.previousMessage)}
                        </TableCell>
                        <TableCell className="relative text-xs font-semibold">
                          <>
                            <div
                              className={cn(
                                "absolute inset-0 flex items-center truncate "
                              )}
                            >
                              <div
                                className=""
                                dangerouslySetInnerHTML={{
                                  __html: document.message,
                                }}
                              ></div>
                            </div>
                            <div
                              className={cn(
                                "absolute inset-y-0 right-0 transition-all flex items-center truncate from-transparent bg-gradient-to-r",
                                hoveredIndex === index
                                  ? "to-muted/50"
                                  : "to-background",
                                "w-[80px] pl-4"
                              )}
                            ></div>
                          </>
                        </TableCell>
                        <TableCell className="text-xs">
                          {hoveredIndex === index && (
                            <span className="underline">Manage</span>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
              </TableBody>
            </Table>
            <TablePagination
              currentPage={currentPage}
              maxPages={maxPages}
              onChange={(pageNumber) => {
                if (pageNumber > currentPage) {
                  goToNextPage();
                } else if (pageNumber < currentPage) {
                  goToPreviousPage();
                }
              }}
            />
          </>
        )}
        <ReportCard
          report={selectedDocument}
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              setSelectedDocument(null);
            }
          }}
        />
      </div>
    </PageContainer>
  );
}
