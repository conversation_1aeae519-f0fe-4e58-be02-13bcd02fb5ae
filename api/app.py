
import json
import uuid
from quart import Quart, Request, Response, request, jsonify
from quart import make_response

import os
import io
from firebase_admin import initialize_app
import firebase_admin
from firebase_admin import firestore


# import openai

#set the openai key globally

import openai

from services.TrainingService import TrainingService
from services.LLMService import LLMService
from services.VectorDBManager import VectorDBManager

from user_agents import parse
from quart_cors import cors

from datetime import datetime, timedelta
from llama_index.core.chat_engine.types import StreamingAgentChatResponse
from llama_index.core.callbacks.global_handlers import set_global_handler
from services.SessionManager import SessionManager
from services.WorkflowExecutor import WorkflowExecutor
from services.LlamaIndexService import LlamaIndexService

set_global_handler("simple")
# Set OpenAI API key env here without calling os or openai

os.environ["OPENAI_API_KEY"] = "********************************************************"



allowed_origins = [
    "https://multiply-chatbot.web.app",
    "http://localhost:3000",
    "http://localhost:61270",
    "https://app.chatnav.co.uk",
    "https://sleepeezee.com",
    "https://sleepeezee.multiapps.net",
    "http://sleepeezee.prox/",
    "https://dollar.multiapps.net/",
    "*"
]
app = Quart(__name__)
app = cors(app, allow_origin=allowed_origins)

# Initialize Firebase
cwd = os.path.dirname(os.path.abspath(__file__))
cert_path = os.path.join(cwd, "credentials/firebase.json")
cred = firebase_admin.credentials.Certificate(cert_path)
firebase_app = firebase_admin.initialize_app(cred)
llama_index_service = LlamaIndexService(100)
database = firestore.client() 
session_manager = SessionManager(database)

llm_service = LLMService()

training_service = TrainingService(database, llm_service, llama_index_service)


@app.route('/create_session', methods=['POST'])
async def create_session() -> Response:
    
  request_data = await request.get_json()
  
  tenant_id = request_data["tenantId"]
  initial_question = request_data["initialQuestion"]

  ua_string = request.headers.get("User-Agent")
  user_agent = parse(ua_string)
  
  browser = user_agent.browser.family  # e.g. 'Mobile Safari'
  os = user_agent.os.family  # e.g. 'iOS'
  device_type = "Desktop"
  if user_agent.is_mobile:
      device_type = "Mobile"
  elif user_agent.is_tablet:
      device_type = "Tablet"

  user_ip = request.remote_addr
  
  session_id = session_manager.init_session(
    tenant_id,
    initial_question,
    user_ip,
    browser,
    os,
    device_type
  )

  return jsonify({"sessionId": session_id, "status": "success"}), 200

@app.route('/chat', methods=['POST'])
async def chat() -> Response:
  
  request_data = await request.get_json()
  
  query_text = request_data["text"]
  
  query_tenantid = request_data["tenantId"]
  query_sessionid = request_data["sessionId"]
  query_metadata = request_data.get("metadata", None)
  
  from services.ChatMessageService import ChatMessageManager
  chat_message_service = ChatMessageManager(
    database,
    query_tenantid,
    query_sessionid
  )
  chat_message_service.write_user_message(query_text)
  
  workflow_executor = WorkflowExecutor(query_tenantid, database, llm_service, llama_index_service)
  response = workflow_executor.execute(query_text, query_sessionid)
  
  
  
  return jsonify({"status": "success"}), 200    

@app.route('/retrain', methods=['POST'])
async def retrain() -> Response:
    request_data = await request.get_json()
    query_tenantid = request_data["tenantId"]

    result: bool = False

    try:
      training_service.train(query_tenantid)
      
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

    if result:
        return jsonify({"status": "success"}), 200

    return jsonify({"status": "error"}), 500


    

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
