import React, { useState, useRef } from "react";
import { BiMicrophone } from "react-icons/bi";
import { Button } from "../ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

type Props = {
  sendMessage: (
    text: string,
    isText: boolean,
    file: File | null
  ) => Promise<void>;
};

export default function MicrophoneButton({ sendMessage }: Props) {
  const [isRecording, setIsRecording] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const startRecording = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    // Setup AudioContext and Analyser
    const audioContext = new AudioContext();
    const sourceNode = audioContext.createMediaStreamSource(stream);
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 256;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    sourceNode.connect(analyser);

    let silenceStart = Date.now();
    let isSilent = false;
    let activityCounter = 0; // Track non-silence

    const checkSilence = () => {
      analyser.getByteTimeDomainData(dataArray);
      const sum = dataArray.reduce((acc, val) => acc + Math.abs(val - 128), 0);
      const avg = sum / bufferLength;

      if (avg < 3) {
        if (!isSilent) {
          isSilent = true;
          silenceStart = Date.now();
        } else if (Date.now() - silenceStart > 2000) {
          stopRecording();
        }
      } else {
        isSilent = false;
        activityCounter++; // Increment activity counter when non-silence detected
      }
    };

    const checkInterval = setInterval(checkSilence, 100);

    mediaRecorderRef.current = new MediaRecorder(stream);
    mediaRecorderRef.current.ondataavailable = (event) => {
      audioChunksRef.current.push(event.data);
    };
    mediaRecorderRef.current.onstop = async () => {
      clearInterval(checkInterval);
      audioContext.close();

      const audioBlob = new Blob(audioChunksRef.current, { type: "audio/wav" });
      const audioFile = new File([audioBlob], "recording.wav", {
        type: "audio/wav",
      });

      // Check if there was significant activity before sending
      if (audioChunksRef.current.length > 0 && activityCounter > 5) {
        // Assuming 3 seconds of audio activity is substantial
        await sendMessage("", false, audioFile);
      }
      audioChunksRef.current = [];
    };

    mediaRecorderRef.current.start();
    setIsRecording(true);
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const toggleRecording = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <Button
            variant={"outline"}
            onClick={toggleRecording}
            className={isRecording ? "bg-red-500" : ""}
          >
            <BiMicrophone
              className={`inline-block text-[20px] ${
                isRecording ? "text-white" : ""
              }`}
            />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>
            {isRecording
              ? "Click to stop recording"
              : "Click to start recording"}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
