import React from 'react'
import './widget.css'
import Chat from "@/components/chat";
import { getTenant } from '@/repositories/server-tenant-repository';

export default async function ChatWidget({ params: { tenantId } }) {



  const tenant = await getTenant(tenantId)


  return (
    <div className="light">
      <div className="chat-widget">
        <Chat tenantId={tenantId} tenant={tenant} />
      </div>
    </div>
  )
}
