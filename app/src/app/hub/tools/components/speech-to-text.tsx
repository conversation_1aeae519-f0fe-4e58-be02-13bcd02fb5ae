"use client";
import React, { useState } from "react";
import { getFirebaseApiEndpoints } from "@/services/api-connector/api-connector";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";

const SpeechToText: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [transcript, setTranscript] = useState<string>("");
  const endpoints = getFirebaseApiEndpoints();
  const [loading, setLoading] = useState<boolean>(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files && event.target.files[0];

    if (!selectedFile) {
      toast({
        title: "No file selected",
        description: "Please select a file.",
      });
      return;
    }

    if (selectedFile?.size > 25 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select a file smaller than 25MB.",
      });
    }

    if (selectedFile) {
      setFile(selectedFile);
    }
  };

  const handleSubmit = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please select a file.",
      });
      return;
    }

    try {
      setLoading(true);
      const response = await endpoints.speechToText(file);
      setTranscript(response.corrected_text); // assuming this is the structure
      setLoading(false);
    } catch (error) {
      console.error("Error uploading file:", error);
      toast({
        title: "Error uploading file",
        description: "Please try again.",
      });
    }
  };

  const downloadTranscript = async () => {
    // This downloads the transcript as a text file
    const element = document.createElement("a");

    const file = new Blob([transcript], {
      type: "text/plain",
    });

    element.href = URL.createObjectURL(file);
    element.download = "transcript.txt";

    document.body.appendChild(element);
    element.click();

    document.body.removeChild(element);
  };

  return (
    <div className="container mx-auto my-8">
      <Card>
        <CardHeader>
          <CardTitle>Speech to Text</CardTitle>
          <CardDescription>
            This tool allows you to create a transcript from an audio file.
          </CardDescription>
        </CardHeader>
        <CardContent
          className={
            loading
              ? "opacity-50 pointer-events-none relative mb-8"
              : "relative mb-8"
          }
        >
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
              </div>
            </div>
          )}
          <div className="grid w-full max-w-sm items-center gap-1.5 mb-4">
            <Label htmlFor="audio">Audio file (mp3)</Label>
            <Input
              id="audio"
              type="file"
              onChange={handleFileChange}
              accept="audio/mp3"
            />
          </div>
          <Button variant={"default"} onClick={handleSubmit}>
            Get Transcript
          </Button>
          {transcript && (
            <div className="my-16">
              <Label>Transcript</Label>
              <div
                className="prose bg-slate-500/10 p-8 max-w-full rounded-lg "
                dangerouslySetInnerHTML={{ __html: transcript }}
              ></div>
              <Button
                variant={"default"}
                className="mt-4"
                onClick={() => downloadTranscript()}
              >
                Download Transcript
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SpeechToText;
