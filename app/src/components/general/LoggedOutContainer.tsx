"use client"
import React, { Suspense } from 'react'
import { headers } from 'next/headers'
import { redirect } from 'next/navigation'
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from '@/services/firebase.config';
import LoadingSkeleton from './LoadingSkeleton';



export default function LoggedOutContainer({children, redirectUrl}: {children: React.ReactNode, redirectUrl: string}) {

  const [user, loading, error] = useAuthState(auth);

  const [isLoggedOut, setIsLoggedOut] = React.useState<boolean>(false)

  React.useEffect(() => {
    if (loading) return;

    if (error) {
      setIsLoggedOut(true)
      return;
    }

    if (user) redirect(redirectUrl)

  }, [user, loading, error]);

  return (
    <Suspense fallback={<div>Loading...</div>}>
      {(loading) && <LoadingSkeleton />}
      {!loading && children}
    </Suspense>
  )
}