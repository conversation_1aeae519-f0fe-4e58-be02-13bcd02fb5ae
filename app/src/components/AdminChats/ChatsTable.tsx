// DocumentTable.js
import React, { useEffect } from "react";
import { addDoc, deleteDoc, doc, collection } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { db } from "@/services/firebase.config";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Sort from "./Sort";

import { AiOutlinePlusCircle } from "react-icons/ai";
import { Button } from "../ui/button";
import Link from "next/link";
import { Skeleton } from "../ui/skeleton";
import useDashboardStore from "@/stores/dashboard.store";
import { Badge } from "../ui/badge";

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import ChatSession from "./ChatSession";

export default function ChatsTable({ documents }: { documents: any[] }) {
  const router = useRouter();

  const [loaded, setLoaded] = React.useState(false);

  const clientName = useDashboardStore((state) => state.selectedClient)?.value;

  useEffect(() => {
    if (documents) {
      setLoaded(true);
    }
  }, [documents]);

  const prettyDate = (date: any) => {
    const theDate = new Date(date.seconds * 1000);

    /* 
      Rules are:
      - If the date is today, show "Today, 12:00 PM"
      - If the date is yesterday, show "Yesterday, 12:00 PM"
      - If the date is within the last 7 days, show "Monday, 12:00 PM"
      - If the date is older than 7 days, show "31/12/2021, 12:00 PM"
    */

    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const lastWeek = new Date();
    lastWeek.setDate(lastWeek.getDate() - 7);

    if (theDate.toDateString() === today.toDateString()) {
      return `Today, ${theDate.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    } else if (theDate.toDateString() === yesterday.toDateString()) {
      return `Yesterday, ${theDate.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    } else if (theDate > lastWeek) {
      return `${theDate.toLocaleDateString([], {
        weekday: "long",
      })}, ${theDate.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    } else {
      return `${theDate.toLocaleDateString([], {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      })}, ${theDate.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    }
  };

  const calculateSessionLength = (dateA: any, dateB: any) => {
    try {
      const aSeconds = dateA.seconds;
      const bSeconds = dateB.seconds;

      const aDate = new Date(aSeconds * 1000);
      const bDate = new Date(bSeconds * 1000);

      const diff = Math.abs(aDate.getTime() - bDate.getTime());

      // Format should be "1h 2m 3s", with only the largest unit shown
      // So if the session is 1h 2m 3s, it should show "1h"

      const hours = Math.floor(diff / 1000 / 60 / 60);
      const minutes = Math.floor(diff / 1000 / 60) - hours * 60;
      const seconds = Math.floor(diff / 1000) - minutes * 60 - hours * 60 * 60;

      let output = "";
      if (hours > 0) {
        output += `${hours}h`;
      }
      if (minutes > 0) {
        output += ` ${minutes}m`;
      }
      if (seconds > 0) {
        output += ` ${seconds}s`;
      }

      output = output.trim();

      return output;
    } catch (error) {
      return "";
    }
  };

  const isActive = (date: any) => {
    const theDate = new Date(date.seconds * 1000);
    const now = new Date();

    const diff = Math.abs(now.getTime() - theDate.getTime());

    // If the session is less than 5 minutes old, it's active
    return diff < 1000 * 60 * 5;
  };

  const [sortOption, setSortOption] = React.useState<string>("lastMessage");

  const handleSortChange = (value: string) => {
    setSortOption(value);
  };

  const sortedDocuments = React.useMemo(() => {
    // Modify this logic to sort based on the sortOption
    return documents?.sort((a, b) => {
      switch (sortOption) {
        case "lastMessage":
          const aDate = new Date(a.lastUpdated);
          const bDate = new Date(b.lastUpdated);
          return bDate.getTime() - aDate.getTime();
        case "relevance":
          const aSessionLength =
            (a.lastUpdated.seconds - a.created.seconds) * 1000; // convert to milliseconds
          const bSessionLength =
            (b.lastUpdated.seconds - b.created.seconds) * 1000; // convert to milliseconds

          // Normalize messageCount and sessionLength between 0 and 1.
          // Note: We're using max values here for normalization purposes, which you might need to adjust or determine dynamically.
          const maxMessageCount = Math.max(
            ...documents.map((doc) => doc.messageCount)
          );
          const maxSessionLength = Math.max(
            ...documents.map(
              (doc) => (doc.lastUpdated.seconds - doc.created.seconds) * 1000
            )
          );

          const aRelevance =
            a.messageCount / maxMessageCount +
            aSessionLength / maxSessionLength;
          const bRelevance =
            b.messageCount / maxMessageCount +
            bSessionLength / maxSessionLength;

          return bRelevance - aRelevance; // Sorting in descending order based on relevance
        default:
          return 0;
      }
    });
  }, [documents, sortOption]);

  const [selectedChatId, setSelectedChatId] = React.useState<string | null>(
    null
  );

  return (
    <>
      {loaded && (
        <div className="m-4">
          <div className="flex items-center gap-4">
            <Sort
              className=""
              label="Sort By"
              defaultValue="lastMessage"
              options={[
                { value: "lastMessage", display: "Last Message" },
                { value: "relevance", display: "Relevance" },
              ]}
              onSortChange={handleSortChange}
            />
          </div>
          <Table className="rounded-lg ">
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Device</TableHead>
                <TableHead>Message Count</TableHead>
                <TableHead>Last message</TableHead>
                <TableHead>Session Length</TableHead>
              </TableRow>
            </TableHeader>

            <TableBody>
              {sortedDocuments &&
                sortedDocuments
                  .sort((a, b) => {
                    // created is a string, so we need to convert it to a date
                    const aDate = new Date(a.created);
                    const bDate = new Date(b.created);
                    return bDate.getTime() - aDate.getTime();
                  })
                  .map((doc) => (
                    <TableRow>
                      <TableCell>
                        <Sheet>
                          <SheetTrigger>
                            <div
                              className="font-bold hover:underline"
                              onClick={() => {
                                setSelectedChatId(doc.id);
                              }}
                            >
                              {doc.initialQuestion || "Unnamed chat session"}
                              {isActive(doc.lastUpdated) && (
                                <Badge className="ml-2 lowercase bg-green-500">
                                  Active
                                </Badge>
                              )}
                            </div>
                          </SheetTrigger>
                          <SheetContent className="w-[80vw] lg:max-w-[100vh]">
                            <SheetHeader>
                              <SheetTitle>Conversation History</SheetTitle>
                              <SheetDescription>
                                {selectedChatId && (
                                  <ChatSession chatSessionId={selectedChatId} />
                                )}
                              </SheetDescription>
                            </SheetHeader>
                          </SheetContent>
                        </Sheet>
                      </TableCell>
                      <TableCell>
                        {doc.browser} / {doc.os}
                      </TableCell>
                      <TableCell>{doc.messageCount}</TableCell>
                      <TableCell>{prettyDate(doc.lastUpdated)}</TableCell>
                      <TableCell>
                        {calculateSessionLength(doc.lastUpdated, doc.created)}
                      </TableCell>
                    </TableRow>
                  ))}
            </TableBody>
          </Table>
        </div>
      )}
    </>
  );
}
