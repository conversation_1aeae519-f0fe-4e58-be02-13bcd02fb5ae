import { on } from "events";

// streamFetcher.ts
export const fetchStream = async (
  url: string,
  options: RequestInit,
  onData: (accumulatedData: string) => void,
  onJson: (json: any) => void,
  onComplete: () => void,
  onFinishMessage: () => void,
  onError: (message: string) => void,
  onFullSentence?: (sentence: string) => Promise<void>
) => {
  let accumulatedData = "";
  let cleanedMessage = "";
  let lastSentIndex = 0; // This will keep track of the last index we sent a sentence from

  const response = await fetch(url, options);
  const reader = response.body!.getReader();

  const processStream = async ({
    done,
    value,
  }: ReadableStreamReadResult<Uint8Array>): Promise<void> => {
    if (done) {
      onComplete();
      return;
    }

    const textValue = new TextDecoder().decode(value);
    accumulatedData += textValue;

    const displayedData = accumulatedData.slice(
      0,
      accumulatedData.lastIndexOf("{")
    );

    cleanedMessage = displayedData.slice(0, displayedData.lastIndexOf("~~~"));
    cleanedMessage = displayedData.slice(0, displayedData.indexOf("{"));
    cleanedMessage = cleanedMessage.replace(/[.!?]/g, "|");

    const sentences = cleanedMessage.split("|");

    while (sentences.length > lastSentIndex) {
      const sentence = sentences[lastSentIndex].trim();
      lastSentIndex++;
      if (sentence && onFullSentence) {
        await onFullSentence(sentence);
      }
    }

    if (accumulatedData.includes("}") && !accumulatedData.includes("***")) {
      // We extract the json from the accumulatedData, starting from the first '{' to the last '}'.
      const json = JSON.parse(
        accumulatedData.slice(
          accumulatedData.indexOf("{"),
          accumulatedData.lastIndexOf("}") + 1
        )
      );

      onJson(json);
    }

    if (accumulatedData.includes("~~~") && accumulatedData.includes("***")) {
      const errorMessage = textValue.slice(
        textValue.indexOf("~~~") + 3,
        textValue.indexOf("***")
      );

      const json = JSON.parse(errorMessage.slice(errorMessage.indexOf("{")));
      const message = errorMessage.slice(0, errorMessage.indexOf("{"));

      onError(message);
      onJson(json);
    }

    if (!accumulatedData.includes("***")) {
      onData(displayedData);
    }

    if (accumulatedData.includes("{") && !accumulatedData.includes("***")) {
      onFinishMessage();
    }

    return reader.read().then(processStream);
  };

  return reader.read().then(processStream);
};
