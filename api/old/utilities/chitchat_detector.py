from llama_index.core.settings import Settings

class ChitChatDetector:
    """Utility class for detecting if a message is chitchat"""

    @staticmethod
    def is_chitchat(message: str) -> bool:
        """Fetches the OpenGraph image URL from the given webpage URL."""
        try:
            chitchatcall = Settings.llm.complete(f"""
            Consider this sentence: "{message}"
            
            Is it small talk/chit-chat? For instance, it is considered small talk if the question is "How are you?", "Hello", "Good morning", "Are you a robot?", etc.
            Any greeting is chitchat, formal or informal.
            Answer "Yes" or "No", nothing else.
            """)
            
            chitchatcall_text = chitchatcall.text
            
            return chitchatcall_text == "Yes"
        
        except Exception as e:
            return False
