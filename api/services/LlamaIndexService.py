import os
from collections import OrderedDict
from typing import List, Optional
from llama_index.core import GPTVectorStoreIndex, StorageContext, load_index_from_storage
from models.BaseDocument import BaseDocument


class LlamaIndexService:
    def __init__(self, max_cache_size: int = 100):
        """
        Initialize the LlamaIndexService with a configurable max cache size.
        Uses an OrderedDict to implement LRU cache behavior for indices.
        """
        self.indices_dir = os.path.join(os.getcwd(), "indices")  # Base directory set to current directory
        os.makedirs(self.indices_dir, exist_ok=True)
        self.index_cache = OrderedDict()
        self.max_cache_size = max_cache_size

    def _get_index_path(self, tenant_id: str, category_id: Optional[str] = "", graphql_id: Optional[str] = "") -> str:
        """
        Returns the file path for the index storage based on tenant, category, and GraphQL identifiers.
        """
        sub_path = f"{tenant_id}"
        if category_id:
            sub_path = os.path.join(sub_path, f"category_{category_id}")
        if graphql_id:
            sub_path = os.path.join(sub_path, f"graphql_{graphql_id}")
        
        return os.path.join(self.indices_dir, sub_path)

    def _cache_index(self, cache_key: str, index: GPTVectorStoreIndex):
        """
        Caches the index in memory, enforcing LRU cache size limits.
        """
        self.index_cache[cache_key] = index
        self.index_cache.move_to_end(cache_key)  # Marks as most recently used

        if len(self.index_cache) > self.max_cache_size:
            self.index_cache.popitem(last=False)  # Remove least recently used

    def _load_index(self, tenant_id: str, category_id: str = "", graphql_id: str = "") -> GPTVectorStoreIndex:
        """
        Loads the index for the given tenant and optional category/graphql IDs from cache or disk.
        """
        # cache_key = f"{tenant_id}_{category_id}_{graphql_id}"
        
        # Make cache key unique for each combination of tenant, category, and graphql ID, and respect the format
        cache_key = f"{tenant_id}_{category_id}_{graphql_id}"

        
        if cache_key in self.index_cache:
            self.index_cache.move_to_end(cache_key)  # Mark as recently used
            return self.index_cache[cache_key]

        index_path = self._get_index_path(tenant_id, category_id, graphql_id)
        if os.path.exists(index_path) and any([f.endswith(".json") for f in os.listdir(index_path)]):            
            storage_context = StorageContext.from_defaults(persist_dir=index_path)
            index = load_index_from_storage(storage_context)
        else:
            index = GPTVectorStoreIndex([])

        self._cache_index(cache_key, index)
        return index

    def reset_index(self, tenant_id: str, category_id: str = "", graphql_id: str = ""):
        """
        Resets the index for the given tenant, category, and graphql ID combination.
        """
        index_path = self._get_index_path(tenant_id, category_id, graphql_id)
        if os.path.exists(index_path):
            import shutil
            shutil.rmtree(index_path)

        cache_key = f"{tenant_id}_{category_id}_{graphql_id}"
        self.index_cache.pop(cache_key, None)  # Remove from cache if present

    def add_document(self, tenant_id: str, document: BaseDocument,
                     category_id: str = "", graphql_id: str = ""):
        """
        Adds documents to the index for the given tenant and caches the updated index.
        """
        index = self._load_index(tenant_id, category_id, graphql_id)  # Load index from memory or disk
        index._show_progress = True

        index.insert(document.to_llama_document())
            
        index.storage_context.persist(persist_dir=self._get_index_path(tenant_id, category_id, graphql_id))

        cache_key = f"{tenant_id}_{category_id}_{graphql_id}"
        self._cache_index(cache_key, index)  # Cache the updated index
        
        global_index = self._load_index(tenant_id)
        global_index._show_progress = True
        global_index.insert(document.to_llama_document())
        
        global_index.storage_context.persist(persist_dir=self._get_index_path(tenant_id))

    def search(self, tenant_id: str, query: str, category_id: str = "", graphql_id: str = ""):
        """
        Searches the index of the given tenant using the provided query.
        Optionally filters results by category_id or graphql_id.
        If no category_id or graphql_id is provided, searches all indices for the tenant.
        Returns the top three results across all indices.
        """
        if category_id or graphql_id:
            # Specific search within a single index
            index = self._load_index(tenant_id, category_id, graphql_id)
            response = index.as_retriever().retrieve(query)
            results = [
                {
                    "title": node.metadata.get("title", ""),
                    "content": node.text,
                    "url": node.metadata.get("url", ""),
                    "category_id": node.metadata.get("category_id", ""),
                    "graphql_id": node.metadata.get("graphql_id", ""),
                }
                for node in response
            ]
            return results  # Return only the top three results
        else:
            # Search across all indices for the tenant
            global_index = self._load_index(tenant_id)
            response = global_index.as_retriever().retrieve(query)
            results = [
                {
                    "title": node.metadata.get("title", ""),
                    "content": node.text,
                    "url": node.metadata.get("url", ""),
                    "category_id": node.metadata.get("category_id", ""),
                    "graphql_id": node.metadata.get("graphql_id", ""),
                }
                for node in response
            ]
            return results