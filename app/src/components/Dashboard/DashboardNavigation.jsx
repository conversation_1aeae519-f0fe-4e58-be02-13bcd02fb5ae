import Link from 'next/link'
import React from 'react'
import { AiOutlineDashboard, AiOutlineUser } from 'react-icons/ai'
import { HiOutlineDocumentDuplicate } from 'react-icons/hi'
import NavigationLink from './NavigationLink'
import { db, auth } from '@/services/firebase.config'
import { useAuthState } from 'react-firebase-hooks/auth'
import { useEffect } from 'react'
import { useState } from 'react'
import { collection, doc, getDoc, getDocs, onSnapshot, query } from 'firebase/firestore'
import { Avatar } from 'react-daisyui'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'

export default function DashboardNavigation() {

  const [user, loading, error] = useAuthState(auth)
  const [userName, setUserName] = useState('')

  const [tenantsList, setTenantsList] = useState([])

  const loadData = async () => {
    const userDocument = doc(db, 'users', user.uid)
    const userValue = await getDoc(userDocument)

    if (userValue) {
      console.log({ value: userValue.data() })
      setUserName(userValue.data().name)
    }

    const q = query(collection(db, "tenants"));
    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const tenants = [];
      querySnapshot.forEach((doc) => {
          tenants.push( {
            id: doc.id,
            ...doc.data()
          });
      });
      setTenantsList(tenants)
    });

    return [
      unsubscribe
    ]

  }

  useEffect(() => {

    let unsubscribes = []
    const processEffect = async () => {
      if (user) {
        unsubscribes = await loadData()
      }
    }

    processEffect()

    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe())
    }

  }, [user])

  const userInitials = userName.split(' ').map(name => name[0]).join('').toUpperCase()

  const toast = useToast()
  const router = useRouter()

  const logout = async () => {
    try {
        await auth.signOut();
        router.push('/login')
    } catch (error) {
        toast({title: 'Error', description: error.message})
    }
}

  return (
    <div className='flex flex-col justify-between gap-4 p-4 bg-slate-900/80'>
      <div className='flex flex-col items-center gap-4 prose-sm prose prose-invert'>
        <NavigationLink icon={<AiOutlineDashboard className='inline w-6 h-6' />} text='Dashboard' href='/dashboard' />
        {/* <NavigationLink icon={<AiOutlineUser className='inline w-6 h-6' />} text='Profile' href='/dashboard/profile' /> */}
        {/* Now documents */}
        {
          tenantsList.map(tenant => (
            <NavigationLink key={
              tenant.name
            } icon={<HiOutlineDocumentDuplicate className='inline w-6 h-6' />} text={tenant.name} href={`/client/${tenant.id}`} />
          ))
        }
        
      </div>
      <div className='border-t-[1px] border-white/50' >
        <div className='flex items-center justify-center gap-4 mt-4 prose text-white'>
          <div className='flex items-center justify-center w-8 h-8 font-bold rounded-full bg-slate-200'>
            {userInitials}
          </div>
          <div className='text-white'>
            {userName}
          </div>
        </div>
        <Button onClick={logout}>Logout</Button>


      </div>
    </div>
  )
}
