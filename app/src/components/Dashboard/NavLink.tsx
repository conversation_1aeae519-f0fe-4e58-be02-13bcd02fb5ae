"use client";
import Link from 'next/link';
import React, { useEffect, useState } from 'react'
import { cn, getCurrentPath } from '@/lib/utils';
import { usePathname, useRouter } from 'next/navigation';

export default function NavLink({href, children}: {href: string, children: React.ReactNode}) {

  const pathName = usePathname();

  const isCurrent = pathName.includes(href) || pathName.includes(`${href}/`);

  return (
    <Link href={href}
      className={`flex items-center px-4 ${isCurrent ? 'text-primary font-bold' : 'font-medium text-muted-foreground'}`}
    >
      {children}
    </Link>
  )
}
