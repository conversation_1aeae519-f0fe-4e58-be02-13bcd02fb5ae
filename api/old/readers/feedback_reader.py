from typing import List, Dict, Any
from firebase_admin import firestore
from llama_index.core.readers.base import BaseReader
from llama_index.core.schema import Document
import hashlib

class Report:
    def __init__(self, category: str, chatSessionId: str, correctReply: str,
                 createdAt: str, message: str, messageId: str,
                 positiveFeedback: bool, previousMessage: str, status: str):
        self.category = category
        self.chatSessionId = chatSessionId
        self.correctReply = correctReply
        self.createdAt = createdAt
        self.message = message
        self.messageId = messageId
        self.positiveFeedback = positiveFeedback
        self.previousMessage = previousMessage
        self.status = status


class FeedbackReader(BaseReader):

    def __init__(self, db: Any, tenant_id: str):
        self.db = db
        self.tenant_id = tenant_id

    def load_data(self, *args: Any, **load_kwargs: Any) -> List[Document]:
        reports = self.get_trainable_reports()
        grouped_data = {}
        
        # Group feedback by category
        for report in reports:
            try:
                category = report['category'] or "Uncategorized"
                correct_reply = report['correctReply'] if report['correctReply'] != "" else report['message']

                text = f"""
                Question: {report['previousMessage']}
                Answer: {correct_reply}
                """

                if category not in grouped_data:
                    grouped_data[category] = []
                grouped_data[category].append(text)

            except:
                pass

        # Create one document per category
        report_data = []
        for category, texts in grouped_data.items():
            merged_text = "\n\n".join(texts)
            category_id_hash = hashlib.md5(category.encode()).hexdigest()  # or some other unique hash function
            report_data.append(
                Document(text=merged_text, doc_id=category_id_hash, doc_hash=category_id_hash)
            )

        return report_data


    def get_trainable_reports(self) -> List[Report]:
        reports_ref = (self.db.collection("tenants")
                       .document(self.tenant_id)
                       .collection("reports")
                       .where("status", "==", "added-to-ai"))

        report_docs = reports_ref.get()
        reports = []

        for doc in report_docs:
            try:
                reports.append(doc.to_dict())
            except:
                pass

        return reports

    def get_category_label(self, category_id: str) -> str:
        category_ref = (self.db.collection("tenants")
                        .document(self.tenant_id)
                        .collection("reportCategories")
                        .document(category_id))

        category_doc = category_ref.get()
        return category_doc.to_dict().get('label', 'Unknown')

    def get_report_categories(self, tenant_id: str):
        categories_ref = (self.db.collection("tenants")
                          .document(tenant_id)
                          .collection("reportCategories"))

        categories_docs = categories_ref.get()
        categories = []

        for doc in categories_docs:
            categories.append(doc.to_dict())

        return categories
