
import json
import os
from typing import Any, List
import asyncio
import time

from collections import namedtuple

import firebase_admin
from firebase_admin import firestore_async
from firebase_admin import firestore

from firebase_admin import firestore
from services.categories_service import get_flattened_categories
from models.report_request import ReportRequest, BasicResponse
from models.chat_response import ChatResponse

from models.tenant import Tenant
from repositories.tenant_repository import TenantRepository
from services.link_validator import LinkValidator
from llama_index.core.llms import ChatMessage

from services.model_service import ModelService
from llama_index.core.chat_engine.types import BaseChatEngine
from google.cloud.firestore import Client
from google.cloud.firestore_v1.base_query import FieldFilter, Or

from llama_index.core.chat_engine.types import StreamingAgentChatResponse

import llama_index

import json

def separate_text_and_json(s):
    # Find the position of the last closing brace
    end_pos = s.rfind('}')
    
    # From that position, find the opening brace
    start_pos = s.rfind('{', 0, end_pos + 1)
    
    # Split the string into plain text message and JSON
    plain_text = s[:start_pos].strip()
    serialized_json = s[start_pos:end_pos + 1]
    
    # Validate that the serialized JSON is valid
    try:
        json_obj = json.loads(serialized_json)
    except json.JSONDecodeError:
        return serialized_json, "{ \"quick_replies\": [], \"mentions_product\": false }"
    
    return plain_text, serialized_json

class ChatManager:
  
  _instances = {}  # Class-level dictionary to store instances by tenant_id

  def __init__(self, database: Client, tenant: Tenant, tenant_repository: TenantRepository, model_service: ModelService, tenant_id: str = None):
    
    self.database: Client = database
    self.tenant: Tenant = tenant
    self.tenant_id:str = tenant_id
    self.tenant_repository: TenantRepository = tenant_repository
    self.model_service: ModelService = model_service
    self.link_validator = LinkValidator(self.database, self.model_service)
    
    
  @classmethod
  def create(cls, tenant_id: str) -> 'ChatManager':
    
    if tenant_id in cls._instances:
      # Return existing instance if it exists
      return cls._instances[tenant_id]
    
    database = firestore.client()
    tenant_repository = TenantRepository(database)
    tenant = tenant_repository.get_tenant(tenant_id)
    model_service = ModelService.create(database, tenant_id)
    
    instance = ChatManager(database, tenant, tenant_repository, model_service, tenant_id)
    
    # Store instance in dictionary
    cls._instances[tenant_id] = instance
    
    return instance
  
  

  def createChatSession(self, tenant_id: str, initial_question: str, user_ip: str, browser: str, os: str, device_type:str):

    firestore_now = firestore.SERVER_TIMESTAMP

    # Corrected 'data' dictionary
    data = {
        'created': firestore_now,
        'lastUpdated': firestore_now,
        'messageCount': 0,
        'userIp': user_ip,
        'browser': browser,
        'os': os,
        'deviceType': device_type,
        'initialQuestion': initial_question
    }

    update_time, chat_session_ref = (
      self.database.collection('tenants')
      .document(tenant_id)
      .collection('chatSessions')
      .add(data))

    return chat_session_ref.id
  
  def moderateMessage(self, text: str):
    
    system_messages = [
      ChatMessage(content="""""", role="system")
      ]
    
    prompt = f"""
    
    You are the moderation system. 
    You are specialised in detecting whether or not a particular question is related to the
    core data or not. 
    
    You can chitchat, but anything that is wildly outside the remit of Sleepeezee, like questions about biology, space, AI taking over the world, opinion pieces, etc. should be marked as not related to the core data.

    User question: {text}
    
    Is the above question related to the core data?
     
    Reply with a JSON containing a number between 1 and 100. The more relevant, the closer to 100. The less relevant, the closer to 1.
    Chitchat (hello, yo, how are you, what's your name, etc.) are always 100.
    The JSON also contains a response to the user if the score is less than 50. 
    
    The response should always be well written, as a helpful assistant would. Don't write the response from the perspective of a moderation system, write it like a helpful assistant would.
    
    Format:   {{
        "response": "This is the response",
        "score": 100,
    }}
    """
    
    engine = self.model_service.getGenericChatEngine(system_messages)
    
    gpt_response = engine.chat(prompt)
    
    
    json_data = json.loads(gpt_response.response)
    
    # If there are any sentences in the response, that contains "core data", we need to remove them.
    sentences = json_data["response"].split('. ')
    filtered_sentences = [sentence for sentence in sentences if "core data" not in sentence.lower()]
    json_data["response"] = '. '.join(filtered_sentences)
    
    
    # if the response is more than 50, it is related to the core data
    # string to int
    
    return json_data
  
  

  async def agent_chat(self, text: str, chat_history: List[ChatMessage], chat_session_id: str) -> ChatResponse:
    
      if not chat_session_id:
        raise ValueError('chat_session_id is required')
    
    
    
      chat_session_ref = (
        self.database
            .collection('tenants')
            .document(self.tenant_id)
            .collection('chatSessions')
            .document(chat_session_id)
      )
      
      chat_session_ref.collection("messages").add(
          {
              "role": "user",
              "message": text,
              "createdAt": firestore.SERVER_TIMESTAMP,
          }
      )
      
      msg_data = self.tenant.systemMessages
      
      system_messages = []
      for message in msg_data:
        system_messages.append(ChatMessage(content=message, role="system"))

      authorized_links= self.link_validator.get_all_authorised_links(self.tenant_id)

      """
      Authorised links. You can ONLY use those links
      --------------------
      {link_string}
      --------------------
      """

      query = f"""
      ## 
      
      After the response, add a JSON object using the following format:
        {{
            "quick_replies": ["Quick reply 1", "Quick reply 2", "Quick reply 3"],
            "mentions_product": true/false,
        }}. 
        
      Only answer questions related to Sleepeezee.
      The quick replies should be an array of 3 strings (questions) and NEVER be empty.
      The quick replies should ALWAYS be relevant to the response.
      "mentions_product" is a boolean value that indicates whether or not the response mentioned a product.
      
      If the response mentions a product, always include a link to it.
      Make ABSOLUTELY certain you include the JSON object, otherwise the AI will not learn from your response.
      
      
      
      This is the user's question:
      {text}.
      Do not include links.
      """

      response_text = ""
      
      response = await self.model_service.chat(query, chat_history)
      response_text += response.response
      
      yield response_text
    
    
  
  async def chat(self,
    text: str,
    chat_history: List[ChatMessage],
    chat_session_id: str,
  ) :
    
    if not chat_session_id:
      raise ValueError('chat_session_id is required')
    
    chat_session_ref = (
      self.database
          .collection('tenants')
          .document(self.tenant_id)
          .collection('chatSessions')
          .document(chat_session_id)
    )
    
    chat_session_ref.collection("messages").add(
        {
            "role": "user",
            "message": text,
            "createdAt": firestore.SERVER_TIMESTAMP,
        }
    )
    
    msg_data = self.tenant.systemMessages
    
    system_messages = []
    for message in msg_data:
      system_messages.append(ChatMessage(content=message, role="system"))
    
    engine = self.model_service.getEngine(system_messages)
    
    authorized_links= self.link_validator.get_all_authorised_links(self.tenant_id)
    link_string = ', '.join(authorized_links)

    """
    Authorised links. You can ONLY use those links
    --------------------
    {link_string}
    --------------------
    """

    query = f"""
    ## 
    
    After the response, add a JSON object using the following format:
      {{
          "quick_replies": ["Quick reply 1", "Quick reply 2", "Quick reply 3"],
          "mentions_product": true/false,
      }}. 
      
    Only answer questions related to Sleepeezee.
    The quick replies should be an array of 3 strings (questions) and NEVER be empty.
    The quick replies should ALWAYS be relevant to the response.
    "mentions_product" is a boolean value that indicates whether or not the response mentioned a product.
    
    If the response mentions a product, always include a link to it.
    Make ABSOLUTELY certain you include the JSON object, otherwise the AI will not learn from your response.
    
    
    
    This is the user's question:
    {text}.
    Do not include links.
    """

    response_text = ""
    
    response = engine.chat(query, chat_history)
    response_text += response.response
    
    yield response_text
    
    # stream = await engine.chat(query, chat_history)
    # response_obj = stream.async_response_gen()

    # async for message in response_obj:
    #     response_text += message
    #     yield message
    
    
        
    # Did the message contain a JSON object?
    contained_json = "{" in response_text and "}" in response_text
    
    if not contained_json:
      empty_json_string = " { \"quick_replies\": [], \"mentions_product\": false }"
      response_text += empty_json_string
      yield (empty_json_string)
      

    plain_text, serialized_json = separate_text_and_json(response_text)

    chat_session_ref.collection("messages").add(
        {
            "role": "assistant",
            "message": plain_text,
            "quick_replies": json.dumps(serialized_json),
            "createdAt": firestore.SERVER_TIMESTAMP,
        }
    )
    
    chat_session_ref.update(
        {
            "messageCount": firestore.Increment(2),
            "lastUpdated": firestore.SERVER_TIMESTAMP,
        }
    )
    
    response_text_mentions_product = json.loads(serialized_json).get("mentions_product", False)
    available_products_string = ""
    
    if response_text_mentions_product:
      available_products_list = self.link_validator.get_available_products(self.tenant_id)
      products_string = '- '.join(available_products_list) + '\n'
      
      available_products_string = f"""    
        Available products. You can ONLY mention those products:
        ----------
        {products_string}
        ----------
      """
    
    product_query = f"""
    Authorised links. You can ONLY use those links:
    ----------
    {link_string}
    ----------
    
    Considering the following reply:
    {plain_text}
    
    Find the best matching links and page names. 
    
    Only give the links, nothing else, in A tags format like <a href="https://www.example.com">Example</a>
    - You can put up to three links
    - They should be in a ul>li>a structure
    """
    
    page_search = engine.chat(product_query, chat_history)
    
    
    chat_session_ref.collection("messages").add(
        {
            "role": "links",
            "message": page_search.response,            
            "createdAt": firestore.SERVER_TIMESTAMP,
        }
    )
    
    chat_session_ref.update(
        {
            "messageCount": firestore.Increment(1),
            "lastUpdated": firestore.SERVER_TIMESTAMP,
        }
    )
    
    
    
    
    # # Does it contain a http string?
    # response_text_contains_links = "http" in response_text
    # response_text_mentions_product = json.loads(serialized_json).get("mentions_product", False)
    
    
    # if response_text_contains_links or response_text_mentions_product:
    #   validate_links, invalid_links = self.link_validator.validate_links(response_text, self.tenant_id)
    #   validate_products = self.link_validator.validate_product(response_text, self.tenant_id)
      
    #   if(not validate_links):
    #     yield ("~~~I apologise, but it seems like I made a mistake in my previous reply." + 
    #            "I will try to do better next time! If you ask me again in a minute, I should be more helpful." +
    #            " { \"quick_replies\": [], \"mentions_product\": false }***"
    #            )
        
    #     self.link_validator.validate_and_rectify(text, plain_text, self.tenant_id, 2, response_text_mentions_product)
    
    
    
    

  def chatSystem(self, query: str, system_messages: List[str]) -> str:
    system_messages_list = []
    
    if system_messages is not None:
        for message in system_messages:
            system_messages_list.append(ChatMessage(content=message, role="system"))

    engine = self.model_service.getSystemChatEngine(system_messages_list)
    
    gpt_response = engine.chat(query)

    return gpt_response.response
      
  def retrainAI(self):
    try:
      self.model_service.trainEngine()
      return True
    except Exception as e:
      return False
    
  def categoriseAll(self, tenant_id: str):
    """
    This function will categorise all the documents of the "websitePages" collection in the tenant's Firestore database.
    """
    
    categories = get_flattened_categories(self.database, tenant_id)
    
    website_pages_ref = (
      self.database
        .collection('tenants')
        .document(tenant_id)
        .collection('websitePages')
    )
    
    
    openaikey = (
      self.database.collection('tenants')
      .document(tenant_id)
      .get()).to_dict()["openai"]["openAIKey"]
    
    website_pages = website_pages_ref.get()
    
    from openai import OpenAI
    client = OpenAI(api_key=openaikey)
    
    for page in website_pages:
      page_data = page.to_dict()
      
      # If the "category" is set to anything, don't do anything
      if page_data.get("category", None) is not None:
        continue
      
      # If the "content" is not set, don't do anything
      if page_data.get("content", None) is None or page_data.get("content", None) == "":
        continue
      
      # In any other case, let's prepare a prompt to automatically categorise the page
      
      short_content = page_data["content"][:500]
      
      prompt = f"""
      You are the categorisation system.
      
      Available categories:
      {categories}
      ========================
      
      Given the following content:
      Title: {page_data.get("title", "No title")}
      Url: {page_data.get("url", "No URL")}
      Content: ```{short_content}...```
      
      Please categorise the content, by returning the category ID and category name it should belong to.
      
      If the content is not related to any category, 
      or if it's a purely "website page" like a contact page, 
      thank you page etc., please return "Uncategorised".
      
      Only reply with the category ID and category name in JSON format.
      {{
        "pageUrl": "https://www.example.com", 
        "categoryId": "id", 
        "categoryName": "label"
      }}
      
      """
      
      completion = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[
          {"role": "system", "content": "You are the categorisation system. "},
          {"role": "user", "content": prompt},
        ]
      )

      response = completion.choices[0].message.content
      
      # strip triple ticks and "```json" from the response, if it's there
      
      response = response.replace("```json", "")
      response = response.replace("```", "")
      
      try:
        response_json = json.loads(response)
          
        if response_json.get("categoryId", None) is None:
          raise ValueError('categoryId is required')
        
        if response_json.get("categoryName", None) is None:
          raise ValueError('categoryName is required')
        
        
        doc_ref = (
          self.database
            .collection('tenants')
            .document(tenant_id)
            .collection('websitePages')
            .document(page.id)
        )
        
        doc_ref.update({
          "categoryId": response_json["categoryId"],
          "categoryName": response_json["categoryName"],
        })
      
      except Exception as e:
        continue
      
    return True
      
      
      
      
        
      


  def report(self, reportRequest: ReportRequest) -> BasicResponse: 
    
    try:
      message_ref = (self.database
        .collection('tenants')
        .document(reportRequest.tenantId)
        .collection('chatSessions')
        .document(reportRequest.chatSessionId)
        .collection('messages')
        .document(reportRequest.id)
      )
      
      message_value = message_ref.get().to_dict()
      
      if message_value is None:
        raise ValueError('message_value is required')
      
      if message_value.get("message", None) != reportRequest.message:
        raise ValueError('message_value.message is required')
      
      similar_reports = (self.database
        .collection('tenants')  
        .document(reportRequest.tenantId)
        .collection('reports')
        .where('message', '==', reportRequest.message)  
      )
      
      similar_reports_value = similar_reports.get()
      
      # If there is a similar report, don't do anything and return
      if len(similar_reports_value) > 0:
        return BasicResponse("error", "Report already exists")
      
      get_flattened_categories(self.database, reportRequest.tenantId)
      categories_ref = (self.database
        .collection('tenants')
        .document(reportRequest.tenantId)
        .collection('reportCategories')
      )
      
      categories_value = categories_ref.get()
      
      
      
      
      # If there are no categories, don't do anything and return
      if len(categories_value) == 0:
        return BasicResponse("error", "No categories found")
      
      # This list should be a JSON string
      # let's first do a .get() on each category, and then add it to the list
      categories = []
      for category in categories_value:
        categories.append({
          "id": category.id,
          "description": category.to_dict().get("description", None),
        })
      
      system_messages = ([
      "If a question is about getting a product for a particular health position, or a type of sleeper, do mention that you cannot give medical advice, but still give the best product available given the question",
      "Subjective questions, like 'What is the best', comparison questions, etc. may have a lower confidence index, and the AI shouldn't have hard opinions. The AI assistant is there to help the user make their own choice, not to manipulate them into a sale, when the AI has not been able to try the products by itself.",
      "Do not say that 'the information is not provided in the data', give the best available reply instead.",
      "As much as possible, do not reuse the old assistant answer, and make up a new message that's relevant to the question.",
      ])
      
      chat_system_messages = []
      for message in system_messages:
        chat_system_messages.append(ChatMessage(content=message, role="system"))
        
      json_format = "Your answer should always be in perfectly JSON format like {\"category\": \"<category id>\", \"correctReplyAccordingToData\": \"<put correct reply here>\", \"dataMissingInContext\": true/false, confidenceIndex: <between 0 and 1>}. Don't use double quotes inside the correctReplyAccordingToData, use single quotes instead. The confidence index is a numeric value between 0 and 1 and should be an indication of how confident the AI is in putting the correctReplyAccordingToData directly in production.",
      
        
      rectified_message = self.link_validator.rectify_message(reportRequest.previousMessage, 
                                          reportRequest.message, 
                                          reportRequest.tenantId,
                                          chat_system_messages,
                                          json_format)
      
      response_object = json.loads(rectified_message)
      
      payload = {
        "chatSessionId": reportRequest.chatSessionId,
        "messageId": reportRequest.id,
        "message": reportRequest.message,
        "status": "backlog",
        "previousMessage": reportRequest.previousMessage,
        "createdAt": firestore.SERVER_TIMESTAMP,
        "positiveFeedback": reportRequest.positiveFeedback,
      }
      
      if response_object.get("category", None) is not None:
        payload["category"] = response_object["category"]
        
      # Must be above 0.8
      if ( response_object["confidenceIndex"] > 0.8):
        payload["correctReply"] = response_object.get("correctReplyAccordingToData", None)
        payload["status"] = "added-to-ai"
        
        
      self.database.collection('tenants').document(reportRequest.tenantId).collection('reports').add(payload)
      
      message_ref.update({
        "reported": True,
      })
      
      if ( response_object["confidenceIndex"] > 0.8):
        ModelService.trainGPT4(tenant_id=reportRequest.tenantId, database=firestore.client())
      
      return BasicResponse("success", response_object)
    
    except Exception as e:
      
      return BasicResponse("error", str(e))
      
  