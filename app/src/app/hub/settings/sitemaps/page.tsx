"use client";
import FirebaseDataTable, {
  ConfigItem,
} from "@/components/advanced-ui/firebase-datatable/firebase-datatable";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import DialogButton from "@/components/ui/dialog-button";
import { getAllSiteContent } from "@/services/data-import/import-sitemap";
import useDashboardStore from "@/stores/dashboard.store";
import { useStore } from "@/stores/use-store";
import React from "react";

type Props = {};

export default function SitemapPage({ }: Props) {
  const clientName = useStore(
    useDashboardStore,
    (state) => state.selectedClientId
  );

  const importSitemaps = async () => {
    if (clientName) {
      await fetch(`/api/fetch-sitemap-items?tenantId=${clientName}&rnd=${Math.random()}`);
    }
  }


  return (
    <Card>
      <CardHeader>
        <CardTitle>Content Validator</CardTitle>
        <CardDescription>
          This is a list of XML Sitemaps that will be used to double-check any
          links generated by the AI so that it never gives a bad link.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        {clientName && (
          <FirebaseDataTable
            firebasePath={["tenants", clientName, "sitemaps"]}
            term="XML Sitemap URL"
            formConfig={{
              name: {
                key: "name",
                label: "Name",
                type: "text",
                defaultValue: "",
              } as ConfigItem,
              description: {
                key: "sitemap",
                label: "Sitemap Url",
                type: "text",
                defaultValue: "",
              } as ConfigItem,
              domSelector: {
                key: "domSelector",
                label: "DOM Selector",
                type: "text",
                defaultValue: "",
              } as ConfigItem,
            }}
          />
        )}
        <div>

          <DialogButton
            variant={"default"}
            callback={importSitemaps}
            okayText="Import all sitemap content"
            headerText="Are you sure?"
            closedToastTitle="Import in progress..."
            closedToastText="This may take a moment."
            button={
              <div className="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-6 h-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
                  />
                </svg>
                Import all sitemap content
              </div>
            }
          >
            <p>
              You need to ensure you have defined all Data Categories,
              and that all your sitemaps are valid before proceeding with this action.
            </p>
            <p>
              This may take several minutes to complete.
            </p>
          </DialogButton>
        </div>
      </CardContent>
    </Card>
  );
}
