import * as admin from "firebase-admin";
import * as serviceAccount from "@/config/firebase.key.json";
import {
  getFirestore,
  Timestamp,
  FieldValue,
  Filter,
} from "firebase-admin/firestore";

const firebaseAdmin =
  admin.apps.length == 0
    ? admin.initializeApp({
        credential: admin.credential.cert(
          serviceAccount as admin.ServiceAccount
        ),
      })
    : admin.app();

const database = getFirestore();

export { firebaseAdmin, database };
