"use client";
import React, { useMemo } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Message } from "@/models/message";
import { Grid } from "react-loader-spinner";
import { cn } from "@/lib/utils";
import "./bubble.scss";
import { Icons } from "../Icons";
import { Report, ReportRequest } from "@/models/report";
import axios from "axios";
import { getFirebaseApiEndpoints } from "@/services/api-connector/api-connector";
import SpeakButton from "./speak-button";

export default function Bubble({
  message,
  previousMessage,
  chatSessionId,
  tenantId,
  botName,
  admin = false,
  loading = false,
  userName = "You",
  short = false,
  reporteable = true,
}: {
  message: Message;
  previousMessage: Message | null;
  admin?: boolean;
  loading?: boolean;
  chatSessionId: string;
  tenantId: string;
  userName?: string;
  botName?: string;
  short?: boolean;
  reporteable?: boolean;
}) {
  // API endpoints
  const endpoints = useMemo(() => getFirebaseApiEndpoints(), []);

  const contentFormatter = (message: string, short: boolean) => {
    /* 
    If there are links in this format: [here](https://sleepeezee.com/product/backcare-supreme-2000/)
    they should become <a href="https://sleepeezee.com/product/backcare-supreme-2000/">here</a>
    */

    const regex = /\[(.*?)\]\((.*?)\)/g;
    const subst = `<a href="$2" target="_blank">$1</a>`;
    let result = message.replace(regex, subst);

    // If the message contains "Answer: ", remove it, and everything before it
    if (result.includes("Answer: ")) {
      result = result.split("Answer: ")[1];
    }


    if (result.startsWith("Sleepeezee chatbot: ")) {
      result = result.replace("Sleepeezee chatbot: ", "");
    }
    if (result.startsWith("AI: ")) {
      result = result.replace("AI: ", "");
    }

    if (result.includes("I cannot answer the question with the provided tools.")) {
      result = "I'm sorry, can you repeat?"
    }

    if (short) {
      result = result.split(" ").slice(0, 12).join(" ");
      if (result.length < message.length) {
        result = result + "...";
      }
    }

    // Test may contain ```json\n{...}``` so we should remove the ```json 
    result = result.replace("```json", "");

    return result;
  };

  const getElapsedTime = (message: Message) => {
    const messageDate =
      message.createdAt.seconds === undefined
        ? message.createdAt
        : new Date(message.createdAt.seconds * 1000);

    // The rules are:
    // If the message was sent today, show the time
    // If the message was sent yesterday, show "Yesterday"
    // If the message was sent before yesterday, show the date
    // If the message was sent less than 1 minute ago, show "Just now"

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const isToday =
      messageDate.getDate() === today.getDate() &&
      messageDate.getMonth() === today.getMonth() &&
      messageDate.getFullYear() === today.getFullYear();

    const isYesterday =
      messageDate.getDate() === yesterday.getDate() &&
      messageDate.getMonth() === yesterday.getMonth() &&
      messageDate.getFullYear() === yesterday.getFullYear();

    if (isToday) {
      if (today.getTime() - messageDate.getTime() < 60000) {
        return "Just now";
      }

      return messageDate.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    }

    if (isYesterday) {
      return "Yesterday";
    }

    return messageDate.toLocaleDateString([], {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  };

  const pageFormatter = (page: string | undefined) => {
    if (page === undefined) {
      return null;
    }

    const decoded = decodeURIComponent(page);
    try {
      const url = new URL(decoded);

      if (url.pathname === "/") {
        return "Homepage";
      }

      return url.pathname;
    } catch (error) {
      return null;
    }
  };

  const [reporting, setReporting] = React.useState(false);
  const [reported, setReported] = React.useState(false);
  const [confirmationMessage, setConfirmationMessage] = React.useState(
    "Thank you for your feedback!"
  );

  const report = async (
    messageToReport: Message,
    previousMessage: Message,
    positiveFeedback: boolean
  ) => {
    const { id, message } = messageToReport;
    const { id: previousMessageId, message: previousMessageMessage } =
      previousMessage;

    const data = {} as ReportRequest;

    try {
      setReporting(true);

      const response = await endpoints.report({
        chatSessionId,
        tenantId,
        id,
        message,
        previousMessage: previousMessageMessage,
        positiveFeedback,
      });

      setConfirmationMessage("Thank you for your feedback!");

      if (response.status === "success") {
        setReported(true);

        setTimeout(() => {
          setReporting(false);
          setConfirmationMessage("");
        }, 3000);
      }
    } catch (error) {
      setReporting(false);
      setReported(false);
      setConfirmationMessage("An error occurred. Please try again later.");
    }
  };

  return (
    <div
      key={message.id}
      className="flex flex-1 gap-3 my-4 text-sm text-gray-600"
    >
      {message.role === "user" && (
        <Avatar className={short ? `w-5 h-5` : `w-8 h-8`}>
          <div className="p-1 bg-gray-100 border rounded-full">
            <svg
              stroke="none"
              fill="black"
              strokeWidth="0"
              viewBox="0 0 16 16"
              height={short ? `10` : `20`}
              width={short ? `10` : `20`}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4Zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10Z"></path>
            </svg>
          </div>
        </Avatar>
      )}
      {message.role === "assistant" && (
        <Avatar className={short ? `w-5 h-5` : `w-8 h-8`}>
          {/* <AvatarFallback>M</AvatarFallback> */}
          <div
            className={cn(
              "rounded-full bg-gray-100 border p-1",
              loading && "animate-pulse"
            )}
          >
            <svg
              stroke="none"
              fill="black"
              strokeWidth="1.5"
              viewBox="0 0 24 24"
              aria-hidden="true"
              height={short ? `10` : `20`}
              width={short ? `10` : `20`}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z"
              ></path>
            </svg>
          </div>
        </Avatar>
      )}

      <p className="w-full leading-relaxed">
        <span
          className={`flex items-center gap-2 font-bold text-muted-foreground ${short ? `text-xs` : ``
            }`}
        >
          { }
          {message.role === "user" ? userName : botName}{" "}
          {!short && (
            <>
              <span className="text-xs font-normal text-gray-400">
                {getElapsedTime(message)}
              </span>
              {message.role === "user" && admin && (
                <span className="pl-2 text-xs font-normal text-gray-400">
                  (page:&nbsp;
                  {pageFormatter(message.metadata?.page)})
                </span>
              )}
              {!loading && message.role == "assistant" && (
                <SpeakButton message={message.message} />
              )}
              {!message.reported &&
                message.role === "assistant" &&
                message.id !== "1" && (
                  <div className="flex justify-end flex-1 gap-2 text-muted-foreground">
                    {reporting && !reported && (
                      <span className="text-xs font-normal">
                        Thank you for your feedback!
                      </span>
                    )}
                    {/* {reported && !reporting && (
                      <span className="text-xs font-normal">
                        {confirmationMessage}
                      </span>
                    )} */}
                    {reporteable &&
                      !reported &&
                      !reporting &&
                      previousMessage && (
                        <>

                          <button
                            onClick={async () =>
                              await report(message, previousMessage, false)
                            }
                          >
                            <Icons.thumbsDown className="w-5" />
                          </button>
                        </>
                      )}
                  </div>
                )}
            </>
          )}
        </span>
        {!loading && (
          <>
            <div
              className={`chat-message text-foreground prose prose-sm ${short ? `text-xs` : ``
                }`}
              dangerouslySetInnerHTML={{
                __html: contentFormatter(message.message, short),
              }}
            ></div>
          </>
        )}
        {/* {JSON.stringify(message)} */}
        {loading && (
          <Grid
            height={12}
            width={12}
            radius={5}
            ariaLabel="grid-loading"
            ms-visible={true}
          />
        )}
      </p>
    </div>
  );
}
