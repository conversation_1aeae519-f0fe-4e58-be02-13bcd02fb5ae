from llama_index.core.storage import StorageContext
import os
from typing import Any, List, Dict, Callable, Optional
from llama_index.core.llms import LLM, ChatMessage
from typing import Optional
from llama_index.core.chat_engine.types import StreamingAgentChatResponse,  AgentChatResponse
from llama_index.core.agent import ReActAgent
import json
from utilities.links_fetcher import LinksFetcher
from utilities.chitchat_detector import ChitChatDetector

from llama_index.core.llms import LLM
from llama_index.llms.openai import OpenAI
from llama_index.core.storage import StorageContext
import openai
import tiktoken
from llama_index.core.query_engine import SubQuestionQueryEngine

from google.cloud.firestore import Client
from llama_index.core.settings import Settings
import logging
from repositories.category_repository import CategoryRepository
from repositories.tenant_repository import TenantRepository
from llama_index.core.indices import load_index_from_storage
from llama_index.core.tools import QueryEngineTool, ToolMetadata

logging.basicConfig(level=logging.INFO)
from llama_index.core.callbacks.global_handlers import set_global_handler
from llama_index.core.llms.llm import LLM
from models.report_request import ReportRequest, BasicResponse

from services.chat_message_manager import ChatMessageManager


from datetime import datetime, timedelta

set_global_handler("simple")

from llama_index.core.indices.base import BaseIndex
class AgentService():
  
  # This holds the one agent for each tenant, identified by their tenant id
  tenant_agent_set = {}
  llm: LLM = None
  # It's a dict of BaseIndex
  tenant_indices_set: Dict[str, Dict[str, BaseIndex]] = {}
  session_agents_set = {}
  tenant_repository = None
  out_text = {}
  
  def __init__(self, database: Client):
    self.database = database
    self.category_repository = CategoryRepository(self.database)
    self.tenant_repository = TenantRepository(self.database)
    self.init_llm()
    
  def get_folder_path(self, tenant_id: str):
    cwd = os.path.dirname(os.path.abspath(__file__))
    folder_path = os.path.join(cwd, f"indexes/{tenant_id}/")
    
    return folder_path
  
  @staticmethod
  def init_llm(): 
    
    if(AgentService.llm is not None):
      return
      
    # from llama_index.llms.groq import Groq
    # groq_api_key = "********************************************************"
    # llm = Groq(model="mixtral-8x7b-32768", api_key=groq_api_key)
    
    import os

    from llama_index.llms.anthropic import Anthropic
    os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
    quick_tokenizer = Anthropic().tokenizer
    # Settings.tokenizer = tokenizer
    # llm = Anthropic(model="claude-3-opus-20240229")
    # quick_llm = Anthropic(model="claude-3-haiku-20240307")
    # llm = Anthropic(model="claude-3-sonnet-20240229")
    
    
    os.environ["OPENAI_API_KEY"] = "********************************************************"

    llm = OpenAI("gpt-4-turbo-preview", 0.1, api_key="********************************************************")
    quick_llm = OpenAI("gpt-4o", 0.1, api_key="********************************************************")
    openai.api_key = "********************************************************"
    Settings.tokenizer = tiktoken.encoding_for_model("gpt-4o").encode
    
    Settings.llm = quick_llm
    AgentService.llm = quick_llm
    AgentService.tokenizer = Settings.tokenizer
    AgentService.quick_llm = quick_llm
    AgentService.quick_tokenizer = quick_tokenizer
    
  def clear_expired_agents(self):
    current_time = datetime.now()
    expired_keys = [key for key, agent in AgentService.session_agents_set.items() if agent["expiry"] < current_time]
    for key in expired_keys:
        del AgentService.session_agents_set[key]

  def get_agent(self, 
                tenant_id: str, 
                session_id: str,
                writer: Optional[Callable[[str], None]] = None,
                reset: Optional[Callable] = None
                ):
    
    self.clear_expired_agents()
    self.init_indices(tenant_id)
    
    agent_key = f"""{tenant_id}-{session_id}"""
    
    if agent_key in AgentService.session_agents_set:
      agent = AgentService.session_agents_set[agent_key]
      
      agent_expiry = agent["expiry"]
      
      if agent_expiry > datetime.now():
        agent["expiry"] = datetime.now() + timedelta(minutes=60)
        return [
          agent["agent"],
          agent["chat"]
        ]
      
      
    categories = self.category_repository.get_categories(tenant_id)
    
    individual_query_engine_tools = []
    
    for category in categories.values():
      
      cur_index = AgentService.tenant_indices_set[tenant_id][category["id"]]
      
      label_slug = category["label"].replace(" ", "_").lower()
      
      individual_query_engine_tools.append(
        QueryEngineTool(
          query_engine=cur_index.as_query_engine(),
          metadata=ToolMetadata(
            name=f"vector_index_{label_slug}",
            description=f"useful for when you want to answer queries about {category['description']}"
          )
        )
      )
    
    all_index = AgentService.tenant_indices_set[tenant_id]["all"]
    
    general_query_engine_tool = QueryEngineTool(
      query_engine=all_index.as_query_engine(),
      metadata=ToolMetadata(
        name="vector_index_all",
        description="useful for when you want to answer queries about anything. If the question is chitchat (hello, how are you, etc.), only use this tool."
      )
    )
    
    individual_query_engine_tools.append(general_query_engine_tool)
    
    query_engine = SubQuestionQueryEngine.from_defaults(
        query_engine_tools=individual_query_engine_tools,
        llm=AgentService.llm,
    )
    
    query_engine_tool = QueryEngineTool(
        query_engine=query_engine,
        metadata=ToolMetadata(
            name="sub_question_query_engine",
            description="useful for when you want to answer queries about anything that is not just chitchat (hello, how are you, etc.)",
        ),
    )
    
    tools = individual_query_engine_tools + [query_engine_tool]
    
    from llama_index.agent.openai import OpenAIAgent
    from llama_index.core.agent import ReActAgent
    # agent = OpenAIAgent.from_tools(tools, verbose=True)
    from services.react_callback_manager import ReActCallbackManager
    
    
    
    agent = ReActAgent.from_tools(tools, 
                                  verbose=False,
                                  # callback_manager=ReActCallbackManager(
                                  #   tenant_id=tenant_id,
                                  #   chat_session_id=session_id,
                                  #   writer=writer,
                                  #   reset=reset
                                  # )
                                  )
    
    from llama_index.core.chat_engine.types import ChatMode
    
    direct_chat = all_index.as_chat_engine(chat_mode=ChatMode.BEST, llm=AgentService.quick_llm)

    AgentService.session_agents_set[agent_key] = {
      "agent": agent,
      "chat": direct_chat,
      "expiry": datetime.now() + timedelta(minutes=60)
    }
    
    
    return [
      agent,
      direct_chat
    ]
    
  

  def report(self, reportRequest: ReportRequest) -> BasicResponse: 
    
    try:
      message_ref = (self.database
        .collection('tenants')
        .document(reportRequest.tenantId)
        .collection('chatSessions')
        .document(reportRequest.chatSessionId)
        .collection('messages')
        .document(reportRequest.id)
      )
      
      message_value = message_ref.get().to_dict()
      
      if message_value is None:
        return
      
      if message_value.get("message", None) != reportRequest.message:
        return
      
      similar_reports = (self.database
        .collection('tenants')  
        .document(reportRequest.tenantId)
        .collection('reports')
        .where('message', '==', reportRequest.message)  
      )
      
      similar_reports_value = similar_reports.get()
      
      # If there is a similar report, don't do anything and return
      if len(similar_reports_value) > 0:
        return 
      
      from firebase_admin import firestore
      
      payload = {
        "chatSessionId": reportRequest.chatSessionId,
        "messageId": reportRequest.id,
        "message": reportRequest.message,
        "status": "backlog",
        "previousMessage": reportRequest.previousMessage,
        "createdAt": firestore.SERVER_TIMESTAMP,
        "positiveFeedback": reportRequest.positiveFeedback,
      }
      
        
      self.database.collection('tenants').document(reportRequest.tenantId).collection('reports').add(payload)
      
      message_ref.update({
        "reported": True,
      })
      
      return
    
    except Exception as e:
      
      return BasicResponse("error", str(e))
      
  
  def init_index(self, tenant_id: str, index_id: str):
    path = self.get_folder_path(tenant_id)
    cat_path = path + index_id + "/"
      
    #ensure the directory exists
    os.makedirs(cat_path, exist_ok=True)
    
    storage_context = StorageContext.from_defaults(persist_dir=cat_path)
    
    cur_index = load_index_from_storage(storage_context)
    
    AgentService.tenant_indices_set[tenant_id][index_id] = cur_index
  
  def init_indices(self, tenant_id: str):
    """Initializes the indices for a tenant."""
    if tenant_id in AgentService.tenant_indices_set:
        return

    categories = self.category_repository.get_categories(tenant_id)
    
    
    if tenant_id not in AgentService.tenant_indices_set:
      AgentService.tenant_indices_set[tenant_id] = {}
    
    for category in categories.values():
      self.init_index(tenant_id, category["id"])
    
    self.init_index(tenant_id, "all")
  
    
    
  def get_writer_function(self, chat_message_manager: ChatMessageManager):
    return lambda message: chat_message_manager.write_agent_step(message)
  
  def get_reset_steps_function(self, chat_message_manager: ChatMessageManager):
    return lambda: chat_message_manager.reset_steps()
    
  
  async def agent_chat(self, 
                 tenant_id: str,
                 chat_session_id: str,
                 text: str) -> AgentChatResponse:
    
    is_chitchat = ChitChatDetector.is_chitchat(text)

    chat_message_manager = ChatMessageManager(self.database, tenant_id, chat_session_id)
    
    chat_message_manager.write_user_message(text)
    
    writer = self.get_writer_function(chat_message_manager)
    reset = self.get_reset_steps_function(chat_message_manager)
    
    tools = self.get_agent(tenant_id, chat_session_id, writer=writer, reset=reset)
    agent = tools[0]
    direct_chat = tools[1]
    
    chat_history = agent.chat_history
    direct_chat_history = direct_chat.chat_history
    
    # Use the biggest history
    chat_history = chat_history if len(chat_history) > len(direct_chat_history) else direct_chat_history
    
    if(len(chat_history) == 0):
    
      tenant = self.tenant_repository.get_tenant(tenant_id)
      chat_history = tenant.get_system_chat_messages()
      
    if(is_chitchat):
      response = await direct_chat.achat(
          message=text,
          chat_history=chat_history
      )
      
      chat_message_manager.write_agent_message(response.response)
      
      return AgentChatResponse(
        response= response.response,
        sources=response.sources,
        source_nodes=response.source_nodes,
      )
    
    
    response = await agent.achat(
        message=text,
        chat_history=chat_history,
        tool_choice="vector_index_all",
    ) 

    chat_message_manager.write_agent_message(response.response)
    
    
    if(is_chitchat):
      return AgentChatResponse(
        response= response.response,
        sources=response.sources,
        source_nodes=response.source_nodes,
      )
    
    links = LinksFetcher(self.database).get_links(tenant_id, text, response)
    
    json_links = json.dumps(links)
    
    chat_message_manager.write_links_message(json_links)
    
    
    return AgentChatResponse(
      response= response.response,
      sources=response.sources,
      source_nodes=response.source_nodes,
    )
    
      
      
      
    
  
    
          