import React from 'react'
import WebsiteConfig from './WebsiteConfig'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'


type Props = {}

export default function WebsiteConfigPage({ }: Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Website Configuration</CardTitle>
        <CardDescription>
          This is where you can configure your website settings for data scraping and training.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        <WebsiteConfig />
      </CardContent>
    </Card>
  )
}