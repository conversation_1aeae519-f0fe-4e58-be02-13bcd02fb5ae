from typing import Optional

class ReportRequest:
    def __init__(self, id: str, tenant_id: str, chatSessionId: str, previousMessage: str, message: str, positiveFeedback: bool):
        self.id = id
        self.tenant_id = tenant_id
        self.chatSessionId = chatSessionId
        self.previousMessage = previousMessage
        self.message = message
        self.positiveFeedback = positiveFeedback

class Report:
    def __init__(self, id: str, chatSessionId: str, createdAt: any, previousMessage: str, message: str, messageId: str, positiveFeedback: bool, correctReply: Optional[str] = None, status: Optional[str] = None, category: Optional[str] = None):
        self.id = id
        self
