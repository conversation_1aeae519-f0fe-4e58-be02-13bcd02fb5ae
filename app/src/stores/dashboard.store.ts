import { tr } from "date-fns/locale";
import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import computed from "zustand-computed";

export interface DashboardState {
  selectedClient: any;
  setSelectedClient: (project: any) => void;

  userName: string;
  tenantsList: any[];
  setUserName: (name: string) => void;
  setTenantsList: (tenants: any[]) => void;

  documents: any[];
  setDocuments: (documents: any[]) => void;

  chats: any[];
  setChats: (chats: any[]) => void;

  reportStatuses: any[];
  setReportStatuses: (statuses: any[]) => void;
}

export interface ComputedState {
  selectedClientId: string | null;
}

const computeState = (state: DashboardState): ComputedState => ({
  selectedClientId: state.selectedClient?.value || null,
});

const useDashboardStore = create<DashboardState>()(
  persist(
    computed(
      (set) => ({
        selectedClient: null,
        setSelectedClient: (project) => set({ selectedClient: project }),

        userName: "",
        tenantsList: [],
        setUserName: (name) => set({ userName: name }),
        setTenantsList: (tenants) => set({ tenantsList: tenants }),

        documents: [],
        setDocuments: (documents) => set({ documents: documents }),

        chats: [],
        setChats: (chats) => set({ chats: chats }),

        reportStatuses: [],
        setReportStatuses: (statuses) => {
          set({ reportStatuses: statuses });
        },
      }),
      computeState
    ),
    {
      name: "dashboard-data", // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => sessionStorage), // (optional) by default, 'localStorage' is used
    }
  )
);

export default useDashboardStore;
