import * as cheerio from 'cheerio';
import type { NextRequest } from 'next/server';
import { imageSize } from "image-size";

function respond({ imageData, fileType, status }: { imageData?: Buffer; 
  fileType?: string;
  status: number }) {
  return new Response(imageData || '', {
    status,
    headers: {
      'Content-Type': fileType || 'image/png',
    },
  });
}

function extractImageSources(html: string) {
  const $ = cheerio.load(html);
  const urls: string[] = [];

  // First check for og:image
  const ogImage = $('meta[property="og:image"]').attr('content');
  if (ogImage) {
      return [ogImage]; // Return immediately if og:image is present
  }

  // Selector for <img> tags not ending with .svg and not containing certain keywords in alt
  const imgSelector = 'img:not([src$=".svg"]):not([alt*="logo"]):not([alt*="search"]):not([alt*="menu"]), ' +
                      'img:not([data-src$=".svg"]):not([alt*="logo"]):not([alt*="search"]):not([alt*="menu"])';

  // Selector for <figure> tags not having src or data-src ending with .svg
  const figureSelector = 'figure:not([src$=".svg"]), figure:not([data-src$=".svg"])';

  $(`${imgSelector}, ${figureSelector}`).each(function() {
      const src = $(this).attr('src') || $(this).attr('data-src');
      if (src) {
          urls.push(src); // Add the URL if it exists
      }
  });

  return urls;
}

async function filterBigImages(imageUrls: string[]): Promise<string[]> {
  const filteredUrls = await Promise.all(imageUrls.map(async (url) => {
    try {
      const imgResponse = await fetch(url);
      if (!imgResponse.ok) return null; // Skip if fetch fails or returns a bad status

      const buffer = await imgResponse.arrayBuffer();
      const image = Buffer.from(buffer);
      const sizeObj = imageSize(image);

      // Only return URL if image width is greater than 200
      if (sizeObj.width && sizeObj.width > 200) {
        return url;
      }
      return null;
    } catch (e) {
      console.error(`Failed to process image ${url}: ${e}`);
      return null; // Return null in case of any errors
    }
  }));

  // Explicitly remove null values and ensure the result is a string array
  return filteredUrls.filter((url): url is string => url !== null);
}



function respondEmptyJpeg() {
  return respond({
    status: 200,
    imageData: Buffer.from('R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==', 'base64'),
    fileType: 'image/jpeg',
  });
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  const resourceUrl = searchParams.get('url');


  if(!resourceUrl) {
    return respond({
      status: 400
    });
  }

  const response = await fetch(resourceUrl);

  if(!response.ok) {
    return respond({
      status: 500
    });
  }
  const targetWebsiteRoot = new URL(resourceUrl).origin;

  const html = await response.text();

  const $ = cheerio.load(html);

  const imageUrls = extractImageSources(html);

  const bigImages = await filterBigImages(imageUrls);

  if(bigImages.length === 0) {
    return respondEmptyJpeg();
  }

  const imageUrl = bigImages[0];

  const relativeUrl = imageUrl;
  const absoluteUrl = new URL(relativeUrl, targetWebsiteRoot).toString();


  const imageResponse = await fetch(absoluteUrl);

  if(!imageResponse.ok) {
    return respond({
      status: 500
    });
  }

  const imageBuffer = await imageResponse.arrayBuffer();
  const imageFileType = imageResponse.headers.get('content-type') || 'image/jpeg';

  return respond({
    imageData: Buffer.from(imageBuffer),
    fileType: imageFileType,
    status: 200
  });

}