"use client";
import ChatsTable from "@/components/AdminChats/ChatsTable";
import PageContainer from "@/components/ui/page-container";
import { getChatsQuery } from "@/repositories/tenant-repository";
import useDashboardStore from "@/stores/dashboard.store";
import { onSnapshot } from "firebase/firestore";
import React, { useEffect } from "react";

export default function ChatSessionsPage() {
  const chats = useDashboardStore((state) => state.chats);
  const setChats = useDashboardStore((state) => state.setChats);

  const tenantId = useDashboardStore((state) => state.selectedClient);

  function processDocuments(querySnapshot: any) {
    const documents = [] as any;
    querySnapshot.forEach((doc: any) => {
      documents.push({
        id: doc.id,
        ...doc.data(),
      });
    });
    setChats(documents);
    return documents;
  }

  const loadDocuments = async () => {
    const unsubscribe = onSnapshot(getChatsQuery(tenantId), (querySnapshot) => {
      processDocuments(querySnapshot);
    });

    return [unsubscribe];
  };

  useEffect(() => {
    if (!(tenantId && tenantId?.value)) {
      return;
    }

    let unsubscribes = [] as any;

    const processEffect = async () => {
      if (tenantId.value) {
        unsubscribes = await loadDocuments();
      }
    };

    processEffect();

    return () => {
      unsubscribes.forEach((unsubscribe: any) => {
        unsubscribe();
      });
    };
  }, [tenantId]);

  return (
    <PageContainer>
      <ChatsTable documents={chats} />
    </PageContainer>
  );
}
