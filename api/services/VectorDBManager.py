
# # Create marqo client
# mq = Client("http://ns3164501.ip-51-89-234.eu:8882")

# # Name your index
# index_name = "text-search"

# # mq.delete_index(index_name) 
# # # Create the movie index
# # mq.create_index(index_name, model="flax-sentence-embeddings/all_datasets_v4_mpnet-base")


# # Add documents (movie descriptions) to the index
# # mq.index(index_name).add_documents(
# #     [
# #         {
# #             "Title": "Inception",
# #             "Description": "A mind-bending thriller about dream invasion and manipulation.",
# #         },
# #         {
# #             "Title": "Shrek",
# #             "Description": "An ogre's peaceful life is disrupted by a horde of fairy tale characters who need his help.",
# #         },
# #         {
# #             "Title": "Interstellar",
# #             "Description": "A team of explorers travel through a wormhole in space to ensure humanity's survival.",
# #         },
# #         {
# #             "Title": "The Martian",
# #             "Description": "An astronaut becomes stranded on Mars and must find a way to survive.",
# #         },
# #     ],
# #     tensor_fields=["Description"],
# # )



# # Perform a search query on the index
# results = mq.index(index_name).search(q="I need a fantasy movie")

# # Print the search results
# for result in results["hits"]:
#     print(
#         f"Title: {result['Title']}, Description: {result['Description']}. Score: {result['_score']}"
#     )

from typing import List
from models.VectorDocument import VectorDocument

class VectorDBManager:
  
  def __init__(self):
    self.client = Client("http://ns3164501.ip-51-89-234.eu:8882")
    
  def create_index(self, index_name: str):
    """
    Create an index with the given name if it doesn't exist.
    """
      
    try:
      settings = {
        "index_defaults": {
            "text_preprocessing": {
                "split_length": 10,
                "split_overlap": 3,
                "split_method": "sentence"
            },
        },
      }
      
      self.client.create_index(index_name, 
                               model="hf/e5-base-v2")
                              # , settings_dict=settings)
      
    except Exception as e:
      pass
      
  def reset_index(self, index_name: str):
    """
    Reset the index with the given name.
    """
    
    try:
      self.client.delete_index(index_name)
      self.create_index(index_name)
      
    except Exception as e:
      pass
    
  def add_documents(self, tenant_id: str, documents: List[VectorDocument]):
    """
    Add documents to the index.
    """
    
    try:
      
      # First, let's ensure the index exists
      self.create_index(tenant_id)
      
      # Add the documents
      
      for document in documents:
        output = self.client.index(tenant_id).add_documents(
          [{
            "title": document.title,
            "content": document.content,
            "url": document.url,
            "categoryId": document.categoryId
          }],
          tensor_fields=["content"]
        )
        ""
      
    except Exception as e:
      pass
    
  def add_graphql(self, tenant_id: str, documents: List[VectorDocument]):
    """
    Add documents to the index.
    """
    
    try:
      
      # First, let's ensure the index exists
      self.create_index(tenant_id)
      
      # Add the documents
      
      for document in documents:
        output = self.client.index(tenant_id).add_documents(
          [{
            "title": document.title,
            "content": document.content,
            "url": document.url,
            "categoryId": document.categoryId
          }],
          tensor_fields=["content"]
        )
        ""
      
    except Exception as e:
      pass
    
  def search(self, tenant_id: str, query: str, category_id: str):
    """
    Search the index for the given query.
    """
    
    try:
      
      # Perform the search
      if(category_id == None or category_id == ""):
        results = self.client.index(tenant_id).search(q=query)
      else:
        results = self.client.index(tenant_id).search(q=query, filter_string=f"categoryId:{category_id}")
      
      return results["hits"]
      
    except Exception as e:
      return []