import { getCategories, getSitemaps, syncSitemapPages } from "@/repositories/server-sitemaps-repository";
import { getFullDataFromSitemap } from "./sitemap-helpers";
import { SitemapPage } from "@/models/sitemap-page";

export const getAllSiteContent = async (tenantId: string) => {
  const sitemaps = await getSitemaps(tenantId);

  let fullWebsiteData: SitemapPage[] = [];

  for (let i = 0; i < sitemaps.length; i++) {
    const sitemap = sitemaps[i];
    const sitemapData = await getFullDataFromSitemap(sitemap, tenantId);

    console.log(`Sitemap ${i + 1} of ${sitemaps.length} done! Number of pages: ${sitemapData.length}`);

    sitemapData.forEach((page) => {
      page.sitemapId = sitemap.id;
      
      // Generate a clean ID from the URL
      page.id = page.url
        .replace(/^https?:\/\//, '')    // Remove protocol
        .replace(/\/$/, '')             // Remove trailing slash
        .replace(/\//g, '-')            // Replace slashes with hyphens
        .replace(/[^a-zA-Z0-9-]/g, ''); // Remove any characters that are not alphanumeric or hyphens
    });
    
    fullWebsiteData = [...fullWebsiteData, ...sitemapData];
  }
  
  await syncSitemapPages(tenantId, fullWebsiteData);

  return fullWebsiteData;
};
