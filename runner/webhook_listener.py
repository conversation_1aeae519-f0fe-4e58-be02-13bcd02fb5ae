from flask import Flask, request
import subprocess
import git
import os

app = Flask(__name__)

current_api_folder = "api_v1"
alternate_api_folder = "api_v2"

@app.route('/webhook', methods=['POST'])
def webhook():
    global current_api_folder, alternate_api_folder

    if request.method == 'POST':
        # Pull the latest code into the alternate folder
        repo_path = f"/app/source_code/{alternate_api_folder}"
        repo = git.Repo(repo_path)
        origin = repo.remotes.origin
        origin.pull()

        # Install any updated Python packages
        subprocess.run(["pip", "install", "-r", f"{repo_path}/requirements.txt"])

        # Stop the old API
        subprocess.run(["pkill", "-f", f"hypercorn.*{current_api_folder}"])

        # Start the new API
        subprocess.run(["hypercorn", f"{repo_path}/your_app:app", "--bind", "0.0.0.0:5000", "--daemon"])

        # Swap the current and alternate folders for the next update
        current_api_folder, alternate_api_folder = alternate_api_folder, current_api_folder

        return 'Webhook received, API updated', 200
    else:
        return 'Unsupported request', 400

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000)
