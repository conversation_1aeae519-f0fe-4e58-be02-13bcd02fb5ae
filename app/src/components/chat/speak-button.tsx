import React from "react";
import { BsPlay } from "react-icons/bs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

type Props = {
  message: string;
};

const htmlToPlainText = (html: string): string => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");
  return doc.body.textContent || "";
};

export default function SpeakButton({ message }: Props) {
  const speakMessage = async () => {
    speechSynthesis.cancel(); // Clear any previous utterances

    const plainMessage = htmlToPlainText(message);

    const sentences = plainMessage.split("."); // This is a naive split on periods

    for (const sentence of sentences) {
      const utterance = new SpeechSynthesisUtterance(sentence);
      speechSynthesis.speak(utterance);

      // Wait for the utterance to finish before moving on
      await new Promise((resolve) => {
        utterance.onend = resolve;
      });
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <button className="flex items-center gap-2" onClick={speakMessage}>
            <div className=" shadow text-foreground shadow-slate-500 h-5 w-5 rounded-full flex justify-center items-center">
              <BsPlay className="inline-block text-[15px] relative left-[1px]"></BsPlay>
            </div>
          </button>
        </TooltipTrigger>
        <TooltipContent>Play message</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
