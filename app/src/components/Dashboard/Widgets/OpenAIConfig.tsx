"use client";

import GenericConfig, {
  FieldDetails,
} from "@/components/Dashboard/Widgets/GenericConfig";
import { Button } from "@/components/ui/button";
import DialogButton from "@/components/ui/dialog-button";
import { toast } from "@/components/ui/use-toast";
import { getAPIUrl } from "@/lib/utils";
import useDashboardStore from "@/stores/dashboard.store";
import axios from "axios";
import { z } from "zod";

const OPENAI_CONFIG_DETAILS: FieldDetails = {
  model: {
    label: "Model",
    description: "Choose the OpenAI model version.",
    formSchema: z.string(),
    selectValues: ["gpt-3.5-turbo", "gpt-4"], // Add more models as needed
  },
  temperature: {
    label: "Temperature",
    description: "Set the randomness factor for generated outputs.",
    formSchema: z.number(),
  },
  openAIKey: {
    label: "OpenAI Key",
    description: "Provide your OpenAI API key.",
    formSchema: z.string(),
  },
};

export default function OpenAIConfig() {
  const clientName = useDashboardStore((state) => state.selectedClient)?.value;

  const retrainAI = async () => {
    const url = `/api/dashboard/retrain`;

    const postData = {
      tenantId: clientName,
    };

    const { data } = await axios.post(url, postData);

    if (data?.status === "success") {
      toast({
        title: "Success!",
        description: "AI successfully retrained!",
      });
    } else {
      toast({
        title: "Error!",
        description: "Something went wrong.",
      });
    }
  };

  return (
    <>
      <GenericConfig
        fieldDetails={OPENAI_CONFIG_DETAILS}
        collectionString="openai"
        buttonName="Save OpenAI Configuration"
      />
      <DialogButton
        variant={"destructive"}
        callback={retrainAI}
        okayText="Retrain the AI"
        headerText="Are you sure?"
        closedToastTitle="Retraining the AI..."
        closedToastText="This may take a moment."
        button={
          <div className="flex items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-6 h-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
              />
            </svg>
            Retrain the AI
          </div>
        }
      >
        Retraining the AI may take up to a few minutes, during which time the
        chatbot will not be available.
      </DialogButton>
    </>
  );
}
