from typing import Any, List, Dict
from llama_index.core.indices import VectorStoreIndex

from llama_index.core.indices.base_retriever import BaseRetriever
from llama_index.core.llms import LLM, ChatMessage
from llama_index.llms.openai import OpenAI
from llama_index.core.chat_engine.context import ContextChatEngine
from llama_index.core.data_structs.data_structs import IndexDict
from llama_index.core.indices.base import BaseIndex
from llama_index.core.chat_engine.types import (
    BaseChatEngine,
)
from models.tenant import Tenant
# import openai
import openai
from llama_index.core.settings import Settings

import os
from services.chatnav_retriever import ChatnavRetriever


class EngineContext:
  
  
  
  def __init__(self, llm: LLM, index: VectorStoreIndex, chat_engine: BaseChatEngine, generic_chat_engine: BaseChatEngine):
    """
    DO NOT USE THIS CONSTRUCTOR. Use the from_defaults method instead.
    """
    self.llm: LLM = llm
    self.index: BaseIndex[IndexDict] = index
    self.chat_engine: BaseChatEngine = chat_engine
    self.generic_chat_engine: BaseChatEngine = generic_chat_engine
    
  @classmethod
  def from_defaults(cls, 
      tenant: Tenant, 
      is_system: bool,
      system_messages: List[ChatMessage]) -> 'EngineContext':
    """
    Creates an EngineContext with values from the tenant object.
    
    :param tenant: The tenant object to use for the EngineContext.
    :type tenant: Tenant
    
    :param is_system: Whether or not this is a system chat.
    :type is_system: bool
    
    :param storage_context: The storage context to use for the EngineContext.
    :type storage_context: StorageContext
    
    :param system_messages: The system messages to use for the EngineContext.
    :type system_messages: List[ChatMessage]
    
    :return: An EngineContext with values from the tenant object.
    :rtype: EngineContext
    """
    
    import openai
    
    llm: LLM
    
    # if is_system:
      # llm = OpenAI(model="gpt-4", temperature=0.1, api_key=tenant.openai.openAIKey)
      # llm = OpenAI(model="gpt-3.5-turbo", temperature=0.1, api_key=tenant.openai.openAIKey)
    # else:
      # llm = OpenAI("gpt-3.5-turbo", tenant.openai.temperature, api_key=tenant.openai.openAIKey)
    
    # llm = OpenAI(model="gpt-3.5-turbo", temperature=tenant.openai.temperature, api_key=tenant.openai.openAIKey)
    from llama_index.llms.groq import Groq
    llm = Groq(model="mixtral-8x7b-32768", api_key="********************************************************")
    import tiktoken

    Settings.llm = llm
    Settings.tokenizer = tiktoken.encoding_for_model("gpt-3.5-turbo").encode
    
    # TODO: Make generic if we have to handle multiple API keys
    openai.api_key = tenant.openai.openAIKey
    
    from llama_index.core.storage.index_store import SimpleIndexStore
    cwd = os.path.dirname(os.path.abspath(__file__))
    folder_path = os.path.join(cwd, f"indexes/{tenant.id}")
    folder_name = folder_path
    

    from llama_index.core.storage import (
        StorageContext
    )
    from llama_index.core.indices import load_index_from_storage
    storage_context = StorageContext.from_defaults(persist_dir=folder_name)
    
    index_standard = load_index_from_storage(storage_context)
    retriever = index_standard.as_retriever()
    
    from services.empty_retriever import EmptyRetriever
    empty_retriever = EmptyRetriever()
    
    # cwd = os.path.dirname(os.path.abspath(__file__))
    # folder_path = os.path.join(cwd, f"indexes/{tenant.id}_GPT4/")
    # folder_name = folder_path
    
    # gpt4_storage_context = StorageContext.from_defaults(persist_dir=folder_name)
    # index_gpt4 = load_index_from_storage(storage_context=gpt4_storage_context)
    
    # gpt4_retriever = index_gpt4.as_retriever()
    
    chatnav_retriever = ChatnavRetriever(kb_retriever=retriever)
    
    chat_engine = ContextChatEngine.from_defaults(
      prefix_messages=system_messages,
      retriever=chatnav_retriever,
    )
    
    generic_chat_engine = ContextChatEngine.from_defaults(
      retriever=empty_retriever,
    )
    
    return cls(llm, index_standard, chat_engine, generic_chat_engine)
  
  def get_chat_engine(self) -> BaseChatEngine:
    """
    Retrieves the chat engine from the EngineContext.
    
    :return: The chat engine from the EngineContext.
    :rtype: BaseChatEngine
    """
    return self.chat_engine
  
  def get_generic_chat_engine(self) -> BaseChatEngine:
    """
    Retrieves the generic chat engine from the EngineContext.
    
    :return: The generic chat engine from the EngineContext.
    :rtype: BaseChatEngine
    """
    return self.generic_chat_engine