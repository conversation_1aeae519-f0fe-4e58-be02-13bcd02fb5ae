import Link from "next/link";
import React from "react";
import { DarkModeSwitcher } from "@/components/theme/dark-mode-switcher";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import ProjectsSwitcher from "@/components/Dashboard/ProjectsSwitcher";
import { usePathname, useRouter } from "next/navigation";
import { auth } from "@/services/firebase.config";
import { useToast } from "../ui/use-toast";
import { cn, getCurrentPath } from "@/lib/utils";
import NavLink from "./NavLink";
import { useAuthState } from "react-firebase-hooks/auth";
import { Sheet } from "lucide-react";
import {
  SheetContent,
  She<PERSON><PERSON>es<PERSON>,
  She<PERSON>Header,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "../ui/sheet";
import UserProfile from "../User/UserProfile";

export default function DashboardHeader({
  tenantsList,
}: {
  tenantsList: any[];
}) {
  const router = useRouter();
  const { toast } = useToast();

  const [user, loading, error] = useAuthState(auth);

  const logout = async () => {
    try {
      await auth.signOut();
      router.push("/login");
    } catch (error) {
      toast({ description: "Logout failed", title: "Error" });
      console.log(error);
    }
  };
  const pathName = usePathname();

  return (
    <div className="bg-muted">
      <div className="p-4">
        <div className="flex items-center justify-between w-full gap-4 pb-6">
          <Link href="/hub" className="text-2xl font-bold">
            ChatNav Dashboard
          </Link>

          <div className="flex items-center gap-4 ">
            <DarkModeSwitcher />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Avatar className="cursor-pointer">
                  <AvatarImage
                    src="https://github.com/shadcn.png"
                    alt="@shadcn"
                  />
                  <AvatarFallback>CN</AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem>
                    <Link href="/hub/profile">My Profile</Link>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={logout}>Log out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <ProjectsSwitcher tenants={tenantsList} />
          <div className="flex items-center w-full">
            <NavLink href="/hub/documents">Knowledge Base</NavLink>
            <NavLink href="/hub/settings">Settings</NavLink>
            <NavLink href="/hub/tools">Tools</NavLink>
            <NavLink href="/hub/feedback">Feedback Queue</NavLink>
            <NavLink href="/hub/chat-sessions">Chat Sessions</NavLink>
          </div>
        </div>
      </div>
    </div>
  );
}
