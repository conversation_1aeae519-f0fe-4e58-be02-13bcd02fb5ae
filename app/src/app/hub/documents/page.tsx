"use client";
import React, { useMemo } from "react";
import { doc, onSnapshot, updateDoc } from "firebase/firestore";
import { useEffect } from "react";
import useDashboardStore from "@/stores/dashboard.store";
import DocumentTable from "@/components/Documents/DocumentTable";

import {
  getDocumentsQuery,
  loadUserData,
} from "@/repositories/tenant-repository";
import WandIcon from "@/components/icons/wand";
import Container from "@/components/general/Container";
import { useStore } from "@/stores/use-store";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useFirestoreSync } from "@/lib/firebase-hooks";
import { FeedbackCategory } from "@/models/feedbackCategory";
import { cn } from "@/lib/utils";
import { db } from "@/services/firebase.config";

export default function DocumentPage() {
  const selectedClient = useDashboardStore((state) => state.selectedClient);

  const documents = useDashboardStore((state) => state.documents);
  const setDocuments = useDashboardStore((state) => state.setDocuments);

  const selectedClientId = useDashboardStore((state) => state.selectedClientId);

  function processDocuments(querySnapshot: any) {
    const documents = [] as any;
    querySnapshot.forEach((doc: any) => {
      documents.push({
        id: doc.id,
        ...doc.data(),
      });
    });
    setDocuments(documents);
    return documents;
  }

  const loadDocuments = async () => {
    const unsubscribe = onSnapshot(
      getDocumentsQuery(selectedClient),
      (querySnapshot) => {
        processDocuments(querySnapshot);
      }
    );

    return [unsubscribe];
  };

  const queryConstraints = useMemo(() => [], []);

  const {
    paginatedDocuments: categories,
  } = useFirestoreSync<FeedbackCategory>(
    `tenants/${selectedClientId}/reportCategories`,
    queryConstraints
  );

  useEffect(() => {
    if (!selectedClientId) {
      return;
    }

    let unsubscribes = [] as any;

    const processEffect = async () => {
      if (selectedClientId) {
        unsubscribes = await loadDocuments();
      }
    };

    processEffect();

    return () => {
      unsubscribes.forEach((unsubscribe: any) => {
        unsubscribe();
      });
    };
  }, [selectedClient]);

  const updateDocumentCategory = async (document: any, categoryId: string) => {
    updateDoc(doc(db, `tenants/${selectedClientId}/documents/${document.id}`), {
      categoryId: categoryId,
    });
  }



  return (
    <Container>
      <div className="p-4 m-4 mx-auto border rounded-lg shadow-lg ">
        <h3 className="text-lg font-semibold">
          <WandIcon className="mr-2" />
          Knowledge Base
        </h3>
        <p className="text-sm text-gray-500">
          These documents are used to train the AI. Make sure the information is
          as accurate as possible.
        </p>
        {selectedClientId && (
          <DocumentTable
            documents={documents}
            clientName={selectedClientId}
            actions={[
              (props: any) =>

                <>
                  <Select onValueChange={(value: string) => {
                    updateDocumentCategory(props.document, value);
                  }}
                    defaultValue={props.document.categoryId}>
                    <SelectTrigger className={cn("w-[180px]", "")}>
                      <SelectValue placeholder="Select an option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Category</SelectLabel>
                        {categories.map((option) => (
                          <SelectItem key={option.id} value={option.id}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </>

            ]}
          />
        )}
      </div>
    </Container>
  );
}
