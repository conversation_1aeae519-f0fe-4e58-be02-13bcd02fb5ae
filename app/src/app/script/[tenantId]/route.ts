import { db } from "@/services/firebase.config";
import { doc, getDoc } from "firebase/firestore";
import JavaScriptObfuscator from "javascript-obfuscator";
import { database } from "@/config/firebaseAdmin";
import { da } from "date-fns/locale";

export async function GET(
  request: Request,
  { params }: { params: { tenantId: string } }
) {
  const return404 = () =>
    new Response("Not found", {
      status: 404,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Content-Type": "text/plain",
      },
    });

  const docRef = database.collection("tenants").doc(params.tenantId);

  const docSnap = (await docRef.get()).data();
  if (!docSnap) {
    return return404();
  }

  const widgetInfo = docSnap.widget;

  if (!widgetInfo) {
    return return404();
  }

  const hasShadow = widgetInfo.shadow == "Enabled";
  const buttonIcon = widgetInfo.buttonIcon;
  const buttonIconColour = widgetInfo.buttonIconColour;
  const buttonColour = widgetInfo.buttonColour;
  const buttonPosition =
    widgetInfo.buttonPosition == "Bottom Left"
      ? "button.style.bottom = '16px'; button.style.left = '16px';"
      : "button.style.bottom = '16px'; button.style.right = '16px';";
  const isDev = process.env.NODE_ENV === "development";

  const serviceUrl = isDev
    ? "http://localhost:3000"
    : "https://multiply-chatbot.web.app";

    
    const script = `
document.addEventListener("DOMContentLoaded", function() {
  let metaViewport = document.querySelector('meta[name="viewport"]');
  if (metaViewport) {
    metaViewport.setAttribute("content", "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no");
  } else {
    metaViewport = document.createElement('meta');
    metaViewport.name = "viewport";
    metaViewport.content = "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no";
    document.head.appendChild(metaViewport);
  }

  // Inject CSS styles
  const style = document.createElement('style');
  style.textContent = \`
    #mpy-chatbot-button {
      width: 60px;
      height: 60px;
      text-align: center;
      overflow: hidden;
      position: fixed;
      z-index: 999999;
      background: ${buttonColour}!important;
      color: ${buttonIconColour};
      border-radius: 50%;
      border: none;
      cursor: pointer;
      outline: none;
      ${hasShadow ? "box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);" : ""}
    }
    #mpy-chatbot-button.bottom-left {
      bottom: 16px;
      left: 16px;
    }
    #mpy-chatbot-button.bottom-right {
      bottom: 16px;
      right: 16px;
    }
    .mpy-chatbot {
      width: 440px;
      height: 645px;
      position: fixed;
      bottom: 110px;
      right: 16px;
      z-index: 99999;
      display: none;
      background: transparent;
      border: none;
      scrolling: no;
    }
    @media (max-width: 768px) {
      .mpy-chatbot {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: 0;
        left: 0;
      }
      #mpy-chatbot-button {
        right: 16px;
        top: auto;
      }
      #mpy-chatbot-button.open {
        top: 16px;
        bottom: auto;
      }
    }
  \`;
  document.head.appendChild(style);

  // Create the button element
  const button = document.createElement('button');
  button.innerHTML = '${buttonIcon}';
  button.className = "mpy-chatbot-button ${widgetInfo.buttonPosition == "Bottom Left" ? "bottom-left" : "bottom-right"}";
  button.id = "mpy-chatbot-button";

  button.addEventListener('click', toggleChatbot);

  // Append the button to the body of the page
  document.body.appendChild(button);

  // Create the iframe element
  const iframe = document.createElement('iframe');

  // Get the parent URL
  const parentUrl = encodeURIComponent(window.location.href);

  // Set the source of the iframe
  iframe.src = "${serviceUrl}/widget/${
    params.tenantId
  }?parentUrl=" + parentUrl;
  iframe.className = "mpy-chatbot";
  iframe.style.display = "none"; // Explicitly set the initial display to "none"

  // Append the iframe to the body of the page
  document.body.appendChild(iframe);

  function toggleChatbot() {
    const isHidden = iframe.style.display === "none";

    iframe.style.display = isHidden ? "block" : "none";
    button.innerHTML = isHidden 
      ? "<span style='font-size: 50px; color: white; display: inline-block;'>&times;</span>"
      : '${buttonIcon}';

    if (window.innerWidth <= 768) { // Mobile breakpoint
      button.classList.toggle('open', isHidden);
    }
  }
});
`;


  return new Response(
    script,
    {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Content-Type": "text/javascript",
      },
    }
  );
}
