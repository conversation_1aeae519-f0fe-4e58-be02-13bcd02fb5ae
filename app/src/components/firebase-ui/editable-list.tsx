import { useState, useEffect } from "react";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { db } from "@/services/firebase.config";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

interface EditableListConfig {
  collectionName: string;
  initialState: string[];
  fetchKey: string;
  updateKey: string;
}

function EditableList({
  collectionName,
  initialState,
  fetchKey,
  updateKey,
}: EditableListConfig) {
  const [items, setItems] = useState(initialState);

  const handleSubmit = async () => {
    const docRef = doc(db, collectionName, fetchKey);
    try {
      await updateDoc(docRef, { [updateKey]: items });
      toast({
        title: "Success!",
        description: "Data updated successfully.",
      });
    } catch (e) {
      toast({
        title: "error",
        description: "Something went wrong.",
      });
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      const docRef = doc(db, collectionName, fetchKey);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists() && docSnap.data()[updateKey]) {
        setItems(docSnap.data()[updateKey]);
      }
    };

    fetchData();
  }, [fetchKey]);

  const [hoveredIndex, setHoveredIndex] = useState(-1);

  return (
    <>
      {items.map((item, index) => (
        <div
          key={index}
          className="flex gap-4 mb-4"
          onMouseEnter={() => setHoveredIndex(index)}
          onMouseLeave={() => setHoveredIndex(-1)}
        >
          <Textarea
            value={item}
            onChange={(e) => {
              let newItems = [...items];
              newItems[index] = e.target.value;
              setItems(newItems);
            }}
          />
          <div
            style={{
              opacity: hoveredIndex === index ? 1 : 0,
            }}
            className="flex items-center gap-2 transition-opacity duration-200"
          >
            <Dialog>
              <DialogTrigger asChild>
                <div className="flex items-center justify-center px-4 py-2 rounded-full shadow-md cursor-pointer shadow-slate-500/50 bg-foreground text-background">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-4 h-4"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                    />
                  </svg>
                  <span className="text-xs uppercase">Delete</span>
                </div>
              </DialogTrigger>
              <DialogContent>
                <div>Are you sure you want to delete this item?</div>
                <DialogFooter>
                  <Button
                    variant={"destructive"}
                    onClick={() => {
                      let newItems = [...items];
                      newItems.splice(index, 1);
                      setItems(newItems);
                    }}
                  >
                    Yes
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      ))}

      <div className="flex gap-4">
        <Button variant={"secondary"} onClick={() => setItems([...items, ""])}>
          Add Item
        </Button>
        <Button onClick={handleSubmit}>Save</Button>
      </div>
    </>
  );
}

export default EditableList;
