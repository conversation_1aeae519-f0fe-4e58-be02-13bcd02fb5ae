# models/GraphQLDocument.py

from models.BaseDocument import BaseDocument
from llama_index.core import Document as LlamaDocument

class GraphQLDocument(BaseDocument):
    def __init__(self, title: str, content: str, id: str):
        self.title = title
        self.content = content
        self.id = id

    def to_llama_document(self):
        metadata = {
            "title": self.title,
            "graphql_id": self.id,
        }
        return LlamaDocument(text=self.content, metadata=metadata)
