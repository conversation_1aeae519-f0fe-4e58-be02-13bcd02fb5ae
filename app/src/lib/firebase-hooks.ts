import { db } from "@/services/firebase.config";
import {
  collection,
  query,
  where,
  onSnapshot,
  addDoc,
  deleteDoc,
  doc,
  updateDoc,
  QueryConstraint,
  Query,
  QuerySnapshot,
  getDocs,
  limit,
  startAfter,
  QueryDocumentSnapshot,
  getCountFromServer,
  startAt,
} from "firebase/firestore";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

export function useFirestoreSync<T extends { id?: string }>(
  basePath: string,
  queryConstraints: QueryConstraint[],
  config: {
    perPage?: number;
  } = { perPage: 20 }
): 
  {
    paginatedDocuments: T[];
    save: (document: T) => Promise<void>;
    remove: (id: string) => Promise<void>;
    goToPage: (page: number) => void;
    goToNextPage: () => void;
    goToPreviousPage: () => void;
    currentPage: number;
    maxPages: number;
    totalDocuments: number;
  }
 {
  const [documents, setDocuments] = useState<T[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [lastDocument, setLastDocument] =
    useState<QueryDocumentSnapshot<T> | null>(null);
  const [totalDocuments, setTotalDocuments] = useState(0);
  const [maxPages, setMaxPages] = useState(0);
  const [perPage, setPerPage] = useState(config.perPage || 20);

  const refreshCount = async () => {
    const result = await getCountFromServer(
      query(collection(db, basePath), ...queryConstraints)
    );

    try {
      const total = result?.data()?.count;
      setTotalDocuments(total);
      setMaxPages(Math.ceil(total / perPage));
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    let q = query(
      collection(db, basePath),
      ...queryConstraints,
      limit(1000)
    ) as Query<T>;

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const docs = querySnapshot.docs.map((doc) => ({
        ...doc.data(),
        id: doc.id,
      })) as T[];

      console.log(`Setting ${docs.length} documents`);
      setDocuments(docs);
      refreshCount();
    });

    return () => {
      unsubscribe(); // This will ensure the listener is detached when the component is unmounted
    };
  }, [basePath, queryConstraints, currentPage]); // Keeping dependencies minimal

  const save = async (document: T) => {
    const collectionRef = collection(db, basePath);
    if (document.id) {
      const docRef = doc(collectionRef, document.id);
      await updateDoc(docRef, document);
    } else {
      await addDoc(collectionRef, document);
    }
  };

  const remove = async (id: string) => {
    const collectionRef = collection(db, basePath);
    const docRef = doc(collectionRef, id);
    await deleteDoc(docRef);
  };

  const goToNextPage = () => {
    if (currentPage < maxPages) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 0) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const goToPage = (page: number) => {
    if (page >= 0 && page <= maxPages) {
      setCurrentPage(page);
    }
  };

  const paginatedDocuments = useMemo(() => {
    return documents.slice((currentPage - 1) * perPage, currentPage * perPage);
  }, [documents, currentPage, perPage]);

  return {
    paginatedDocuments,
    save,
    remove,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    currentPage,
    maxPages,
    totalDocuments,
  };
}

export const useFirestore = <T extends { id?: string }>(
  basePath: string,
  queryConstraints: QueryConstraint[]
): [T[], (document: T) => Promise<void>, (id: string) => Promise<void>] => {
  const [documents, setDocuments] = useState<T[]>([]);

  const processEffects = async () => {
    const q = query(collection(db, basePath), ...queryConstraints) as Query<T>;

    const docsRef = await getDocs(q);

    const docs = docsRef.docs.map((doc) => ({
      ...doc.data(),
      id: doc.id,
    })) as T[];

    console.log(`Setting ${docs.length} documents (useFirestore)`);

    setDocuments(docs);
  };

  useEffect(() => {
    processEffects();
  }, [basePath, queryConstraints]); // Keeping dependencies minimal

  const save = async (document: T) => {
    const collectionRef = collection(db, basePath);
    if (document.id) {
      const docRef = doc(collectionRef, document.id);
      await updateDoc(docRef, document);
    } else {
      await addDoc(collectionRef, document);
    }
  };

  const remove = async (id: string) => {
    const collectionRef = collection(db, basePath);
    const docRef = doc(collectionRef, id);
    await deleteDoc(docRef);
  };

  return [documents, save, remove];
};
