export type ReportRequest = {
  id: string;
  tenantId: string;
  chatSessionId: string;
  previousMessage: string;
  message: string;
  positiveFeedback: boolean;
};

export type Report = {
  id: string;
  chatSessionId: string;
  createdAt: any;
  previousMessage: string;
  message: string;
  messageId: string;
  positiveFeedback: boolean;
  correctReply?: string;
  status?: string;
  category?: string;
};

export type TrainReportRequest = {
  tenantId: string;
};
