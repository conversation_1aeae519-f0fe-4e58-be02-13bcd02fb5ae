from llama_index.core.storage import StorageContext
import os
from typing import Any, List, Dict, Callable, Optional
from llama_index.core.llms import LLM, ChatMessage
from typing import Optional
from llama_index.core.chat_engine.types import StreamingAgentChatResponse,  AgentChatResponse
from llama_index.core.agent import ReActAgent

from llama_index.core.llms import LLM
from llama_index.llms.openai import OpenAI
from llama_index.core.storage import StorageContext
import openai
import tiktoken
from llama_index.core.query_engine import SubQuestionQueryEngine

from google.cloud.firestore import Client
from llama_index.core.settings import Settings
import logging
from repositories.category_repository import CategoryRepository
from repositories.tenant_repository import TenantRepository
from llama_index.core.indices import load_index_from_storage
from llama_index.core.tools import QueryEngineTool, ToolMetadata

logging.basicConfig(level=logging.INFO)
from llama_index.core.callbacks.global_handlers import set_global_handler
from llama_index.core.llms.llm import LLM

from services.chat_message_manager import ChatMessageManager


from datetime import datetime, timedelta

set_global_handler("simple")

from llama_index.core.indices.base import BaseIndex


class LinksFinder(): 
  