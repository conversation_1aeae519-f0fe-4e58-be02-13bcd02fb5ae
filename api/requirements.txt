aiofiles==23.2.1
aiohappyeyeballs==2.4.3
aiohttp==3.10.10
aiosignal==1.3.1
aiostream==0.5.2
annotated-types==0.7.0
anthropic==0.28.1
anyio==4.6.2.post1
appdirs==1.4.4
asgiref==3.8.1
asttokens==2.4.1
async-timeout==4.0.3
attrs==24.2.0
backcall==0.2.0
beautifulsoup4==4.12.3
bleach==6.1.0
blinker==1.8.2
bs4==0.0.2
CacheControl==0.14.0
cachetools==5.3.3
certifi==2024.8.30
cffi==1.16.0
charset-normalizer==3.4.0
click==8.1.7
cloudevents==1.10.1
cmake==3.29.5.1
cryptography==42.0.8
dataclasses-json==0.6.7
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.14
deprecation==2.1.0
dirtyjson==1.0.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.6.1
docopt==0.6.2
email_validator==2.2.0
executing==2.0.1
fastapi==0.111.1
fastapi-cli==0.0.4
fastjsonschema==2.20.0
filelock==3.15.1
firebase-admin==6.5.0
Flask==3.0.3
Flask-Cors==4.0.1
frozenlist==1.4.1
fs==2.4.16
fsspec
functions-framework==3.7.0
gcsfs==2024.6.0
gitdb==4.0.11
GitPython==3.1.43
google-api-core==2.19.0
google-api-python-client==2.133.0
google-auth==2.30.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.0
google-cloud==0.34.0
google-cloud-core==2.4.1
google-cloud-firestore==2.16.0
google-cloud-storage==2.17.0
google-crc32c==1.5.0
google-events==0.12.0
google-resumable-media==2.7.1
googleapis-common-protos==1.63.1
greenlet==3.1.1
grpcio==1.64.1
grpcio-status==1.62.2
grpclib==0.4.7
gunicorn==22.0.0
h11==0.14.0
h2==4.1.0
hiredis==2.3.2
hpack==4.0.0
httpcore==1.0.6
httplib2==0.22.0
httptools==0.6.1
httpx==0.27.2
huggingface-hub==0.23.4
Hypercorn==0.17.3
hyperframe==6.0.1
idna==3.10
itsdangerous==2.2.0
jedi==0.19.1
Jinja2==3.1.4
jiter==0.6.1
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.22.0
jsonschema-specifications==2023.12.1
jupyter_client==8.6.2
jupyter_core==5.7.2
jupyterlab_pygments==0.3.0
langchain-core==0.2.9
langchain-text-splitters==0.2.1
langsmith==0.1.80
lit==18.1.7
llama-cloud==0.1.4
llama-index==0.11.19
llama-index-agent-openai==0.3.4
llama-index-cli==0.3.1
llama-index-core==0.11.19
llama-index-embeddings-openai==0.2.5
llama-index-indices-managed-llama-cloud==0.4.0
llama-index-legacy==0.9.48.post3
llama-index-llms-openai==0.2.15
llama-index-multi-modal-llms-openai==0.2.2
llama-index-program-openai==0.2.0
llama-index-question-gen-openai==0.2.0
llama-index-readers-file==0.2.2
llama-index-readers-llama-parse==0.3.0
llama-parse==0.5.10
llamaindex-py-client==0.1.19
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.23.0
matplotlib-inline==0.1.7
mdurl==0.1.2
mistune==3.0.2
modal==0.63.63
more-itertools==10.3.0
mpmath==1.3.0
msgpack==1.0.8
multidict==6.1.0
mypy-extensions==1.0.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.4.1
nltk==3.9.1
numexpr==2.10.0
numpy==1.26.4
nvidia-cublas-cu11==11.11.3.6
nvidia-cublas-cu12==12.4.5.8
nvidia-cuda-cupti-cu11==11.8.87
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu11==11.8.89
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu11==11.8.89
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu11==9.1.1.17
nvidia-cudnn-cu12==9.1.0.70
nvidia-cufft-cu11==10.9.0.58
nvidia-cufft-cu12==********
nvidia-curand-cu11==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu11==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu11==*********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu11==2.21.5
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu11==11.8.86
nvidia-nvtx-cu12==12.4.127
oauthlib==3.2.2
openai==1.52.0
orjson==3.10.5
packaging==24.1
pandas==2.2.3
pandocfilters==1.5.1
parso==0.8.4
pexpect==4.9.0
pickleshare==0.7.5
pillow==11.0.0
pip-check==2.9
platformdirs==4.2.2
priority==2.0.0
prompt_toolkit==3.0.47
propcache==0.2.0
proto-plus==1.23.0
protobuf==4.25.3
ptyprocess==0.7.0
pure-eval==0.2.2
pyasn1==0.6.0
pyasn1_modules==0.4.0
pycparser==2.22
pydantic==2.9.2
pydantic_core==2.23.4
Pygments==2.18.0
PyJWT==2.8.0
PyMuPDF==1.24.5
PyMuPDFb==1.24.3
pyparsing==3.1.2
pypdf==4.3.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.9
python-ulid==1.1.0
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.0.3
Quart==0.19.6
quart-cors==0.7.0
redis==5.0.6
redis-om==0.3.1
referencing==0.35.1
regex==2024.9.11
requests==2.32.3
requests-oauthlib==2.0.0
rich==13.7.1
rpds-py==0.18.1
rsa==4.9
safetensors==0.4.3
scikit-learn==1.5.0
scipy==1.13.1
sentence-transformers==2.2.2
sentencepiece==0.2.0
shellingham==1.5.4
sigtools==4.0.1
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.36
stack-data==0.6.3
starlette==0.37.2
striprtf==0.0.26
sympy==1.13.1
synchronicity==0.6.7
tenacity==8.5.0
terminaltables==3.1.10
threadpoolctl==3.5.0
tika==2.6.0
tiktoken==0.8.0
tinycss2==1.3.0
tokenizers==0.19.1
toml==0.10.2
torch==2.5.0
torchvision==0.20.0
tornado==6.4.1
tqdm==4.66.5
traitlets==5.14.3
transformers==4.41.2
triton==3.1.0
typer==0.12.3
types-certifi==2021.10.8.3
types-cffi==1.16.0.20240331
types-pyOpenSSL==24.1.0.20240425
types-redis==4.6.0.20240425
types-setuptools==70.0.0.20240524
types-toml==0.10.8.20240310
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.2
ua-parser==0.18.0
uritemplate==4.1.1
urllib3==2.2.3
user-agents==2.2.0
uvicorn==0.30.1
uvloop==0.19.0
watchdog==4.0.1
watchfiles==0.22.0
wcwidth==0.2.13
webencodings==0.5.1
websockets==12.0
Werkzeug==3.0.3
WooCommerce==3.0.0
wrapt==1.16.0
wsproto==1.2.0
yarg==0.1.9
yarl==1.15.5
