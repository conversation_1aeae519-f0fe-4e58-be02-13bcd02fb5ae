"use client";

import GenericConfig, {
  FieldDetails,
} from "@/components/Dashboard/Widgets/GenericConfig";
import { Button } from "@/components/ui/button";
import DialogButton from "@/components/ui/dialog-button";
import { toast } from "@/components/ui/use-toast";
import { getAPIUrl } from "@/lib/utils";
import useDashboardStore from "@/stores/dashboard.store";
import axios from "axios";
import { z } from "zod";

const OPENAI_CONFIG_DETAILS: FieldDetails = {
  defaultSelector: {
    label: "Default DOM Selector",
    description: "The default DOM selector for the website. Will determine what element holds the content to be analyzed. This can be overriden on a per-page basis.",
    formSchema: z.string(),
  }
};

export default function WebsiteConfig() {

  // const clientName = useDashboardStore((state) => state.selectedClient)?.value;

  return (
    <>
      <GenericConfig
        fieldDetails={OPENAI_CONFIG_DETAILS}
        collectionString="website"
        buttonName="Save Website Configuration"
      />
    </>
  );
}
