import { Button } from "@/components/ui/button";
import FormControl from "@/components/ui/formcontrol";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Report } from "@/models/report";
import useDashboardStore from "@/stores/dashboard.store";
import React, { useEffect } from "react";

export type Filters = {
  category: string;
  status: string;
};

type Props = {
  categories: any[];
  onFilterChange: (filters: Filters) => void;
};

export default function FeedbackFilters({ categories, onFilterChange }: Props) {
  const reportStatuses = useDashboardStore((state) => state.reportStatuses);

  const [selectedCategory, setSelectedCategory] = React.useState("");
  const [selectedStatus, setSelectedStatus] = React.useState("");

  const triggerChange = (
    selectedCategory: string = "",
    selectedStatus: string = ""
  ) => {
    onFilterChange({
      category: selectedCategory,
      status: selectedStatus,
    });
  };

  useEffect(() => {
    if (reportStatuses.length > 0) {
      const backlogStatus = (reportStatuses as Report[]).find(
        (reportStatus) => reportStatus.id === "backlog"
      );
      if (backlogStatus) {
        setSelectedStatus(backlogStatus.id);
        triggerChange(selectedCategory, backlogStatus.id);
      }
    }
  }, [reportStatuses]);

  useEffect(() => {
    if (categories.length > 0) {
      setSelectedCategory("");
      triggerChange("", selectedStatus);
    }
  }, [categories]);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex w-full gap-2">
        <Button
          variant={selectedStatus === "" ? "default" : "outline"}
          className="p-2 text-sm"
          onClick={() => {
            setSelectedStatus("");
            triggerChange();
          }}
        >
          All statuses
        </Button>
        {reportStatuses
          .sort((a: any, b: any) => a.order - b.order)
          .map((status: any) => (
            <Button
              variant={selectedStatus === status.id ? "default" : "outline"}
              onClick={(e) => {
                e.preventDefault();
                setSelectedStatus(status.id);
                triggerChange(selectedCategory, status.id);
              }}
            >
              {status.label}
            </Button>
          ))}
      </div>

      <div className="flex w-full gap-2">
        <Button
          variant={selectedCategory === "" ? "default" : "outline"}
          className="p-2 text-sm"
          onClick={() => {
            setSelectedCategory("");
            triggerChange("", "");
          }}
        >
          All categories
        </Button>
        {categories.map((category) => (
          <Button
            variant={selectedCategory === category.id ? "default" : "outline"}
            onClick={(e) => {
              e.preventDefault();
              setSelectedCategory(category.id);
              triggerChange(category.id, selectedStatus);
            }}
          >
            {category.label}
          </Button>
        ))}
      </div>
    </div>
  );
}
