"use client";

import * as React from "react";
import {
  CaretSortIcon,
  CheckIcon,
  PlusCircledIcon,
} from "@radix-ui/react-icons";

import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import useDashboardStore from "@/stores/dashboard.store";
import { addTenant } from "@/repositories/tenant-repository";
import { FidgetSpinner } from "react-loader-spinner";
import { Skeleton } from "../ui/skeleton";
import { collection, doc, getDocs } from "firebase/firestore";
import { db } from "@/services/firebase.config";

type Project = {
  label: string;
  value: string;
};

type PopoverTriggerProps = React.ComponentPropsWithoutRef<
  typeof PopoverTrigger
>;

interface ProjectSwitcherProps extends PopoverTriggerProps {
  tenants: any[];
}

export default function ProjectsSwitcher({
  className,
  tenants,
}: ProjectSwitcherProps) {
  const [open, setOpen] = React.useState(false);
  const [showNewProjectDialog, setShowNewProjectDialog] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(true);

  const [groups, setGroups] = React.useState<any[]>([]);

  const selectedClient = useDashboardStore((state) => state.selectedClient);

  React.useEffect(() => {
    if (tenants.length === 0) return;

    const teams = tenants.map((tenant) => ({
      label: tenant.name,
      value: tenant.id,
    }));

    const groups = [
      {
        label: "Clients",
        teams: teams as Project[],
      },
    ];

    setGroups(groups);
    if (
      !selectedClient &&
      !selectedClient?.value &&
      teams.length > 0 &&
      teams[0]
    ) {
      setSelectedClient(teams[0]);
      setStatuses(teams[0].value);
    } else {
      setSelectedClient(selectedClient);
      setStatuses(selectedClient?.value);
    }
    setIsLoading(false);
  }, [tenants, selectedClient]);

  const setSelectedClient = useDashboardStore(
    (state) => state.setSelectedClient
  );

  const reportStatuses = useDashboardStore((state) => state.reportStatuses);
  const setReportStatuses = useDashboardStore(
    (state) => state.setReportStatuses
  );

  const setStatuses = async (selectedClientId: string) => {
    const docRef = collection(
      db,
      "tenants",
      selectedClientId,
      "reportStatuses"
    );

    const docs = await getDocs(docRef);

    if (docs) {
      const statuses = docs.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
      setReportStatuses(statuses);
    }
  };

  const [newProjectName, setNewProjectName] = React.useState("");
  const [newProjectDescription, setNewProjectDescription] = React.useState("");

  const createNewProject = async () => {
    await addTenant({
      name: newProjectName,
      description: newProjectDescription,
    });
  };

  return (
    <>
      {isLoading && <Skeleton className="min-w-[212px] h-[36px]" />}
      {!isLoading && (
        <Dialog
          open={showNewProjectDialog}
          onOpenChange={setShowNewProjectDialog}
        >
          <Popover open={open} onOpenChange={setOpen}>
            {selectedClient && (
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  aria-label="Select a team"
                  className={cn("w-[250px] justify-between", className)}
                >
                  <Avatar className="w-5 h-5 mr-2">
                    <AvatarImage
                      src={`https://avatar.vercel.sh/${selectedClient.value}.png`}
                      alt={selectedClient.label}
                    />
                    <AvatarFallback>SC</AvatarFallback>
                  </Avatar>
                  {selectedClient.label}
                  <CaretSortIcon className="w-4 h-4 ml-auto opacity-50 shrink-0" />
                </Button>
              </PopoverTrigger>
            )}
            <PopoverContent className="w-[200px] p-0">
              <Command>
                <CommandList>
                  <CommandInput placeholder="Search team..." />
                  <CommandEmpty>No team found.</CommandEmpty>
                  {groups.map((group) => (
                    <CommandGroup key={group.label} heading={group.label}>
                      {group.teams.map((team: any) => (
                        <CommandItem
                          key={team.value}
                          onSelect={async () => {
                            setSelectedClient(team);
                            await setStatuses(team.value);
                            setOpen(false);
                          }}
                          className="text-sm"
                        >
                          <Avatar className="w-5 h-5 mr-2">
                            <AvatarImage
                              src={`https://avatar.vercel.sh/${team.value}.png`}
                              alt={team.label}
                              className="grayscale"
                            />
                            <AvatarFallback>SC</AvatarFallback>
                          </Avatar>
                          {team.label}
                          <CheckIcon
                            className={cn(
                              "ml-auto h-4 w-4",
                              selectedClient.value === team.value
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  ))}
                </CommandList>
                <CommandSeparator />
                <CommandList>
                  <CommandGroup>
                    <DialogTrigger asChild>
                      <CommandItem
                        onSelect={() => {
                          setOpen(false);
                          setShowNewProjectDialog(true);
                        }}
                      >
                        <PlusCircledIcon className="w-5 h-5 mr-2" />
                        Create Project
                      </CommandItem>
                    </DialogTrigger>
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create client</DialogTitle>
              <DialogDescription>
                This will add the client to the list of clients.
              </DialogDescription>
            </DialogHeader>
            <div>
              <div className="py-2 pb-4 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Client name</Label>
                  <Input
                    id="name"
                    placeholder="Acme Inc."
                    value={newProjectName}
                    onChange={(e) => setNewProjectName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    placeholder="Description"
                    value={newProjectDescription}
                    onChange={(e) => setNewProjectDescription(e.target.value)}
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowNewProjectDialog(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                onClick={() => {
                  createNewProject();
                  setShowNewProjectDialog(false);
                }}
              >
                Continue
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
