{"name": "multiply-chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "debug": "NODE_OPTIONS='--inspect=5055' next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@blocknote/core": "^0.8.2", "@blocknote/react": "^0.8.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.1.1", "@mdxeditor/editor": "^0.22.5", "@radix-ui/react-avatar": "1.0.3", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-dropdown-menu": "2.0.5", "@radix-ui/react-hover-card": "1.0.6", "@radix-ui/react-icons": "1.3.0", "@radix-ui/react-label": "2.0.2", "@radix-ui/react-navigation-menu": "1.1.3", "@radix-ui/react-popover": "1.0.6", "@radix-ui/react-scroll-area": "1.0.4", "@radix-ui/react-select": "1.2.2", "@radix-ui/react-separator": "1.0.3", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-switch": "1.0.3", "@radix-ui/react-tabs": "1.0.4", "@radix-ui/react-toast": "1.1.4", "@radix-ui/react-tooltip": "1.0.7", "@remirror/extension-markdown": "^2.0.13", "@remirror/pm": "^2.0.8", "@remirror/react": "^2.0.35", "@remirror/react-editors": "^1.0.38", "@tanstack/react-table": "^8.9.3", "@types/node": "20.3.3", "@types/react": "18.2.14", "@types/react-dom": "18.2.6", "@types/uuid": "^9.0.2", "@xyflow/react": "^12.3.1", "autoprefixer": "10.4.14", "axios": "^1.4.0", "beautiful-dom": "^1.0.9", "cheerio": "1.0.0-rc.12", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "encoding": "^0.1.13", "firebase": "^9.23.0", "firebase-admin": "^11.10.1", "formik": "^2.4.3", "image-size": "^1.1.1", "javascript-obfuscator": "^4.0.2", "lodash.debounce": "^4.0.8", "lucide-react": "^0.263.1", "multiply-llamaindex": "^0.0.25", "next": "13.4.7", "next-themes": "^0.2.1", "node-loader": "^2.0.0", "postcss": "8.4.24", "prosemirror-commands": "^1.5.2", "react": "18.2.0", "react-color": "^2.19.3", "react-daisyui": "^4.0.0", "react-day-picker": "^8.8.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.2.0", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.45.2", "react-icons": "^4.10.1", "react-loader-spinner": "^5.3.4", "react-toastify": "^9.1.3", "remirror": "^2.0.36", "sass": "^1.63.6", "tailwind-merge": "^1.14.0", "tailwindcss": "3.3.2", "tailwindcss-animate": "^1.0.6", "typescript": "5.1.6", "uuid": "^9.0.0", "xml2js": "^0.6.2", "zod": "^3.21.4", "zustand": "^4.4.0", "zustand-computed": "^1.3.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.9", "@types/react-color": "^3.0.6", "@types/xml2js": "^0.4.14", "daisyui": "^3.1.7"}}