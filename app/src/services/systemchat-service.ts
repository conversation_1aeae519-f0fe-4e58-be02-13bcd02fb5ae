import { getAPIUrl } from "@/lib/utils";
import { FeedbackRequest, FeedbackResponse } from "@/models/feedback";
import axios from "axios";
import { database } from "@/config/firebaseAdmin";
import { getFirebaseApiEndpoints } from "./api-connector/api-connector";

const fetchGPT4ProcessedFeedback = async (
  feedbackRequest: FeedbackRequest
): Promise<FeedbackResponse> => {
  const categoriesRef = database
    .collection("tenants")
    .doc(feedbackRequest.tenantId)
    .collection("reportCategories")
    .get();

  const categories = (await categoriesRef).docs.map((doc) => {
    return {
      id: doc.id,
      ...doc.data(),
    };
  });

  const categoriesString = categories
    .map((category) => {
      return JSON.stringify(category);
    })
    .join("\n");

  const systemMessages = [
    `You are the Sleepeezee feedback report system. You take in a question, and you give the correct category for that particular question. Available categories: 
    ${categoriesString}
    `,
    `Your answer should always be in perfectly JSON format like {"category": "<category id>", "correctReplyAccordingToData": "<put correct reply here>", "dataMissingInContext": true/false, confidenceIndex: <between 0 and 1>}. Don't use double quotes inside the correctReplyAccordingToData, use single quotes instead. The confidence index is a numeric value between 0 and 1 and should be an indication of how confident the AI is in putting the correctReplyAccordingToData directly in production.`,
    "If a question is about getting a product for a particular health position, or a type of sleeper, do mention that you cannot give medical advice, but still give the best product available given the question",
    "Subjective questions, like 'What is the best', comparison questions, etc. may have a lower confidence index, and the AI shouldn't have hard opinions. The AI assistant is there to help the user make their own choice, not to manipulate them into a sale, when the AI has not been able to try the products by itself.",
    "Do not say that 'the information is not provided in the data', give the best available reply instead.",
    "If 'positiveFeedback' is false, you need to ensure you give a brand new reply, and not reuse the one given by the assistant.",
  ];

  const text = JSON.stringify({
    positiveFeedback: feedbackRequest.positiveFeedback,
    messages: feedbackRequest.messages,
  });

  const fullRequest = {
    tenantId: feedbackRequest.tenantId,
    text: text,
    systemMessages: systemMessages,
  };

  const endpoints = getFirebaseApiEndpoints();

  const response = await endpoints.systemChat({
    tenantId: feedbackRequest.tenantId,
    text: text,
    systemMessages: systemMessages,
  });

  console.log("Response from systemchat", { response });

  try {
    return JSON.parse(response.response) as FeedbackResponse;
  } catch (e) {
    console.error("Error parsing JSON", e);
    return {
      category: "Error parsing JSON",
      correctReplyAccordingToData: "Error parsing JSON",
      dataMissingInContext: false,
      confidenceIndex: 0,
    };
  }
};

export { fetchGPT4ProcessedFeedback };
