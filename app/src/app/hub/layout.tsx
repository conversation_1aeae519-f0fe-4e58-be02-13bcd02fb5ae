"use client";
import React from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { auth } from "@/services/firebase.config";
import { useEffect } from "react";
import useDashboardStore from "@/stores/dashboard.store";

import DashboardHeader from "@/components/Dashboard/dashboard-header";
import { loadUserData } from "@/repositories/tenant-repository";

export default function DashboardLayout({ children }: { children: React.ReactNode }) {

  const [user] = useAuthState(auth);

  const tenantsList = useDashboardStore((state) => state.tenantsList);

  const setUserName = useDashboardStore((state) => state.setUserName);
  const setTenantsList = useDashboardStore((state) => state.setTenantsList);


  useEffect(() => {
    let unsubscribes = [] as any;
    const processEffect = async () => {
      if (user) {
        unsubscribes = await loadUserData(user, setUserName, setTenantsList);
      }
    };

    processEffect();

    return () => {
      unsubscribes.forEach((unsubscribe: any) => {
        unsubscribe();
      });
    };
  }, [user]);


  return (
    <div className="h-screen bg-background">
      <DashboardHeader tenantsList={tenantsList} />
      {children}
    </div>
  )
}
