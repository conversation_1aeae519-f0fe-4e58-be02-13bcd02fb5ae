:root,
.light-theme {
  --blue1: hsl(206, 100%, 99.2%);
  --blue2: hsl(210, 100%, 98%);
  --blue3: hsl(209, 100%, 96.5%);
  --blue4: hsl(210, 98.8%, 94%);
  --blue5: hsl(209, 95%, 90.1%);
  --blue6: hsl(209, 81.2%, 84.5%);
  --blue7: hsl(208, 77.5%, 76.9%);
  --blue8: hsl(206, 81.9%, 65.3%);
  --blue9: hsl(206, 100%, 50%);
  --blue10: hsl(208, 100%, 47.3%);
  --blue11: hsl(211, 100%, 43.2%);
  --blue12: hsl(211, 100%, 15%);
}
.dark-theme {
  --blue1: hsl(212, 35%, 9.2%);
  --blue2: hsl(216, 50%, 11.8%);
  --blue3: hsl(214, 59.4%, 15.3%);
  --blue4: hsl(214, 65.8%, 17.9%);
  --blue5: hsl(213, 71.2%, 20.2%);
  --blue6: hsl(212, 77.4%, 23.1%);
  --blue7: hsl(211, 85.1%, 27.4%);
  --blue8: hsl(211, 89.7%, 34.1%);
  --blue9: hsl(206, 100%, 50%);
  --blue10: hsl(209, 100%, 60.6%);
  --blue11: hsl(210, 100%, 66.1%);
  --blue12: hsl(206, 98%, 95.8%);
}
:root,
.light-theme {
  --slate1: hsl(206, 30%, 98.8%);
  --slate2: hsl(210, 16.7%, 97.6%);
  --slate3: hsl(209, 13.3%, 95.3%);
  --slate4: hsl(209, 12.2%, 93.2%);
  --slate5: hsl(208, 11.7%, 91.1%);
  --slate6: hsl(208, 11.3%, 88.9%);
  --slate7: hsl(207, 11.1%, 85.9%);
  --slate8: hsl(205, 10.7%, 78%);
  --slate9: hsl(206, 6%, 56.1%);
  --slate10: hsl(206, 5.8%, 52.3%);
  --slate11: hsl(206, 6%, 43.5%);
  --slate12: hsl(206, 24%, 9%);
}
.dark-theme {
  --slate1: hsl(200, 7%, 8.8%);
  --slate2: hsl(195, 7.1%, 11%);
  --slate3: hsl(197, 6.8%, 13.6%);
  --slate4: hsl(198, 6.6%, 15.8%);
  --slate5: hsl(199, 6.4%, 17.9%);
  --slate6: hsl(201, 6.2%, 20.5%);
  --slate7: hsl(203, 6%, 24.3%);
  --slate8: hsl(207, 5.6%, 31.6%);
  --slate9: hsl(206, 6%, 43.9%);
  --slate10: hsl(206, 5.2%, 49.5%);
  --slate11: hsl(206, 6%, 63%);
  --slate12: hsl(210, 6%, 93%);
}
:root,
.light-theme {
  --grass1: hsl(116, 50%, 98.9%);
  --grass2: hsl(120, 60%, 97.1%);
  --grass3: hsl(120, 53.6%, 94.8%);
  --grass4: hsl(121, 47.5%, 91.4%);
  --grass5: hsl(122, 42.6%, 86.5%);
  --grass6: hsl(124, 39%, 79.7%);
  --grass7: hsl(126, 37.1%, 70.2%);
  --grass8: hsl(131, 38.1%, 56.3%);
  --grass9: hsl(131, 41%, 46.5%);
  --grass10: hsl(132, 43.1%, 42.2%);
  --grass11: hsl(133, 50%, 32.5%);
  --grass12: hsl(130, 30%, 14.9%);
}
:root,
.light-theme {
  --cyan1: hsl(185, 60%, 98.7%);
  --cyan2: hsl(185, 73.3%, 97.1%);
  --cyan3: hsl(186, 70.2%, 94.4%);
  --cyan4: hsl(186, 63.8%, 90.6%);
  --cyan5: hsl(187, 58.3%, 85.4%);
  --cyan6: hsl(188, 54.6%, 78.4%);
  --cyan7: hsl(189, 53.7%, 68.7%);
  --cyan8: hsl(189, 60.3%, 52.5%);
  --cyan9: hsl(190, 95%, 39%);
  --cyan10: hsl(191, 91.2%, 36.8%);
  --cyan11: hsl(192, 85%, 31%);
  --cyan12: hsl(192, 88%, 12.5%);
}
:root,
.light-theme {
  --amber1: hsl(39, 70%, 99%);
  --amber2: hsl(40, 100%, 96.5%);
  --amber3: hsl(44, 100%, 91.7%);
  --amber4: hsl(43, 100%, 86.8%);
  --amber5: hsl(42, 100%, 81.8%);
  --amber6: hsl(38, 99.7%, 76.3%);
  --amber7: hsl(36, 86.1%, 67.1%);
  --amber8: hsl(35, 85.2%, 55.1%);
  --amber9: hsl(39, 100%, 57%);
  --amber10: hsl(35, 100%, 55.5%);
  --amber11: hsl(30, 100%, 34%);
  --amber12: hsl(20, 80%, 17%);
}
:root,
.light-theme {
  --red1: hsl(359, 100%, 99.4%);
  --red2: hsl(359, 100%, 98.6%);
  --red3: hsl(360, 100%, 96.8%);
  --red4: hsl(360, 97.9%, 94.8%);
  --red5: hsl(360, 90.2%, 91.9%);
  --red6: hsl(360, 81.7%, 87.8%);
  --red7: hsl(359, 74.2%, 81.7%);
  --red8: hsl(359, 69.5%, 74.3%);
  --red9: hsl(358, 75%, 59%);
  --red10: hsl(358, 69.4%, 55.2%);
  --red11: hsl(358, 65%, 48.7%);
  --red12: hsl(354, 50%, 14.6%);
}
.mdxeditor .diff {
  --diff-font-family: var(--font-mono);
  font-size: var(--text-xs);
}
/** Code mirror */
.mdxeditor .cm-editor {
  --sp-font-mono: var(--font-mono);
  --sp-font-body: var(--font-body);
  padding: var(--sp-space-4) 0;
}
.mdxeditor .cm-scroller {
  padding: 0 !important;
}
.mdxeditor .sp-wrapper {
  border: 1px solid var(--baseLine);
  border-radius: var(--radius-medium);
  overflow: hidden;
}
.mdxeditor .sp-layout {
  border: none;
}
.mdxeditor .sp-cm pre {
  white-space: break-spaces;
  word-break: break-word;
  overflow-wrap: anywhere;
  flex-shrink: 1;
}
._editorRoot_npm11_36 {
  --accentBase: var(--blue1);
  --accentBgSubtle: var(--blue2);
  --accentBg: var(--blue3);
  --accentBgHover: var(--blue4);
  --accentBgActive: var(--blue5);
  --accentLine: var(--blue6);
  --accentBorder: var(--blue7);
  --accentBorderHover: var(--blue8);
  --accentSolid: var(--blue9);
  --accentSolidHover: var(--blue10);
  --accentText: var(--blue11);
  --accentTextContrast: var(--blue12);

  --baseBase: var(--slate1);
  --baseBgSubtle: var(--slate2);
  --baseBg: var(--slate3);
  --baseBgHover: var(--slate4);
  --baseBgActive: var(--slate5);
  --baseLine: var(--slate6);
  --baseBorder: var(--slate7);
  --baseBorderHover: var(--slate8);
  --baseSolid: var(--slate9);
  --baseSolidHover: var(--slate10);
  --baseText: var(--slate11);
  --baseTextContrast: var(--slate12);

  --admonitionTipBg: var(--cyan4);
  --admonitionTipBorder: var(--cyan8);

  --admonitionInfoBg: var(--grass4);
  --admonitionInfoBorder: var(--grass8);

  --admonitionCautionBg: var(--amber4);
  --admonitionCautionBorder: var(--amber8);

  --admonitionDangerBg: var(--red4);
  --admonitionDangerBorder: var(--red8);

  --admonitionNoteBg: var(--slate4);
  --admonitionNoteBorder: var(--slate8);

  --spacing-0: 0px;
  --spacing-px: 1px;
  --spacing-0_5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1_5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2_5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3_5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-11: 2.75rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-28: 7rem;
  --spacing-32: 8rem;
  --spacing-36: 9rem;
  --spacing-40: 10rem;
  --spacing-44: 11rem;
  --spacing-48: 12rem;
  --spacing-52: 13rem;
  --spacing-56: 14rem;
  --spacing-60: 15rem;
  --spacing-64: 16rem;
  --spacing-72: 18rem;
  --spacing-80: 20rem;
  --spacing-96: 24rem;

  --radius-none: 0px;
  --radius-small: var(--spacing-0_5);
  --radius-base: var(--spacing-1);
  --radius-medium: var(--spacing-1_5);
  --radius-large: var(--spacing-2);
  --radius-extra-large: var(--spacing-3);
  --radius-full: 9999px;

  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  --font-body: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;

  --text-base: 1rem;
  --text-sm: 0.875rem;
  --text-xs: 0.75rem;

  font-family: var(--font-body);
  color: var(--baseText);
}

._editorWrapper_npm11_133 {
}

._nestedListItem_npm11_136 {
  list-style: none;
}

._toolbarRoot_npm11_140 {
  /* border: var(--spacing-px) solid var(--baseBorder); */
  z-index: 1;
  display: flex;
  flex-direction: row;
  gap: var(--spacing-1);
  border-radius: var(--radius-medium);
  padding: var(--spacing-2) var(--spacing-2);
  align-items: center;
  overflow-x: auto;
  position: sticky;
  top: 0;
  background-color: var(--baseBgSubtle);
  width: inherit;
}

._toolbarRoot_npm11_140 div[role="separator"] {
  margin: var(--spacing-2) var(--spacing-2);
  border-left: 1px solid var(--baseBorder);
  border-right: 1px solid var(--baseBase);
  height: var(--spacing-4);
}

._readOnlyToolbarRoot_npm11_163 {
  pointer-events: none;
  background: var(--baseBase);
}

._readOnlyToolbarRoot_npm11_163 > div {
  opacity: 0.5;
}

._toolbarModeSwitch_npm11_171 {
  opacity: 1 !important;
  margin-left: auto;
  align-self: stretch;
  align-items: stretch;
  display: flex;
  border: 1px solid var(--baseBg);
  border-radius: var(--radius-medium);
  font-size: var(--text-xs);
}

._toolbarModeSwitch_npm11_171 ._toolbarToggleItem_npm11_181 {
  padding-inline-end: var(--spacing-4);
  padding-inline-start: var(--spacing-4);
}

._toolbarModeSwitch_npm11_171 ._toolbarToggleItem_npm11_181:active,
._toolbarModeSwitch_npm11_171 ._toolbarToggleItem_npm11_181[data-state="on"] {
  background-color: var(--baseBorder);
}

._toolbarGroupOfGroups_npm11_191 {
  display: flex;
  margin: 0 var(--spacing-1);
}

._toolbarToggleSingleGroup_npm11_196:first-of-type
  ._toolbarToggleItem_npm11_181:only-child,
._toolbarToggleSingleGroup_npm11_196:only-child
  ._toolbarToggleItem_npm11_181:first-child,
._toolbarModeSwitch_npm11_171 ._toolbarToggleItem_npm11_181:first-child {
  border-top-left-radius: var(--radius-base);
  border-bottom-left-radius: var(--radius-base);
}

._toolbarToggleSingleGroup_npm11_196:last-of-type
  ._toolbarToggleItem_npm11_181:only-child,
._toolbarToggleSingleGroup_npm11_196:only-child
  ._toolbarToggleItem_npm11_181:last-child,
._toolbarModeSwitch_npm11_171 ._toolbarToggleItem_npm11_181:last-child {
  border-top-right-radius: var(--radius-base);
  border-bottom-right-radius: var(--radius-base);
}

._toolbarToggleItem_npm11_181,
._toolbarButton_npm11_210 {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-1);
}

._toolbarToggleItem_npm11_181 svg,
._toolbarButton_npm11_210 svg {
  display: block;
}

._toolbarToggleItem_npm11_181:hover,
._toolbarButton_npm11_210:hover {
  background-color: var(--baseBgHover);
}

._toolbarToggleItem_npm11_181:active svg,
._toolbarButton_npm11_210:active svg {
  transform: translate(1px, 1px);
}

._toolbarToggleItem_npm11_181[data-state="on"],
._toolbarButton_npm11_210[data-state="on"],
._toolbarToggleItem_npm11_181:active,
._toolbarButton_npm11_210:active {
  background-color: var(--accentBgActive);
}

._toolbarToggleItem_npm11_181[data-disabled],
._toolbarButton_npm11_210[data-disabled] {
  pointer-events: none;
  color: var(--baseBorderHover);
}

._toolbarButton_npm11_210 {
  border-radius: var(--radius-base);
}

._activeToolbarButton_npm11_240 {
  color: var(--accentText);
}

._toolbarToggleSingleGroup_npm11_196 {
  white-space: nowrap;
}

._toolbarNodeKindSelectContainer_npm11_248,
._toolbarButtonDropdownContainer_npm11_249,
._toolbarCodeBlockLanguageSelectContent_npm11_250,
._selectContainer_npm11_251 {
  z-index: 3;
  width: var(--spacing-36);
  border-bottom-left-radius: var(--radius-base);
  border-bottom-right-radius: var(--radius-base);
  background-color: var(--baseBgHover);
  font-size: var(--text-sm);
}

._toolbarButtonDropdownContainer_npm11_249 {
  border-top-right-radius: var(--radius-base);
}

._toolbarNodeKindSelectTrigger_npm11_265,
._toolbarButtonSelectTrigger_npm11_266,
._selectTrigger_npm11_267 {
  border: 0;
  background-color: transparent;
  display: flex;
  color: inherit;
  align-items: center;
  width: var(--spacing-36);
  padding: var(--spacing-1);
  padding-inline-start: var(--spacing-4);
  padding-inline-end: var(--spacing-2);
  border-radius: var(--radius-medium);
  white-space: nowrap;
  flex-wrap: nowrap;
  font-size: var(--text-sm);
}

._toolbarNodeKindSelectTrigger_npm11_265:hover,
._toolbarButtonSelectTrigger_npm11_266:hover,
._selectTrigger_npm11_267:hover {
  background-color: var(--baseBgHover);
}

._toolbarNodeKindSelectTrigger_npm11_265[data-state="open"],
._toolbarButtonSelectTrigger_npm11_266[data-state="open"],
._selectTrigger_npm11_267[data-state="open"] {
  border-bottom-right-radius: var(--radius-none);
  border-bottom-left-radius: var(--radius-none);
  background-color: var(--baseBgHover);
}
._selectTrigger_npm11_267[data-placeholder] > span:first-child {
  color: var(--baseBorderHover);
}

/** used in the sandpack */
._toolbarButtonSelectTrigger_npm11_266 {
  width: auto;
  padding-inline-start: var(--spacing-2);
  padding-inline-end: var(--spacing-1);
}

._toolbarCodeBlockLanguageSelectTrigger_npm11_303,
._toolbarCodeBlockLanguageSelectContent_npm11_250 {
  width: var(--spacing-48);
}

._toolbarNodeKindSelectItem_npm11_309,
._selectItem_npm11_310 {
  cursor: default;
  display: flex;
  padding: var(--spacing-2) var(--spacing-4);
}

._toolbarNodeKindSelectItem_npm11_309[data-highlighted],
._selectItem_npm11_310[data-highlighted] {
  background-color: var(--baseBgSubtle);
}

._toolbarNodeKindSelectItem_npm11_309[data-state="checked"],
._selectItem_npm11_310[data-state="checked"] {
  background-color: var(--accentBgActive);
}

._toolbarNodeKindSelectItem_npm11_309[data-highlighted],
._selectItem_npm11_310[data-highlighted] {
  outline: none;
}

._toolbarNodeKindSelectItem_npm11_309:last-child,
._selectItem_npm11_310:last-child {
  border-bottom-left-radius: var(--radius-base);
  border-bottom-right-radius: var(--radius-base);
}

._toolbarNodeKindSelectDropdownArrow_npm11_334,
._selectDropdownArrow_npm11_335 {
  margin-left: auto;
}

._contentEditable_npm11_339 {
  box-sizing: border-box;
  width: 100%;
  color: var(--baseTextContrast);
  padding: var(--spacing-3);
}

._contentEditable_npm11_339:focus {
  outline: none;
}

._sandpackWrapper_npm11_349 {
  margin-bottom: var(--spacing-5);
}

._frontmatterWrapper_npm11_353 {
  border-radius: var(--radius-medium);
  padding: var(--spacing-3);
  background-color: var(--baseBgSubtle);
}

._frontmatterWrapper_npm11_353[data-expanded="true"] {
  margin-bottom: var(--spacing-10);
}

._frontmatterToggleButton_npm11_363 {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--text-sm);
}

._propertyPanelTitle_npm11_371 {
  font-size: var(--text-xs);
  font-weight: 400;
  margin: 0;
  padding-top: var(--spacing-2);
  padding-left: var(--spacing-2);
}

._propertyEditorTable_npm11_379 {
  table-layout: fixed;
  border-spacing: var(--spacing-2);
}

._propertyEditorTable_npm11_379 th {
  text-align: left;
  font-size: var(--text-sm);
  padding: var(--spacing-2) var(--spacing-3);
}

._propertyEditorTable_npm11_379 col:nth-child(1) {
  width: 30%;
}

._propertyEditorTable_npm11_379 col:nth-child(2) {
  width: 70%;
}

._propertyEditorTable_npm11_379 td:last-child ._iconButton_npm11_396 {
  margin-left: var(--spacing-4);
  margin-right: var(--spacing-4);
}

._propertyEditorTable_npm11_379 ._readOnlyColumnCell_npm11_401 {
  padding-left: 0;
}

._propertyEditorLabelCell_npm11_406 {
  font-weight: 400;
}

._readOnlyColumnCell_npm11_401 {
  padding-left: 0;
}

._buttonsFooter_npm11_414 {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2);
}

._propertyEditorInput_npm11_420 {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-base);
  border: 1px solid var(--baseBorder);
  background-color: var(--baseBase);
  font-size: var(--text-sm);
}

._iconButton_npm11_396 {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  color: var(--baseText);
}

._iconButton_npm11_396:hover {
  color: var(--accentText);
}

._iconButton_npm11_396:disabled,
._iconButton_npm11_396:disabled:hover {
  color: var(--baseLine);
}

._primaryButton_npm11_441,
._secondaryButton_npm11_441 {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--accentBorder);
  background-color: var(--accentSolidHover);
  color: var(--baseBase);
  font-size: var(--text-sm);
  border-radius: var(--radius-medium);
}

._primaryButton_npm11_441:disabled,
._secondaryButton_npm11_441:disabled {
  background: var(--accentLine);
  border-color: var(--accentBg);
}

._smallButton_npm11_455 {
  font-size: var(--text-xs);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-base);
}

._secondaryButton_npm11_441 {
  border: 1px solid var(--baseBorder);
  background-color: var(--baseSolidHover);
  color: var(--baseBase);
}

._linkDialogEditForm_npm11_467 {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: var(--spacing-2);
}

._linkDialogInputContainer_npm11_474 {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

._linkDialogInputWrapper_npm11_480 {
  display: flex;
  align-items: center;
  background-color: var(--baseBase);

  border-radius: var(--radius-base);
  border: 1px solid var(--baseBorder);
}

._linkDialogInputWrapper_npm11_480[data-visible-dropdown="true"] {
  border-bottom-left-radius: var(--radius-none);
  border-bottom-right-radius: var(--radius-none);
  border-bottom-width: 0;
}

._linkDialogInputWrapper_npm11_480 > button {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding-right: var(--spacing-2);
}

._linkDialogInput_npm11_474 {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  width: 20rem;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--text-sm);
}

._linkDialogInput_npm11_474::-moz-placeholder {
  color: var(--baseBorder);
}

._linkDialogInput_npm11_474::placeholder {
  color: var(--baseBorder);
}

._linkDialogAutocompleteContainer_npm11_510 {
  position: relative;
}

._linkDialogAutocompleteContainer_npm11_510 ul {
  all: unset;
  box-sizing: border-box;
  position: absolute;
  font-size: var(--text-sm);
  width: 100%;
  display: none;
  border-bottom-left-radius: var(--radius-medium);
  border-bottom-right-radius: var(--radius-medium);
  max-height: var(--spacing-48);
  overflow-x: hidden;
  overflow-y: auto;
  border: 1px solid var(--baseBorder);
  border-top-width: 0;
  background-color: var(--baseBase);
}

._linkDialogAutocompleteContainer_npm11_510 ul[data-visible="true"] {
  display: block;
}

._linkDialogAutocompleteContainer_npm11_510 ul li {
  padding: var(--spacing-2) var(--spacing-3);
  white-space: nowrap;
  margin-bottom: var(--spacing-1);
  overflow-x: hidden;
  text-overflow: ellipsis;
}

._linkDialogAutocompleteContainer_npm11_510 ul li[data-selected="true"] {
  background-color: var(--baseBgSubtle);
}

._linkDialogAutocompleteContainer_npm11_510 ul li[data-highlighted="true"] {
  background-color: var(--baseBgHover);
}

._linkDialogAutocompleteContainer_npm11_510 ul li:last-of-type {
  border-bottom-left-radius: var(--radius-medium);
  border-bottom-right-radius: var(--radius-medium);
}

._linkDialogAnchor_npm11_555 {
  position: fixed;
  background-color: highlight;
  z-index: -1;
}

._linkDialogAnchor_npm11_555[data-visible="true"] {
  visibility: visible;
}

._linkDialogAnchor_npm11_555[data-visible="false"] {
  visibility: hidden;
}

._linkDialogPopoverContent_npm11_569,
._tableColumnEditorPopoverContent_npm11_569,
._dialogContent_npm11_569 {
  filter: drop-shadow(0 2px 2px rgb(0 0 0 / 0.2));
  display: flex;
  align-items: center;
  gap: var(--spacing-0_5);
  border-radius: var(--radius-medium);
  border: 1px solid var(--baseBorder);
  background-color: var(--baseBgSubtle);
  padding: var(--spacing-2) var(--spacing-2);
  font-size: var(--text-sm);
}

._largeDialogContent_npm11_581 {
  filter: drop-shadow(0 2px 2px rgb(0 0 0 / 0.2));
  gap: var(--spacing-0_5);
  border-radius: var(--radius-medium);
  border: 1px solid var(--baseBorder);
  background-color: var(--baseBgSubtle);
  padding: var(--spacing-4);
  font-size: var(--text-sm);
}

._dialogTitle_npm11_591 {
  font-size: var(--text-base);
  font-weight: 600;
  padding-left: var(--spacing-2);
}

._dialogCloseButton_npm11_597 {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  position: absolute;
  top: 10px;
  right: 10px;
}

._popoverContent_npm11_604 {
  filter: drop-shadow(0 2px 2px rgb(0 0 0 / 0.2));
  display: flex;
  align-items: center;
  gap: var(--spacing-0_5);
  border-radius: var(--radius-medium);
  background-color: var(--baseBgSubtle);
  padding: var(--spacing-2) var(--spacing-2);
  font-size: var(--text-sm);
  z-index: 1;
}

._popoverArrow_npm11_616 {
  fill: var(--baseBgSubtle);
}

._linkDialogPreviewAnchor_npm11_622 {
  margin-right: var(--spacing-3);
  display: flex;
  align-items: flex-start;
  color: var(--accentText);
  text-decoration: none;
  border: 1px solid transparent;
}

._linkDialogPreviewAnchor_npm11_622 svg {
  width: var(--spacing-5);
  height: var(--spacing-5);
  margin-left: var(--spacing-2);
}

._linkDialogPreviewAnchor_npm11_622 span {
  max-width: 14rem;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

._tooltipContent_npm11_644 {
  z-index: 2;
  position: relative;
  border-radius: var(--radius-medium);
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--text-xs);
  background-color: var(--baseText);
  color: var(--baseBase);
}

._tooltipContent_npm11_644 svg {
  fill: var(--baseText);
}

._actionButton_npm11_658 {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-2);
  padding: var(--spacing-1) var(--spacing-1);
  border-radius: var(--radius-medium);
}

._actionButton_npm11_658 svg {
  display: block;
}

._actionButton_npm11_658:hover {
  background-color: var(--baseBgHover);
}

._actionButton_npm11_658:active svg {
  transform: translate(1px, 1px);
}

._actionButton_npm11_658[data-state="on"],
._actionButton_npm11_658:active {
  background-color: var(--accentBgActive);
}

._primaryActionButton_npm11_664 {
  background-color: var(--accentSolid);
  color: var(--baseBase);
}

._primaryActionButton_npm11_664:hover {
  background-color: var(--accentSolidHover);
  color: var(--baseBase);
}

._tableEditor_npm11_673 {
  table-layout: fixed;
  width: 100%;
  height: 100%;
  border-spacing: 0;
  border-collapse: collapse;
}

._tableEditor_npm11_673 thead > tr > th {
  text-align: right;
}

._tableEditor_npm11_673 > tbody > tr > td:not(._toolCell_npm11_684) {
  border: 1px solid var(--baseBorder);
  padding: var(--spacing-1) var(--spacing-2);
  white-space: normal;
}

._tableEditor_npm11_673 > tbody > tr > td:not(._toolCell_npm11_684) > div {
  outline: none;
}

._tableEditor_npm11_673 > tbody > tr > td:not(._toolCell_npm11_684) > div > p {
  margin: 0;
}

._tableEditor_npm11_673
  > tbody
  > tr
  > td[data-active="true"]:not(._toolCell_npm11_684) {
  outline: solid 2px var(--accentSolid);
}

._tableEditor_npm11_673 ._tableColumnEditorTrigger_npm11_701,
._tableEditor_npm11_673 ._tableRowEditorTrigger_npm11_701,
._tableEditor_npm11_673 ._addRowButton_npm11_701,
._tableEditor_npm11_673 ._addColumnButton_npm11_701,
._tableEditor_npm11_673 ._iconButton_npm11_396 {
  opacity: 0.2;
}

._tableEditor_npm11_673:hover ._tableColumnEditorTrigger_npm11_701,
._tableEditor_npm11_673:hover ._tableRowEditorTrigger_npm11_701,
._tableEditor_npm11_673:hover ._addRowButton_npm11_701,
._tableEditor_npm11_673:hover ._addColumnButton_npm11_701,
._tableEditor_npm11_673:hover ._iconButton_npm11_396 {
  opacity: 0.5;
}

._tableEditor_npm11_673:hover ._tableColumnEditorTrigger_npm11_701:hover,
._tableEditor_npm11_673:hover ._tableRowEditorTrigger_npm11_701:hover,
._tableEditor_npm11_673:hover ._addRowButton_npm11_701:hover,
._tableEditor_npm11_673:hover ._addColumnButton_npm11_701:hover,
._tableEditor_npm11_673:hover ._iconButton_npm11_396:hover {
  opacity: 1;
}

._toolCell_npm11_684 {
  text-align: right;
}

._toolCell_npm11_684 button {
  margin: auto;
  display: block;
}

._tableColumnEditorTrigger_npm11_701 {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-2);
  padding: var(--spacing-1);
  border-radius: var(--radius-full);
  opacity: 0.2;
}

._tableColumnEditorTrigger_npm11_701 svg {
  display: block;
}

._tableColumnEditorTrigger_npm11_701:hover {
  background-color: var(--baseBgHover);
}

._tableColumnEditorTrigger_npm11_701:active svg {
  transform: translate(1px, 1px);
}

._tableColumnEditorTrigger_npm11_701[data-state="on"],
._tableColumnEditorTrigger_npm11_701:active {
  background-color: var(--accentBgActive);
}

._tableColumnEditorTrigger_npm11_701[data-active="true"] {
  opacity: 1;
}

._tableColumnEditorToolbar_npm11_735 {
  display: flex;
}

._tableColumnEditorToolbar_npm11_735 > button {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-2);
}

._tableColumnEditorToolbar_npm11_735 > button svg {
  display: block;
}

._tableColumnEditorToolbar_npm11_735 > button:hover {
  background-color: var(--baseBgHover);
}

._tableColumnEditorToolbar_npm11_735 > button:active svg {
  transform: translate(1px, 1px);
}

._tableColumnEditorToolbar_npm11_735 > button[data-state="on"],
._tableColumnEditorToolbar_npm11_735 > button:active {
  background-color: var(--accentBgActive);
}

._tableColumnEditorToolbar_npm11_735 [role="separator"] {
  margin-left: var(--spacing-1);
  margin-right: var(--spacing-1);
}

._toggleGroupRoot_npm11_747 {
  display: inline-flex;
}

._toggleGroupRoot_npm11_747 button {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-2);
}

._toggleGroupRoot_npm11_747 button svg {
  display: block;
}

._toggleGroupRoot_npm11_747 button:hover {
  background-color: var(--baseBgHover);
}

._toggleGroupRoot_npm11_747 button:active svg {
  transform: translate(1px, 1px);
}

._toggleGroupRoot_npm11_747 button[data-state="on"],
._toggleGroupRoot_npm11_747 button:active {
  background-color: var(--accentBgActive);
}

._toggleGroupRoot_npm11_747 button:first-child {
  border-top-left-radius: var(--radius-base);
  border-bottom-left-radius: var(--radius-base);
}

._toggleGroupRoot_npm11_747 button:last-child {
  border-top-right-radius: var(--radius-base);
  border-bottom-right-radius: var(--radius-base);
}

._tableToolsColumn_npm11_764 {
  width: 3rem;
}

._tableToolsColumn_npm11_764 button {
  margin: auto;
  display: block;
}

._leftAlignedCell_npm11_772 {
  text-align: left;
}

._rightAlignedCell_npm11_776 {
  text-align: right;
}

._centeredCell_npm11_780 {
  text-align: center;
}

._addColumnButton_npm11_701,
._addRowButton_npm11_701 {
  border: 0;
  background-color: transparent;
  font-size: inherit;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-2);
  background-color: var(--baseBase);

  display: flex;
  align-items: center;
  border-radius: var(--radius-medium);
}

._addColumnButton_npm11_701 svg,
._addRowButton_npm11_701 svg {
  display: block;
}

._addColumnButton_npm11_701:hover,
._addRowButton_npm11_701:hover {
  background-color: var(--baseBgHover);
}

._addColumnButton_npm11_701:active svg,
._addRowButton_npm11_701:active svg {
  transform: translate(1px, 1px);
}

._addColumnButton_npm11_701[data-state="on"],
._addRowButton_npm11_701[data-state="on"],
._addColumnButton_npm11_701:active,
._addRowButton_npm11_701:active {
  background-color: var(--accentBgActive);
}

._addColumnButton_npm11_701 svg,
._addRowButton_npm11_701 svg {
  margin: auto;
}

._addRowButton_npm11_701 {
  width: 100%;
  margin-top: var(--spacing-2);
  box-sizing: border-box;
}

._addColumnButton_npm11_701 {
  margin-left: var(--spacing-2);
  height: 100%;
}

/** Dialog */
._dialogOverlay_npm11_808 {
  position: fixed;
  inset: 0;
  animation: _overlayShow_npm11_1 150ms cubic-bezier(0.16, 1, 0.3, 1);
  background-color: var(--baseBase);
  z-index: 51;
  opacity: 0.5;
}

._dialogContent_npm11_569,
._largeDialogContent_npm11_581 {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: _contentShow_npm11_1 150ms cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 52;
}

._dialogContent_npm11_569:focus,
._largeDialogContent_npm11_581:focus {
  outline: none;
}

@keyframes _overlayShow_npm11_1 {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.5;
  }
}

@keyframes _contentShow_npm11_1 {
  from {
    opacity: 0;
    transform: translate(-50%, -48%) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

._focusedImage_npm11_851 {
  outline: highlight solid 2px;
}

._imageWrapper_npm11_855 {
  display: inline-block;
  position: relative;
}

._imageWrapper_npm11_855[draggable="true"] {
  cursor: move; /* fallback if grab cursor is unsupported */
  cursor: grab;
  cursor: -webkit-grab;
}

._inlineEditor_npm11_867 {
  display: inline-flex;
  border-radius: var(--radius-medium);
  padding: var(--spacing-1);
  gap: var(--spacing-2);
  align-items: center;
  background: var(--baseBg);
}

._blockEditor_npm11_876 {
  display: flex;
  justify-content: stretch;
  border-radius: var(--radius-medium);
  padding: var(--spacing-2);
  gap: var(--spacing-2);
  align-items: center;
  background: var(--baseBg);
}

._blockEditor_npm11_876 ._nestedEditor_npm11_885 {
  flex-grow: 1;
}

._nestedEditor_npm11_885 {
  background: white;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-medium);
}

._nestedEditor_npm11_885 > p {
  margin: 0;
}

._nestedEditor_npm11_885:focus {
  outline: none;
}

._genericComponentName_npm11_902 {
  font-size: var(--text-sm);
  color: var(--baseText);
  padding-right: var(--spacing-2);
}

._diffSourceToggle_npm11_908 {
  border: 1px solid var(--baseBorder);
  border-radius: var(--radius-medium);
  display: flex;
}

._diffSourceToggle_npm11_908 ._toolbarToggleItem_npm11_181 {
  padding: 0;
}

._diffSourceToggle_npm11_908 ._toolbarToggleItem_npm11_181 > span {
  display: block;
  padding: var(--spacing-2) var(--spacing-3);
}

._selectWithLabel_npm11_922 {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-left: var(--spacing-2);
}

._selectWithLabel_npm11_922 > label {
  font-size: var(--text-sm);
}

._selectWithLabel_npm11_922 ._selectTrigger_npm11_267 {
  border: 1px solid var(--baseBorder);
}

._toolbarTitleMode_npm11_936 {
  font-size: var(--text-sm);
  margin-left: var(--spacing-2);
}

._imageControlWrapperResizing_npm11_942 {
  touch-action: none;
}

._imageResizer_npm11_946 {
  display: block;
  width: 7px;
  height: 7px;
  position: absolute;
  background-color: var(--accentText);
  border: 1px solid var(--baseBg);
}

._imageResizer_npm11_946._imageResizerN_npm11_955 {
  top: -6px;
  left: 48%;
  cursor: n-resize;
}

._imageResizer_npm11_946._imageResizerNe_npm11_961 {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

._imageResizer_npm11_946._imageResizerE_npm11_967 {
  bottom: 48%;
  right: -6px;
  cursor: e-resize;
}

._imageResizer_npm11_946._imageResizerSe_npm11_973 {
  bottom: -2px;
  right: -6px;
  cursor: nwse-resize;
}

._imageResizer_npm11_946._imageResizerS_npm11_973 {
  bottom: -2px;
  left: 48%;
  cursor: s-resize;
}

._imageResizer_npm11_946._imageResizerSw_npm11_985 {
  bottom: -2px;
  left: -6px;
  cursor: sw-resize;
}

._imageResizer_npm11_946._imageResizerW_npm11_991 {
  bottom: 48%;
  left: -6px;
  cursor: w-resize;
}

._imageResizer_npm11_946._imageResizerNw_npm11_997 {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

._placeholder_npm11_1003 {
  color: var(--baseSolid);
  overflow: hidden;
  position: absolute;
  top: 0;
  padding: var(--spacing-3);
  text-overflow: ellipsis;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  white-space: nowrap;
  display: inline-block;
  pointer-events: none;
}

._rootContentEditableWrapper_npm11_1016 {
  position: relative;
}
:root,
._light-theme_13nbk_1 {
  --blue1: hsl(206, 100%, 99.2%);
  --blue2: hsl(210, 100%, 98%);
  --blue3: hsl(209, 100%, 96.5%);
  --blue4: hsl(210, 98.8%, 94%);
  --blue5: hsl(209, 95%, 90.1%);
  --blue6: hsl(209, 81.2%, 84.5%);
  --blue7: hsl(208, 77.5%, 76.9%);
  --blue8: hsl(206, 81.9%, 65.3%);
  --blue9: hsl(206, 100%, 50%);
  --blue10: hsl(208, 100%, 47.3%);
  --blue11: hsl(211, 100%, 43.2%);
  --blue12: hsl(211, 100%, 15%);
}
._dark-theme_13nbk_1 {
  --blue1: hsl(212, 35%, 9.2%);
  --blue2: hsl(216, 50%, 11.8%);
  --blue3: hsl(214, 59.4%, 15.3%);
  --blue4: hsl(214, 65.8%, 17.9%);
  --blue5: hsl(213, 71.2%, 20.2%);
  --blue6: hsl(212, 77.4%, 23.1%);
  --blue7: hsl(211, 85.1%, 27.4%);
  --blue8: hsl(211, 89.7%, 34.1%);
  --blue9: hsl(206, 100%, 50%);
  --blue10: hsl(209, 100%, 60.6%);
  --blue11: hsl(210, 100%, 66.1%);
  --blue12: hsl(206, 98%, 95.8%);
}
:root,
._light-theme_13nbk_1 {
  --slate1: hsl(206, 30%, 98.8%);
  --slate2: hsl(210, 16.7%, 97.6%);
  --slate3: hsl(209, 13.3%, 95.3%);
  --slate4: hsl(209, 12.2%, 93.2%);
  --slate5: hsl(208, 11.7%, 91.1%);
  --slate6: hsl(208, 11.3%, 88.9%);
  --slate7: hsl(207, 11.1%, 85.9%);
  --slate8: hsl(205, 10.7%, 78%);
  --slate9: hsl(206, 6%, 56.1%);
  --slate10: hsl(206, 5.8%, 52.3%);
  --slate11: hsl(206, 6%, 43.5%);
  --slate12: hsl(206, 24%, 9%);
}
._dark-theme_13nbk_1 {
  --slate1: hsl(200, 7%, 8.8%);
  --slate2: hsl(195, 7.1%, 11%);
  --slate3: hsl(197, 6.8%, 13.6%);
  --slate4: hsl(198, 6.6%, 15.8%);
  --slate5: hsl(199, 6.4%, 17.9%);
  --slate6: hsl(201, 6.2%, 20.5%);
  --slate7: hsl(203, 6%, 24.3%);
  --slate8: hsl(207, 5.6%, 31.6%);
  --slate9: hsl(206, 6%, 43.9%);
  --slate10: hsl(206, 5.2%, 49.5%);
  --slate11: hsl(206, 6%, 63%);
  --slate12: hsl(210, 6%, 93%);
}
:root,
._light-theme_13nbk_1 {
  --grass1: hsl(116, 50%, 98.9%);
  --grass2: hsl(120, 60%, 97.1%);
  --grass3: hsl(120, 53.6%, 94.8%);
  --grass4: hsl(121, 47.5%, 91.4%);
  --grass5: hsl(122, 42.6%, 86.5%);
  --grass6: hsl(124, 39%, 79.7%);
  --grass7: hsl(126, 37.1%, 70.2%);
  --grass8: hsl(131, 38.1%, 56.3%);
  --grass9: hsl(131, 41%, 46.5%);
  --grass10: hsl(132, 43.1%, 42.2%);
  --grass11: hsl(133, 50%, 32.5%);
  --grass12: hsl(130, 30%, 14.9%);
}
:root,
._light-theme_13nbk_1 {
  --cyan1: hsl(185, 60%, 98.7%);
  --cyan2: hsl(185, 73.3%, 97.1%);
  --cyan3: hsl(186, 70.2%, 94.4%);
  --cyan4: hsl(186, 63.8%, 90.6%);
  --cyan5: hsl(187, 58.3%, 85.4%);
  --cyan6: hsl(188, 54.6%, 78.4%);
  --cyan7: hsl(189, 53.7%, 68.7%);
  --cyan8: hsl(189, 60.3%, 52.5%);
  --cyan9: hsl(190, 95%, 39%);
  --cyan10: hsl(191, 91.2%, 36.8%);
  --cyan11: hsl(192, 85%, 31%);
  --cyan12: hsl(192, 88%, 12.5%);
}
:root,
._light-theme_13nbk_1 {
  --amber1: hsl(39, 70%, 99%);
  --amber2: hsl(40, 100%, 96.5%);
  --amber3: hsl(44, 100%, 91.7%);
  --amber4: hsl(43, 100%, 86.8%);
  --amber5: hsl(42, 100%, 81.8%);
  --amber6: hsl(38, 99.7%, 76.3%);
  --amber7: hsl(36, 86.1%, 67.1%);
  --amber8: hsl(35, 85.2%, 55.1%);
  --amber9: hsl(39, 100%, 57%);
  --amber10: hsl(35, 100%, 55.5%);
  --amber11: hsl(30, 100%, 34%);
  --amber12: hsl(20, 80%, 17%);
}
:root,
._light-theme_13nbk_1 {
  --red1: hsl(359, 100%, 99.4%);
  --red2: hsl(359, 100%, 98.6%);
  --red3: hsl(360, 100%, 96.8%);
  --red4: hsl(360, 97.9%, 94.8%);
  --red5: hsl(360, 90.2%, 91.9%);
  --red6: hsl(360, 81.7%, 87.8%);
  --red7: hsl(359, 74.2%, 81.7%);
  --red8: hsl(359, 69.5%, 74.3%);
  --red9: hsl(358, 75%, 59%);
  --red10: hsl(358, 69.4%, 55.2%);
  --red11: hsl(358, 65%, 48.7%);
  --red12: hsl(354, 50%, 14.6%);
}
._bold_13nbk_10 {
  font-weight: bold;
}
._italic_13nbk_14 {
  font-style: italic;
}
._underline_13nbk_18 {
  text-decoration: underline;
}
._bold_13nbk_10 {
  font-weight: 700;
}
._italic_13nbk_14 {
  font-style: italic;
}
._underline_13nbk_18 {
  text-decoration: underline;
}
._strikethrough_13nbk_34 {
  text-decoration: line-through;
}
._underlineStrikethrough_13nbk_38 {
  text-decoration: underline line-through;
}
._subscript_13nbk_42 {
  font-size: 0.8em;
  vertical-align: sub !important;
}
._superscript_13nbk_47 {
  font-size: 0.8em;
  vertical-align: super;
}
._code_13nbk_52 {
  background-color: var(--baseBg);
  padding: 1px 0.25rem;
  font-family: var(--font-mono);
  font-size: 94%;
}
._nestedListItem_13nbk_59 {
  list-style: none;
}
._admonitionDanger_13nbk_63,
._admonitionInfo_13nbk_63,
._admonitionNote_13nbk_63,
._admonitionTip_13nbk_63,
._admonitionCaution_13nbk_63 {
  padding: var(--spacing-2);
  margin-top: var(--spacing-2);
  margin-bottom: var(--spacing-2);
  border-left: 3px solid var(--admonitionBorder);
  background-color: var(--admonitionBg);
}
._admonitionInfo_13nbk_63 {
  --admonitionBorder: var(--admonitionInfoBorder);
  --admonitionBg: var(--admonitionInfoBg);
}
._admonitionTip_13nbk_63 {
  --admonitionBorder: var(--admonitionTipBorder);
  --admonitionBg: var(--admonitionTipBg);
}
._admonitionCaution_13nbk_63 {
  --admonitionBorder: var(--admonitionCautionBorder);
  --admonitionBg: var(--admonitionCautionBg);
}
._admonitionDanger_13nbk_63 {
  --admonitionBorder: var(--admonitionDangerBorder);
  --admonitionBg: var(--admonitionDangerBg);
}
._admonitionNote_13nbk_63 {
  --admonitionBorder: var(--admonitionNoteBorder);
  --admonitionBg: var(--admonitionNoteBg);
}
:root {
  --diff-background-color: initial;
  --diff-text-color: initial;
  --diff-font-family: Consolas, Courier, monospace;
  --diff-selection-background-color: #b3d7ff;
  --diff-selection-text-color: var(--diff-text-color);
  --diff-gutter-insert-background-color: #d6fedb;
  --diff-gutter-insert-text-color: var(--diff-text-color);
  --diff-gutter-delete-background-color: #fadde0;
  --diff-gutter-delete-text-color: var(--diff-text-color);
  --diff-gutter-selected-background-color: #fffce0;
  --diff-gutter-selected-text-color: var(--diff-text-color);
  --diff-code-insert-background-color: #eaffee;
  --diff-code-insert-text-color: var(--diff-text-color);
  --diff-code-delete-background-color: #fdeff0;
  --diff-code-delete-text-color: var(--diff-text-color);
  --diff-code-insert-edit-background-color: #c0dc91;
  --diff-code-insert-edit-text-color: var(--diff-text-color);
  --diff-code-delete-edit-background-color: #f39ea2;
  --diff-code-delete-edit-text-color: var(--diff-text-color);
  --diff-code-selected-background-color: #fffce0;
  --diff-code-selected-text-color: var(--diff-text-color);
  --diff-omit-gutter-line-color: #cb2a1d;
}
.diff {
  background-color: var(--diff-background-color);
  border-collapse: collapse;
  color: var(--diff-text-color);
  table-layout: fixed;
  width: 100%;
}
.diff::-moz-selection {
  background-color: #b3d7ff;
  background-color: var(--diff-selection-background-color);
  color: var(--diff-text-color);
  color: var(--diff-selection-text-color);
}
.diff::selection {
  background-color: #b3d7ff;
  background-color: var(--diff-selection-background-color);
  color: var(--diff-text-color);
  color: var(--diff-selection-text-color);
}
.diff td {
  padding-bottom: 0;
  padding-top: 0;
  vertical-align: top;
}
.diff-line {
  font-family: Consolas, Courier, monospace;
  font-family: var(--diff-font-family);
  line-height: 1.5;
}
.diff-gutter > a {
  color: inherit;
  display: block;
}
.diff-gutter {
  cursor: pointer;
  padding: 0 1ch;
  text-align: right;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.diff-gutter-insert {
  background-color: #d6fedb;
  background-color: var(--diff-gutter-insert-background-color);
  color: var(--diff-text-color);
  color: var(--diff-gutter-insert-text-color);
}
.diff-gutter-delete {
  background-color: #fadde0;
  background-color: var(--diff-gutter-delete-background-color);
  color: var(--diff-text-color);
  color: var(--diff-gutter-delete-text-color);
}
.diff-gutter-omit {
  cursor: default;
}
.diff-gutter-selected {
  background-color: #fffce0;
  background-color: var(--diff-gutter-selected-background-color);
  color: var(--diff-text-color);
  color: var(--diff-gutter-selected-text-color);
}
.diff-code {
  word-wrap: break-word;
  padding: 0 0 0 0.5em;
  white-space: pre-wrap;
  word-break: break-all;
}
.diff-code-edit {
  color: inherit;
  display: inline-block;
}
.diff-code-insert {
  background-color: #eaffee;
  background-color: var(--diff-code-insert-background-color);
  color: var(--diff-text-color);
  color: var(--diff-code-insert-text-color);
}
.diff-code-insert .diff-code-edit {
  background-color: #c0dc91;
  background-color: var(--diff-code-insert-edit-background-color);
  color: var(--diff-text-color);
  color: var(--diff-code-insert-edit-text-color);
}
.diff-code-delete {
  background-color: #fdeff0;
  background-color: var(--diff-code-delete-background-color);
  color: var(--diff-text-color);
  color: var(--diff-code-delete-text-color);
}
.diff-code-delete .diff-code-edit {
  background-color: #f39ea2;
  background-color: var(--diff-code-delete-edit-background-color);
  color: var(--diff-text-color);
  color: var(--diff-code-delete-edit-text-color);
}
.diff-code-selected {
  background-color: #fffce0;
  background-color: var(--diff-code-selected-background-color);
  color: var(--diff-text-color);
  color: var(--diff-code-selected-text-color);
}
.diff-widget-content {
  vertical-align: top;
}
.diff-gutter-col {
  width: 7ch;
}
.diff-gutter-omit {
  height: 0;
}
.diff-gutter-omit:before {
  background-color: #cb2a1d;
  background-color: var(--diff-omit-gutter-line-color);
  content: " ";
  display: block;
  height: 100%;
  margin-left: 4.6ch;
  overflow: hidden;
  white-space: pre;
  width: 2px;
}
.diff-decoration {
  line-height: 1.5;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.diff-decoration-content {
  font-family: Consolas, Courier, monospace;
  font-family: var(--diff-font-family);
  padding: 0;
}
