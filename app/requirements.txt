aiofiles==23.2.1
aiohttp==3.8.5
aiosignal==1.3.1
annotated-types==0.5.0
appdirs==1.4.4
asgiref==3.7.2
async-timeout==4.0.3
attrs==23.1.0
beautifulsoup4==4.12.2
blinker==1.5
CacheControl==0.13.1
cachetools==5.3.1
certifi==2023.7.22
cffi==1.15.1
charset-normalizer==3.2.0
click==8.1.7
cloudevents==1.9.0
cmake==3.27.4.1
cryptography==41.0.3
dataclasses-json==0.5.14
decorator==5.1.1
deprecation==2.1.0
filelock==3.12.3
firebase-admin==6.2.0
Flask==2.2.5
Flask-Cors==4.0.0
frozenlist==1.4.0
fs==2.4.16
fsspec==2023.9.0
functions-framework==3.4.0
gcsfs==2023.9.0
google-api-core==2.11.1
google-api-python-client==2.97.0
google-auth==2.22.0
google-auth-httplib2==0.1.0
google-auth-oauthlib==1.0.0
google-cloud==0.34.0
google-cloud-core==2.3.3
google-cloud-firestore==2.11.1
google-cloud-storage==2.10.0
google-crc32c==1.5.0
google-events==0.10.0
google-resumable-media==2.5.0
googleapis-common-protos==1.60.0
greenlet==2.0.2
grpcio==1.57.0
grpcio-status==1.57.0
gunicorn==20.1.0
h11==0.14.0
h2==4.1.0
hpack==4.0.0
httplib2==0.22.0
huggingface-hub==0.16.4
hypercorn==0.14.4
hyperframe==6.0.1
idna==3.4
itsdangerous==2.1.2
Jinja2==3.1.2
joblib==1.3.2
langchain==0.0.281
langsmith==0.0.33
lit==16.0.6
llama-index==0.8.20
MarkupSafe==2.1.3
marshmallow==3.20.1
mpmath==1.3.0
msgpack==1.0.5
multidict==6.0.4
mypy-extensions==1.0.0
nest-asyncio==1.5.7
networkx==3.1
nltk==3.8.1
numexpr==2.8.5
numpy==1.25.2
nvidia-cublas-cu11==11.10.3.66
nvidia-cuda-cupti-cu11==11.7.101
nvidia-cuda-nvrtc-cu11==11.7.99
nvidia-cuda-runtime-cu11==11.7.99
nvidia-cudnn-cu11==8.5.0.96
nvidia-cufft-cu11==10.9.0.58
nvidia-curand-cu11==10.2.10.91
nvidia-cusolver-cu11==11.4.0.1
nvidia-cusparse-cu11==*********
nvidia-nccl-cu11==2.14.3
nvidia-nvtx-cu11==11.7.91
oauthlib==3.2.2
openai==0.28.0
packaging==23.1
pandas==2.1.0
Pillow==10.0.0
priority==2.0.0
proto-plus==1.22.3
protobuf==4.24.2
pyasn1==0.5.0
pyasn1-modules==0.3.0
pycparser==2.21
pydantic==2.3.0
pydantic_core==2.6.3
PyJWT==2.8.0
pyparsing==3.1.1
python-dateutil==2.8.2
pytz==2023.3.post1
PyYAML==6.0.1
quart==0.18.4
regex==2023.8.8
requests==2.31.0
requests-oauthlib==1.3.1
rsa==4.9
safetensors==0.3.3
scikit-learn==1.3.0
scipy==1.11.2
sentence-transformers==2.2.2
sentencepiece==0.1.99
six==1.16.0
soupsieve==2.5
SQLAlchemy==2.0.20
sympy==1.12
tenacity==8.2.3
threadpoolctl==3.2.0
tiktoken==0.4.0
tokenizers==0.13.3
torch==2.0.1
torchvision==0.15.2
tqdm==4.66.1
transformers==4.33.1
triton==2.0.0
typing-inspect==0.9.0
typing_extensions==4.7.1
tzdata==2023.3
ua-parser==0.18.0
uritemplate==4.1.1
urllib3==1.26.16
user-agents==2.2.0
watchdog==3.0.0
Werkzeug==2.3.7
WooCommerce==3.0.0
wsproto==1.2.0
yarl==1.9.2
