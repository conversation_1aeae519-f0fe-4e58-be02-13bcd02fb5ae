import Container from "@/components/general/Container";
import { SidebarNav } from "@/components/general/sidebar-nav";
import { Separator } from "@/components/ui/separator";
import { Toaster } from "@/components/ui/toaster";
import React from "react";

const sidebarNavItems = [
  {
    title: "AI General Settings",
    href: "/hub/settings",
  },
  {
    title: "Workflows",
    href: "/hub/settings/workflows",
  },
  {
    title: "WordPress/WooCommerce",
    href: "/hub/settings/wordpress",
  },
  {
    title: "Chat Widget",
    href: "/hub/settings/widget",
  },
  {
    title: "Products",
    href: "/hub/settings/products",
  },
  {
    title: "Widget Welcome Messages",
    href: "/hub/settings/welcome-messages",
  },
  {
    title: "Data Categories",
    href: "/hub/settings/data-categories",
  },
  {
    title: "Sitemaps",
    href: "/hub/settings/sitemaps",
  },
  {
    title: "Website Config",
    href: "/hub/settings/website-config",
  },
  {
    title: "Website Data",
    href: "/hub/settings/website-data",
  },
  {
    title: "GraphQL Data",
    href: "/hub/settings/graphql",
  },
];

export default function ConnectorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <Container className="my-12 border rounded-lg shadow">
        <div className="hidden p-10 pb-16 space-y-6 md:block">
          <div className="space-y-0.5">
            <h2 className="text-2xl font-bold tracking-tight">Settings</h2>
            <p className="text-muted-foreground">
              Configure your AI settings, data connectors, website widget, and
              more.
            </p>
          </div>
          <Separator className="my-6" />
          <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
            <aside className="-mx-4 lg:w-1/5">
              <SidebarNav items={sidebarNavItems} />
            </aside>
            <div className="flex-1 ">{children}</div>
          </div>
        </div>
      </Container>
      <Toaster />
    </>
  );
}
