import { Message } from "@/models/message";
import { useMemo } from "react";

export interface FirestoreDate {
  seconds: number;
  nanoseconds: number;
}

export const convertDate = (date: FirestoreDate) => {
  return new Date(date.seconds * 1000);
};

export const orderMessagesByDate = (messages: Message[]) => {
  return messages.sort((a, b) => {
    const aDate = convertDate(a.createdAt as FirestoreDate);
    const bDate = convertDate(b.createdAt as FirestoreDate);
    return aDate.getTime() - bDate.getTime();
  });
};


export function useLastLinkMessage(messages: Message[]) {
  return useMemo(() => {
    const lastLinkMessage = messages
      .sort((a, b) => {
        const aDate = convertDate(a.createdAt as FirestoreDate);
        const bDate = convertDate(b.createdAt as FirestoreDate);
        return bDate.getTime() - aDate.getTime();
      })
      .find((message) => message.role === "links");

    if (lastLinkMessage) {
      return lastLinkMessage.message;
    }
  }, [messages]);
}