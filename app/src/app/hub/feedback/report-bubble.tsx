import Bubble from "@/components/chat/bubble";
import { Report } from "@/models/report";

export default function ReportBubble({
  document,
  role,
  short = true,
}: {
  document: Report;
  role: "user" | "assistant";
  short?: boolean;
}) {
  return (
    <Bubble
      chatSessionId="1"
      message={{
        createdAt: document.createdAt.toDate(),
        message: role == "user" ? document.previousMessage : document.message,
        id: "1",
        role: role,
        reported: true,
      }}
      tenantId="1"
      short={short}
      previousMessage={null}
      userName="User"
    />
  );
}
