import React, { useState, useEffect } from "react";
import "./loader.css";

type Props = {
  author: string;
};

export default function LoadingBubble({ author }: Props) {
  const [showProcessing, setShowProcessing] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowProcessing(true);
    }, 6000); // 6 seconds

    // Cleanup function to clear the timer when the component is unmounted
    return () => {
      clearTimeout(timer);
    };
  }, []); // Empty dependency array to run the effect only once

  return (
    <div className="flex flex-1 gap-3 my-4 text-sm text-gray-600">
      <span className="relative flex shrink-0 overflow-hidden rounded-full w-5 h-5">
        <div className="rounded-full bg-gray-100 border p-1">
          <svg
            stroke="none"
            fill="black"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            aria-hidden="true"
            height="10"
            width="10"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z"
            ></path>
          </svg>
        </div>
      </span>
      <p className="w-full leading-relaxed">
        <span className="flex items-center gap-2 font-bold text-muted-foreground text-xs">
          {author}{" "}
        </span>
        <div className="chat-message text-foreground text-xs flex items-center gap-2">
          <div className="lds-ellipsis">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          {showProcessing && <small>Give me a quick second...</small>}
        </div>
      </p>
    </div>
  );
}
