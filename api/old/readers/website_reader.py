from typing import List, Dict, Any
from firebase_admin import firestore
from llama_index.core.readers.base import BaseReader
from llama_index.core.schema import Document
import hashlib

class Page:
    def __init__(self, id: str, categoryId: str, content: str, title: str,
                 url: str):
        self.id = id
        self.categoryId = categoryId
        self.content = content
        self.title = title
        self.url = url


class WebsiteReader(BaseReader):

    def __init__(self, db: Any, tenant_id: str, category_id: str = None):
        self.db = db
        self.tenant_id = tenant_id
        self.category_id = category_id

    def load_data(self, *args: Any, **load_kwargs: Any) -> List[Document]:
        pages = self.get_pages()
        
        # Createdocuments
        report_data = []
        for page in pages:
            
            the_content = f"""
            Page Title: {page["title"]}
            Page URL: {page["url"]}
            
            Page Content
            ===========================
            {page["content"]}
            
            """
            
            report_data.append(
                Document(text=the_content, doc_id=page['id'], doc_hash=hashlib.md5(the_content.encode()).hexdigest())
            )

        return report_data


    def get_pages(self) -> List[Page]:
        pages_ref = (self.db.collection("tenants")
                       .document(self.tenant_id)
                       .collection("websitePages")
                       .where("categoryId", "==", self.category_id) if self.category_id else None
                       )

        pages = pages_ref.get()
        reports = []

        for doc in pages:
            try:
                reports.append(doc.to_dict())
            except:
                pass

        return reports
