// aiAgent.ts
import { OpenAI } from "openai";

export function createAIEngine(
  organization: string = 'org-QCtXQY9FA2zXS2f9PuyKZ6h9', 
  apiKey: string = "***************************************************", 
  model: string = "gpt-3.5-turbo") {
    const openai = new OpenAI({
        organization: organization,
        apiKey: apiKey
    });


    async function createCompletion(
      userPrompt: string, 
      systemPrompt: string,
        ): Promise<string> {
        const completion = await openai.chat.completions.create({
            messages: [{ role: "system", content: systemPrompt },
            { role: "user", content: userPrompt }],
            model: model,
        });
        return completion.choices[0].message.content || "";
    }

    return {
        createCompletion
    };
}
