from openai import OpenAI
from typing import List

class LLMService:
  
  def __init__(self):
    
    self.openai = OpenAI(api_key="********************************************************")
    
    
  def chat(self, message: str, system_messages: List[str], model = "gpt-4o") -> str:
    # Should be {"role": "system", "content": "Hello, how can I help you today?"} in format
    system_messages = [{"role": "system", "content": message} for message in system_messages]
    
    completion = self.openai.chat.completions.create(
      messages=[
        *system_messages,
        {"role": "user", "content": message}
      ],
      model=model
    )
    
    str_response = completion.choices[0].message.content
    return str_response
    
    