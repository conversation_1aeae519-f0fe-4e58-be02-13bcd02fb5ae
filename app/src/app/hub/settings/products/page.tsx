"use client";
import GenericConfig, {
  FieldDetails,
} from "@/components/Dashboard/Widgets/GenericConfig";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import React from "react";
import { z } from "zod";

type Props = {};

export default function ProductsListPage({}: Props) {
  const CONFIG_DETAILS: FieldDetails = {
    productList: {
      label: "Products",
      description: "The full list of products sold",
      formSchema: z.string(), // Assuming color is input as a string (e.g., HEX or RGB).
    },
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Products</CardTitle>
        <CardDescription>
          This is a list of all the products sold. This is a critical feature of
          the hallucination detection system.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        <GenericConfig
          fieldDetails={CONFIG_DETAILS}
          collectionString="productsList"
          buttonName="Save Configuration"
        />
      </CardContent>
    </Card>
  );
}
