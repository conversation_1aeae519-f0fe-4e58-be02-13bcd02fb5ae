"use client";
import React, { use, useEffect, useRef, useState } from "react";
import { auth, db, firebaseApp } from "@/services/firebase.config";
import { useCollection, useDocumentData } from "react-firebase-hooks/firestore";
import {
  collection,
  doc,
  getDoc,
  getDocs,
  getFirestore,
  onSnapshot,
  query,
  where,
  updateDoc,
} from "firebase/firestore";
import DashboardContainer from "@/components/Dashboard/DashboardContainer";
import { Table } from "react-daisyui";
import { BlockNoteView, useBlockNote } from "@blocknote/react";
import "@blocknote/core/style.css";
import Link from "next/link";
import { AiOutlineLeftCircle } from "react-icons/ai";
import { useTheme } from "next-themes";
import useDashboardStore from "@/stores/dashboard.store";
import DocumentNavigator from "@/components/Documents/DocumentNavigator";
import { Button } from "@/components/ui/button";
import { Toaster } from "@/components/ui/toaster";
import { toast } from "@/components/ui/use-toast";

export default function DocumentPage({ params: { documentId, clientName } }) {
  const { theme } = useTheme();

  const [documentData] = clientName
    ? useDocumentData(doc(db, "tenants", clientName, "documents", documentId), {
      snapshotListenOptions: { includeMetadataChanges: true },
    })
    : [null];
  const [title, setTitle] = useState(null);
  const [editingTitle, setEditingTitle] = useState(false);

  const [markdown, setMarkdown] = useState(null);

  const docRef = doc(db, "tenants", clientName, "documents", documentId);

  const save = async () => {
    if (markdown && title) {
      await updateDoc(docRef, {
        data: markdown,
        title: title,
        lastModified: new Date().toISOString(),
      });
    }
  };

  const editor = useBlockNote({
    theme: theme,
    onEditorContentChange: (editor) => {
      const saveBlocksAsMarkdown = async () => {
        const markdown = await editor.blocksToMarkdown(editor.topLevelBlocks);
        setMarkdown(markdown);
      };
      saveBlocksAsMarkdown();
    },
  });

  useEffect(() => {
    if (documentData) {
      setMarkdown(documentData.data);
    }
  }, [documentData]);

  const [loadOnce, setLoadOnce] = useState(false);

  useEffect(() => {
    if (editor && markdown && !loadOnce) {
      const getBlocks = async () => {
        const blocks = await editor.markdownToBlocks(markdown);
        editor.replaceBlocks(editor.topLevelBlocks, blocks);
        setLoadOnce(true);
      };
      getBlocks();
    }
  }, [editor, markdown]);

  useEffect(() => {
    if (documentData) {
      setTitle(documentData.title);
    }
  }, [documentData]);

  const titleRef = useRef(null);

  return (
    <div className="grid grid-cols-12 gap-4">
      <div className="col-span-12 border-r md:col-span-2">
        <DocumentNavigator clientName={clientName} currentPageId={documentId} />
      </div>
      <div className="col-span-12 md:col-span-10 pb-[100px]">
        <h1
          className="my-4 text-4xl pl-[2.7rem] font-bold"
          onClick={() => {
            setEditingTitle(true);
            setTimeout(() => {
              titleRef.current.focus();
            }, 100);
          }}
        >
          {editingTitle ? (
            <input
              className={`w-full p-2 `}
              type="text"
              ref={titleRef}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              onBlur={() => {
                setEditingTitle(false);
              }}
            />
          ) : (
            <span
              className={`${title == "New Document" ? "text-muted italic" : "text-primary"
                } inline-block p-2`}
            >
              {title}
            </span>
          )}
        </h1>

        <BlockNoteView className="h-full" editor={editor} />
      </div>
      <div className="fixed inset-x-0 bottom-0 bg-background border-t-[1px] border-muted-background p-4 flex justify-end">
        <Button
          className="mr-2"
          onClick={() => {
            save();
            toast({
              title: "Saved",
              description: "Your document has been saved.",
            });
          }}
        >
          Save
        </Button>
      </div>
      <Toaster />
    </div>
  );
}
