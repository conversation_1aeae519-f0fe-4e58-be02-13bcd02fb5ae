"use client";
import GenericConfig, {
  FieldDetails,
} from "@/components/Dashboard/Widgets/GenericConfig";
import React from "react";
import { z } from "zod";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import ConfigTabs from "@/components/advanced-ui/firebase-datatable/welcome-tabs";
import FirebaseDataTable, {
  ConfigItem,
} from "@/components/advanced-ui/firebase-datatable/firebase-datatable";
import { useStore } from "@/stores/use-store";
import useDashboardStore from "@/stores/dashboard.store";

type Props = {};

export default function WelcomeMessagesPage({}: Props) {
  const CONFIG_DETAILS: FieldDetails = {
    author: {
      label: "Author",
      description: "The name of the author of the messages",
      formSchema: z.string(), // Assuming color is input as a string (e.g., HEX or RGB).
    },
    intDelayInSeconds: {
      label: "Delay in seconds",
      description:
        "The wordpress username (should be an application user with read access to the wordpress site)",
      formSchema: z.number(),
    },
    behaviour: {
      label: "Behaviour",
      description: "The behaviour of the messages",
      formSchema: z.string(), // Assuming color is input as a string (e.g., HEX or RGB).
      selectValues: ["Simulate chat", "All at once"],
      defaultValue: "Simulate chat",
    },
  };

  const clientName = useStore(
    useDashboardStore,
    (state) => state.selectedClientId
  );

  return (
    clientName && (
      <div>
        <ConfigTabs
          tabs={[
            {
              id: "messages",
              label: "Welcome messages",
              description:
                "These message will show up like actual chat messages when the user first starts the chat.",
              content: (
                <FirebaseDataTable
                  firebasePath={["tenants", clientName, "welcomeMessages"]}
                  term="Welcome Message"
                  formConfig={{
                    order: {
                      key: "order",
                      label: "Order",
                      type: "text",
                      defaultValue: "",
                    } as ConfigItem,
                    description: {
                      key: "description",
                      label: "Description",
                      type: "textarea",
                      defaultValue: "",
                    } as ConfigItem,
                  }}
                />
              ),
            },
            {
              id: "config",
              label: "Configuration",
              description:
                "How you want those messages to behave on the chat widget",
              content: (
                <GenericConfig
                  fieldDetails={CONFIG_DETAILS}
                  collectionString="welcomeMessagesConfig"
                  buttonName="Save Configuration"
                />
              ),
            },
          ]}
        />
      </div>
    )
  );
}
