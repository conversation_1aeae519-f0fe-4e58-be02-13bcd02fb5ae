# from firebase_admin import firestore
from typing import Any
from firebase_admin import firestore

from models.tenant import Tenant

from google.cloud.firestore import Client


class CategoryRepository:

  database: Client
    
  def __init__(self, database: Client):
    self.database = database
    
  def get_categories(self, tenant_id: str):
  
    categories_ref = (self.database
                      .collection('tenants')
                      .document(tenant_id)
                      .collection("reportCategories"))  
    
    categories = categories_ref.get()
    categories_data = {}
    for category in categories:
      categories_data[category.id] = category.to_dict()
      categories_data[category.id]["id"] = category.id
      
    return categories_data
  