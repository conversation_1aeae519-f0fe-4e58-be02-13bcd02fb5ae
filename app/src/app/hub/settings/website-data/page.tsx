"use client";
import FirebaseDataTable, {
  ConfigItem,
} from "@/components/advanced-ui/firebase-datatable/firebase-datatable";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import useDashboardStore from "@/stores/dashboard.store";
import { useStore } from "@/stores/use-store";
import { collection, doc, getDocs, query, updateDoc } from "firebase/firestore";
import React, { useEffect, useMemo, useState } from "react";
import { db } from "@/services/firebase.config";
import { FeedbackCategory } from "@/models/feedbackCategory";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { useFirestoreSync } from "@/lib/firebase-hooks";
import { Sitemap } from "@/models/sitemap";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select";

type Props = {};

type SitemapPage = {
  id: string,
  title: string,
  url: string,
  content: string,
  categoryId: string,
  categoryName: string,
  sitemapId: string,
}

export default function ContentValidatorPage({ }: Props) {
  const clientName = useDashboardStore((state) => state.selectedClient)?.value;
  const tabsRef = React.createRef<HTMLDivElement>();

  const queryConstraints = useMemo(() => [], []);

  const {
    paginatedDocuments: categories,
  } = useFirestoreSync<FeedbackCategory>(
    `tenants/${clientName}/reportCategories`,
    queryConstraints
  );

  const {
    paginatedDocuments: sitemaps,
  } = useFirestoreSync<Sitemap>(
    `tenants/${clientName}/sitemaps`,
    queryConstraints
  );


  const {
    paginatedDocuments: websitePages,
  } = useFirestoreSync<SitemapPage>(
    `tenants/${clientName}/websitePages`,
    queryConstraints,
    { perPage: 1000 }
  );

  const updatePageCategory = async (page: SitemapPage, categoryId: string) => {
    updateDoc(doc(db, `tenants/${clientName}/websitePages/${page.id}`), {
      categoryId: categoryId,
    });
  };

  const updateMultiplePageCategories = async (pages: SitemapPage[], categoryId: string) => {
    pages.forEach((page) => {
      updatePageCategory(page, categoryId);
    });
  }

  return (
    <div>
      {sitemaps && sitemaps.length > 0 && (
        <Tabs ref={tabsRef} defaultValue={sitemaps[0].id} className="">
          <TabsList className="flex w-full justify-between">
            {sitemaps &&
              sitemaps.map((sitemap) => (
                <TabsTrigger value={sitemap.id} key={sitemap.id} className="flex-1">
                  {sitemap.name}
                </TabsTrigger>
              ))}
          </TabsList>
          {sitemaps &&
            sitemaps.map((sitemap) => (
              <TabsContent value={sitemap.id} key={sitemap.id}>
                <Card>
                  <CardHeader>
                    <CardTitle>{sitemap.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-end">


                      <Select
                        onValueChange={(value: string) => {
                          confirm('Are you sure you want to update all pages in this sitemap?') &&
                            updateMultiplePageCategories(websitePages.filter((page) => page.sitemapId === sitemap.id), value);
                        }}
                        defaultValue="0"
                      >
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Select an option" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Category</SelectLabel>
                            {categories.map((option) => (
                              <SelectItem key={option.id} value={option.id}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>

                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Page Title</TableHead>
                          <TableHead>Page URL</TableHead>
                          <TableHead>Category</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {websitePages
                          .filter((page) => page.sitemapId === sitemap.id)
                          .map((page) => (
                            <TableRow key={page.id}>
                              <TableCell>{page.title}</TableCell>
                              <TableCell>{page.url}</TableCell>
                              <TableCell>

                                <Select onValueChange={(value: string) => {
                                  updatePageCategory(page, value)
                                }}
                                  defaultValue={page.categoryId}>
                                  <SelectTrigger className={cn("w-[180px]", "")}>
                                    <SelectValue placeholder="Select an option" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectGroup>
                                      <SelectLabel>Category</SelectLabel>
                                      {categories.map((option) => (
                                        <SelectItem key={option.id} value={option.id}>
                                          {option.label}
                                        </SelectItem>
                                      ))}
                                    </SelectGroup>
                                  </SelectContent>
                                </Select>

                              </TableCell>
                            </TableRow>
                          ))}

                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          {/* Other TabsContent components */}
        </Tabs>
      )}
    </div>
  );
}