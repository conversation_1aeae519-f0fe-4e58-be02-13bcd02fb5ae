import React from 'react'

type Props = {
  data: {
    links: [{
      name: string,
      url: string,
    }]
  }
}

export default function LinkPreview({
  data
}: Props) {
  return (
    <div className="navlinks">
      <div className="text-xs text-gray-500 pb-1 font-bold">Quick Links</div>
      <ul className='grid grid-flow-col gap-2'>

        {
          data && data.links.map((link, index) => {
            const imageUrl = `/api/ogimage?url=${link.url}`
            return (
              <li>
                <a key={index} href={`${link.url}`} target="_blank" rel="noreferrer" className="flex flex-col justify-center items-center space-x-2 py-1">
                  <div className="rounded shadow-md overflow-hidden bg-white">
                    {
                      imageUrl && <img src={`${imageUrl}`} className="object-contain h-[60px]" alt={link.name} />
                    }

                    {!imageUrl &&
                      <div className="h-[60px] w-full bg-gray-200 flex items-center justify-center">
                        {
                          link.name && <span className="text-[21px] text-gray-600">{link.name[0]}</span>
                        }
                      </div>
                    }
                  </div>

                  <div className="text-xs mt-2 text-center text-ellipsis text-gray-500">{link.name}</div>
                </a>
              </li>
            )
          }
          )
        }

      </ul>
    </div>
  )
}