# services/TrainingService.py

from firebase_admin import firestore
from services.LLMService import LLMService
from services.LlamaIndexService import LlamaIndexService
from models.VectorDocument import VectorDocument
from models.GraphQLDocument import GraphQLDocument
from repositories.category_repository import CategoryRepository
from google.cloud.firestore import Client
from services.GenericGraphQLReader import GenericGraphQLReader


class TrainingService:

    def __init__(
        self,
        database: Client,
        llm_service: LLMService,
        llama_index_service: LlamaIndexService
    ):
        self.db = database
        self.llm_service = llm_service
        self.llama_index_service = llama_index_service

    def train_website(self, tenant_id: str):
        pages_ref = (
            self.db.collection("tenants")
            .document(tenant_id)
            .collection("websitePages")
            .where("categoryId", "!=", "")
        )

        documents = []

        pages = pages_ref.stream()
        for page in pages:
            page_data = page.to_dict()
            page_id = page.id

            vector_document = VectorDocument(
                title=page_data["title"],
                content=page_data["content"],
                categoryId=page_data.get("categoryId", ""),
                url=page_data["url"],
            )

            self.llama_index_service.add_document(
                tenant_id, vector_document, category_id=page_data.get("categoryId", ""))

    def train_knowledgebase(self, tenant_id: str):
        kb_ref = (
            self.db.collection("tenants")
            .document(tenant_id)
            .collection("documents")
            .where("categoryId", "!=", "")
        )

        documents = []

        kbs = kb_ref.get()
        for kb in kbs:
            kb_data = kb.to_dict()
            kb_id = kb.id

            vector_document = VectorDocument(
                title=kb_data["title"],
                content=kb_data["data"],
                categoryId=kb_data.get("categoryId", ""),
                url="",
            )

            self.llama_index_service.add_document(
                tenant_id, vector_document, category_id=kb_data.get("categoryId", ""))

    def train_graphql(self, tenant_id: str):
        gql_ref = (
            self.db.collection("tenants")
            .document(tenant_id)
            .collection("graphql")
        )

        documents = []

        gqls = gql_ref.get()
        for gql in gqls:
            gql_data = gql.to_dict()
            gql_id = gql.id

            query = gql_data["query"]
            endpoint = gql_data["endpoint"]

            # Initialize GenericGraphQLReader to fetch data
            reader = GenericGraphQLReader(endpoint, query)
            data = reader.load_data()
            
            # Loop on the data and create GraphQLDocuments for each entry
            for entry in data:
                graph_ql_document = GraphQLDocument(
                    title=gql_data["name"],
                    content=entry,
                    id=gql_id
                )

                self.llama_index_service.add_document(
                    tenant_id, graph_ql_document, graphql_id=gql_id)


    def train(self, tenant_id: str):
        # Reset the existing index for the tenant
        self.llama_index_service.reset_index(tenant_id)

        # Train different components
        self.train_graphql(tenant_id)
        self.train_website(tenant_id)
        self.train_knowledgebase(tenant_id)
