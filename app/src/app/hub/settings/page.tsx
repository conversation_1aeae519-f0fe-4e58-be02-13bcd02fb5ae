import Container from '@/components/general/Container'
import { SidebarNav } from '@/components/general/sidebar-nav'
import { Separator } from '@/components/ui/separator'
import React from 'react'
import { ProfileForm } from './profile-form'
import SystemMessages from '@/components/Wordpress/SystemMessages'

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import OpenAIConfig from '@/components/Dashboard/Widgets/OpenAIConfig'
import AnalyticsConfig from '@/components/Dashboard/Widgets/AnalyticsConfig'

export default function ConnectorPage() {
  return (
    <Tabs defaultValue="openai" className="">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="openai">OpenAI Configuration</TabsTrigger>
        <TabsTrigger value="account">System Messages</TabsTrigger>
        <TabsTrigger value="analytics">Analytics</TabsTrigger>
      </TabsList>
      <TabsContent value="openai">
        <Card>
          <CardHeader>
            <CardTitle>OpenAI Configuration</CardTitle>
            <CardDescription>
              This is where you can configure your OpenAI API key, and related settings.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <OpenAIConfig />
          </CardContent>
          {/* <CardFooter>
            <Button>Save changes</Button>
          </CardFooter> */}
        </Card>
      </TabsContent>
      <TabsContent value="account">
        <Card>
          <CardHeader>
            <CardTitle>System messages</CardTitle>
            <CardDescription>
              The system messages are used by the AI chatbot to direct behaviour
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <SystemMessages />
          </CardContent>
          {/* <CardFooter>
            <Button>Save changes</Button>
          </CardFooter> */}
        </Card>
      </TabsContent>
      <TabsContent value="analytics">
        <Card>
          <CardHeader>
            <CardTitle>Analytics Configuration</CardTitle>
            <CardDescription>
              Configure analytics tracking for your chatbot.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <AnalyticsConfig />
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
