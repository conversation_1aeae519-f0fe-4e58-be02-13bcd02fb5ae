"use client"
import React from 'react'
import DashboardNavigation from './DashboardNavigation'
import { Avatar } from 'react-daisyui'
import { Toaster } from '@/components/ui/toaster'

export default function DashboardContainer({children, whiteBg = false}) {
  return (
    <div className={`relative flex min-h-screen ${whiteBg ? 'bg-white' : 'bg-[radial-gradient(circle_at_top_right,_var(--tw-gradient-stops))] from-sky-400 to-white'}`}>
      <DashboardNavigation />
      <div className='flex flex-col flex-1 gap-4 p-4'>
        {children}
      </div>
      <Toaster />
    </div>
  )
}
