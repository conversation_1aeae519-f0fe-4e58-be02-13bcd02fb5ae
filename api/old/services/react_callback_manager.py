from typing import Any, Dict, Optional, List

from typing import Any, Dict, List, Optional, cast

from llama_index.core.callbacks.base import Callback<PERSON>anager
from llama_index.core.callbacks.schema import CBEventType, EventPayload
from llama_index.core.callbacks.base_handler import <PERSON><PERSON>all<PERSON><PERSON><PERSON><PERSON>
from typing import Callable
from services.openai_direct_service import OpenAIDirectService

class ReActCallbackManager(CallbackManager):
  
    
    def __init__(self, 
                 handlers: Optional[List[BaseCallbackHandler]] = None,
                 tenant_id: Optional[str] = None,
                 chat_session_id: Optional[str] = None,
                 writer: Optional[Callable[[str], None]] = None,
                 reset: Optional[Callable] = None,
                 ):
      
      super().__init__(handlers)
      self.tenant_id = tenant_id
      self.chat_session_id = chat_session_id
      self.writer = writer
      self.reset = reset
      self.openai_service = OpenAIDirectService()
    
    # def on_event_start(
    #     self,
    #     event_type: CBEventType,
    #     payload: Optional[Dict[str, Any]] = None,
    #     event_id: Optional[str] = None,
    #     parent_id: Optional[str] = None,
    #     **kwargs: Any,
    # ) -> str:
      
    #     print(f"Event started: {event_type}")
    #     print(f"Payload: {payload}")
    #     if event_type == CBEventType.FUNCTION_CALL:
          
    #       has_tool = EventPayload.TOOL in payload
    #       if(has_tool):
    #         tool_name = payload[EventPayload.TOOL]
    #         # if(self.writer):
    #         #   self.writer(f"Agent is using tool {tool_name.name}", self.tenant_id, self.chat_session_id)
        
    #     return super().on_event_start(event_type, payload, event_id, parent_id, **kwargs)

    def on_event_end(
        self,
        event_type: CBEventType,
        payload: Optional[Dict[str, Any]] = None,
        event_id: Optional[str] = None,
        **kwargs: Any,
    ) -> None:
      
        if(event_type == CBEventType.AGENT_STEP and self.reset is not None):
          self.reset()
      
        if(event_type == CBEventType.LLM and self.writer is not None):
          
          response = payload['response']
          pay_response = response.message.content
          
          last_user_message = payload['messages'][-1].content
           
          response_is_thought = pay_response.startswith("Thought: ")
          
          if(response_is_thought):
            text_json_translated_response = self.openai_service.thought_to_response(pay_response, last_user_message)
            
            # Remove any ```json and ``` characters
            text_json_translated_response = text_json_translated_response.replace("```json", "").replace("```", "")            
            
            # Decoding the json, the above is a string
            from json import loads
            translated_response = loads(text_json_translated_response)
            
            self.writer(translated_response.get("response", ""))
      
        return super().on_event_end(event_type, payload, event_id, **kwargs)