import React from "react";
import DialogButton from "@/components/ui/dialog-button";

type ConfirmProps = {
  callback: () => Promise<void>;
  children: React.ReactNode;
  title: string;
  button: React.ReactNode;
};

export default function ConfirmButton({
  callback,
  children,
  title,
  button,
}: ConfirmProps) {
  return (
    <>
      <DialogButton
        variant={"destructive"}
        callback={callback}
        okayText="Okay"
        headerText={title}
        closedToastTitle="Done!"
        closedToastText="Your changes have been processed."
        fullButton={button}
      >
        {children}
      </DialogButton>
    </>
  );
}
