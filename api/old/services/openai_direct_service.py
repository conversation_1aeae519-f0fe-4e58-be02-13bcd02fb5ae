from openai import OpenAI
from llama_index.core.settings import Settings

class OpenAIDirectService:
    # def __init__(self):
      # self.key = "********************************************************"
      # self.client = OpenAI(api_key=self.key)
    
    def get_response(self, prompt, system_message):
      llm = Settings.llm
      
      response = llm.complete(prompt)
      
      return response.text
      # completion = self.client.chat.completions.create(
      #   model="gpt-4-turbo-preview",
      #   messages=[
      #     {"role": "system", "content": system_message},
      #     {"role": "user", "content": prompt},
      #   ]
      # )
      
      # return completion.choices[0].message.content
    
    def thought_to_response(self, thought, question):
      
      
      prompt = f"""
      
        You are an AI system specialized in making technical inputs helpful. 
        Your task is to understand the technical description of a user's query 
        and then rewrite it in a way that is useful to the user. 
        
        Examples:
        - "Fetching information about X."
        - "Fetching additional information about the Y."
        - "Gathering information about XYZ."
        
        Always follow a similar format.
      
        Original user query: 
        {question}
        
        Consider the following response:
        {thought}
        =========
        
        Always follow the format defined above.
        
        Write the answer as a JSON object with the key "response" and the value as the response.
        eg. {{"response": "I am fetching information about X."}}
        
        Important: if the Original user query is chitchat (Hello, how are you, are you a robot, etc.), return an empty string in the response in the JSON.
        
        """
  
      return self.get_response(prompt, "")
      
      