import React, { useState } from "react";
import { SketchPicker } from "react-color";
import { Button } from "./button";

export default function ColourPicker({
  initialColor,
  onChange,
}: {
  initialColor: string;
  onChange: (color: string) => void;
}) {
  const [color, setColor] = useState(initialColor);
  const [showPicker, setShowPicker] = useState(false);

  const handleChangeComplete = (colorResult: any) => {
    setColor(colorResult.hex);
    onChange(colorResult.hex);
  };

  const togglePicker = (e: any) => {
    e.preventDefault();
    setShowPicker(!showPicker);
  };

  return (
    <div>
      {/* Button and color display */}
      <div style={{ display: "flex", alignItems: "center" }}>
        <Button
          onClick={togglePicker}
          style={{ marginRight: "10px" }}
          variant={"secondary"}
          className="flex items-center gap-2"
        >
          {color}
          <div
            className="inline-block"
            style={{
              backgroundColor: color,
              width: "24px",
              height: "24px",
              border: "1px solid #ccc",
            }}
          ></div>
        </Button>
      </div>

      {/* Color Picker */}
      {showPicker && (
        <>
          <div
            className="fixed inset-0 z-10"
            onClick={() => setShowPicker(false)}
          ></div>
          <SketchPicker
            color={color}
            onChangeComplete={handleChangeComplete}
            className="absolute z-20 "
          />
        </>
      )}
    </div>
  );
}
