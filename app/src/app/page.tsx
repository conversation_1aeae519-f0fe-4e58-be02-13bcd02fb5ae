"use client"
import React from 'react'
import { useRouter } from "next/navigation";
import { useAuthState } from "react-firebase-hooks/auth";
import { auth } from "@/services/firebase.config";
import { Skeleton } from "@/components/ui/skeleton";
import { useEffect } from "react";

export default function Homepage() {
  
  const [user, loading, error] = useAuthState(auth)

  const router = useRouter()

  useEffect(() => {
    if(!loading) { 
      if (user) {
        router.push('/hub/documents')
      } else {
        router.push('/login')
      }
    }
  }, [user, loading])

  return (
    <div>
      <Skeleton className="h-6 max-w-[60px]" />
    </div>
  )
}


