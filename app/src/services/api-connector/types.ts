export interface IFunctionRequest {
  tenantId: string;
}

export interface IFunctionResponse {
  status: string;
}

export interface ChatMessage {
  message: string;
  role: "user" | "assistant" | "system";
}

export interface DiscussRequest extends IFunctionRequest {
  tenantId: string;
  sessionId: string;
  text: string;
  history?: ChatMessage[];
  metadata?: {};
}

export interface SpeechRequest extends IFunctionRequest {
  tenantId: string;
  sessionId: string;
  history?: ChatMessage[];
  metadata?: {};
}

export interface DiscussResponse extends IFunctionResponse {
  response: string;
  quickReplies?: string[];
  status: string;
}

export interface SystemChatRequest extends IFunctionRequest {
  tenantId: string;
  text: string;
  systemMessages?: string[];
}

export interface SystemChatResponse extends IFunctionResponse {
  status: string;
  response: string;
}

export interface TrainRequest extends IFunctionRequest {
  tenantId: string;
}

export interface TrainResponse extends IFunctionResponse {
  status: string;
}

export interface CreateSessionRequest extends IFunctionRequest {
  tenantId: string;
  initialQuestion: string;
}

export interface CreateSessionResponse extends IFunctionResponse {
  status: string;
  sessionId: string;
}

export interface ReportRequest extends IFunctionRequest {
  id: string;
  tenantId: string;
  chatSessionId: string;
  previousMessage: string;
  message: string;
  positiveFeedback: boolean;
}

export interface ReportResponse extends IFunctionResponse {
  status: string;
  message: string;
}
