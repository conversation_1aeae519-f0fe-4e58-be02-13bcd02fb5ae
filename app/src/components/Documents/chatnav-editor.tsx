import React, { forwardRef } from "react";

import { MDXEditor } from "./mdx-editor";
import { headingsPlugin } from "@mdxeditor/editor/plugins/headings";
import { listsPlugin } from "@mdxeditor/editor/plugins/lists";
import { quotePlugin } from "@mdxeditor/editor/plugins/quote";
import { thematicBreakPlugin } from "@mdxeditor/editor/plugins/thematic-break";
import {
  BlockTypeSelect,
  BoldItalicUnderlineToggles,
  Button,
  ListsToggle,
  MDXEditorMethods,
  UndoRedo,
  toolbarPlugin,
} from "@mdxeditor/editor";
import { FaRegSave } from "react-icons/fa";

type Props = {
  markdown: string;
  setMarkdown: (markdown: string) => void;
  save: () => void;
};

export const ChatNavEditor = React.forwardRef<MDXEditorMethods, Props>(
  ({ markdown, setMarkdown, save }: Props, ref) => {
    return (
      <div className="prose w-full max-w-full">
        <MDXEditor
          markdown={markdown}
          onChange={(markdownString) => setMarkdown(markdownString)}
          ref={ref}
          plugins={[
            headingsPlugin(),
            listsPlugin(),
            quotePlugin(),
            thematicBreakPlugin(),
            toolbarPlugin({
              toolbarContents: () => (
                <>
                  {" "}
                  <Button
                    onClick={() => {
                      save && save();
                    }}
                    className="!text-[20px] !p-[10px] !cursor-pointer"
                  >
                    <FaRegSave />
                  </Button>
                  <UndoRedo />
                  <BlockTypeSelect />
                  <ListsToggle />
                  <BoldItalicUnderlineToggles />
                </>
              ),
            }),
          ]}
        />
      </div>
    );
  }
);
