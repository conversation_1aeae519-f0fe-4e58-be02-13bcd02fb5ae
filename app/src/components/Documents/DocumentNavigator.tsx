import React, { useMemo } from "react";
import useDashboardStore, { DashboardState } from "@/stores/dashboard.store";
import { useStore } from "@/stores/use-store";
import Link from "next/link";
import { cn } from "@/lib/utils";

type Props = {
  clientName: string;
  currentPageId: string;
};

export default function DocumentNavigator({
  clientName,
  currentPageId,
}: Props) {
  const documents = useStore(
    useDashboardStore,
    (state: DashboardState) => state.documents
  );

  const sortedDocuments = useMemo(
    () =>
      documents &&
      documents.sort((a, b) => {
        const aDate = new Date(a.lastModified);
        const bDate = new Date(b.lastModified);
        return bDate.getTime() - aDate.getTime();
      }),
    [documents]
  );

  return (
    <div className="sticky top-0">
      <div className="p-4 prose dark:prose-invert">
        <h4 className="font-bold">Document Navigator</h4>
      </div>
      {sortedDocuments &&
        sortedDocuments.map((doc) => (
          <Link
            href={`/hub/documents/${clientName}/document/${doc.id}`}
            key={doc.id}
            // "block p-4 py-2 border-b"
            className={cn(
              "block p-4 py-2 border-b",
              currentPageId === doc.id ? "bg-muted " : "bg-background "
            )}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="relative inline-block w-4 h-4 -top-[1px]"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M8.25 4.5l7.5 7.5-7.5 7.5"
              />
            </svg>

            {doc.title}
          </Link>
        ))}
    </div>
  );
}
