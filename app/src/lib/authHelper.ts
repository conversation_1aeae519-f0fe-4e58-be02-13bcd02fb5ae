import { firebaseAdmin } from "@/config/firebaseAdmin";

export async function isUserAuthenticated(request: Request): Promise<boolean> {
  const token = request.headers.get("Authorization")?.replace("Bearer ", "");

  if (!token || token.length < 1 || token === undefined || token === null) {
    return false;
  }

  try {
    const decodedToken = await firebaseAdmin.auth().verifyIdToken(token);

    if (decodedToken) {
      return true;
    }

    return false;
  } catch (error) {
    return false;
  }
}
