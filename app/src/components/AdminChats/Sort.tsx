import React from 'react'

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cn } from '@/lib/utils';

type SortProps = {
  label: string;
  options: { value: string, display: string }[];
  defaultValue: string;
  className?: string;
  onSortChange: (sortValue: string) => void;
}

export default function Sort({ label, className, options, defaultValue, onSortChange }: SortProps) {
  return (
    <Select onValueChange={(value: string) => onSortChange(value)} defaultValue={defaultValue}>
      <SelectTrigger className={cn("w-[180px]", className)}>
        <SelectValue placeholder="Select an option" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>{label}</SelectLabel>
          {options.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.display}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}
