class ReportRequest:
    def __init__(
        self,
        id: str,
        tenantId: str,
        chatSessionId: str,
        previousMessage: str,
        message: str,
        positiveFeedback: bool,
    ):
        self.id = id
        self.tenantId = tenantId
        self.chatSessionId = chatSessionId
        self.previousMessage = previousMessage
        self.message = message
        self.positiveFeedback = positiveFeedback
        
    @classmethod
    def from_dict(cls, data: dict) -> 'ReportRequest':
        return cls(
            id=data.get('id', None),
            tenantId=data.get('tenantId', None),
            chatSessionId=data.get('chatSessionId', None),
            previousMessage=data.get('previousMessage', None),
            message=data.get('message', None),
            positiveFeedback=data.get('positiveFeedback', None)
        )

class BasicResponse:
  def __init__(self, success: str, message: str):
    self.status = success
    self.message = message
    
  def to_dict(self):
    return {
      "status": self.status,
      "message": self.message
    }
    