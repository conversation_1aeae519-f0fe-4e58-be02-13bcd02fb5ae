import "./globals.css";
import { Mulish } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";

const nunito = Mulish({ subsets: ["latin"] });

export const metadata = {
  title: "Multiply ChatNav",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" data-theme="fantasy" suppressHydrationWarning={true}>
      <body className={nunito.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
