"use client";
import FirebaseDataTable, {
  ConfigItem,
} from "@/components/advanced-ui/firebase-datatable/firebase-datatable";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import DialogButton from "@/components/ui/dialog-button";
import { getAllSiteContent } from "@/services/data-import/import-sitemap";
import useDashboardStore from "@/stores/dashboard.store";
import { useStore } from "@/stores/use-store";
import React from "react";

type Props = {};

export default function GraphQLPage({ }: Props) {
  const clientName = useStore(
    useDashboardStore,
    (state) => state.selectedClientId
  );


  return (
    <Card>
      <CardHeader>
        <CardTitle>GraphQL Data</CardTitle>
        <CardDescription>
          These are the GraphQL data sources that are available for the chatbot.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        {clientName && (
          <FirebaseDataTable
            firebasePath={["tenants", clientName, "graphql"]}
            term="GraphQL Data Source"
            formConfig={{
              name: {
                key: "name",
                label: "Name",
                type: "text",
                defaultValue: "",
              } as ConfigItem,
              endpoint: {
                key: "endpoint",
                label: "Endpoint URL",
                type: "text",
                defaultValue: "",
              } as ConfigItem,
              query: {
                key: "query",
                label: "Query",
                type: "textarea",
                detailOnly: true,
                defaultValue: "",
              } as ConfigItem,

            }}
          />
        )}
        <div>

        </div>
      </CardContent>
    </Card>
  );
}
