# import QueryBundle
from llama_index.core.schema import QueryBundle

# import NodeWithScore
from llama_index.core.schema import NodeWithScore

# Retrievers
from llama_index.core.retrievers import (
    BaseRetriever,
    VectorIndexRetriever,
    KeywordTableSimpleRetriever,
)

from typing import List
from math import ceil

class ChatnavRetriever(BaseRetriever):
    """Custom retriever that performs both semantic search and hybrid search."""

    def __init__(
        self,
        kb_retriever: VectorIndexRetriever,
        # gpt4_retriever: VectorIndexRetriever,
        mode: str = "AND",
    ) -> None:
        """Init params."""

        self._kb_retriever = kb_retriever
        # self._gpt4_retriever = gpt4_retriever
        
        # if mode not in ("AND", "OR"):
        #     raise ValueError("Invalid mode.")
        # self._mode = mode

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """Retrieve nodes given query."""

        vector_nodes = self._kb_retriever.retrieve(query_bundle)
      
        # output nodes to console
        # for node in vector_nodes: 
        #     print(f"Node ID: {node.node.node_id}")
        #     print(node.node.get_content())
        return vector_nodes

    #   keyword_nodes = self._gpt4_retriever.retrieve(query_bundle)

    #   vector_ids = {n.node.node_id for n in vector_nodes}
    # #   keyword_ids = {n.node.node_id for n in keyword_nodes}

    #   combined_dict = {n.node.node_id: n for n in vector_nodes}
    #   combined_dict.update({n.node.node_id: n for n in keyword_nodes})
      
    #   if self._mode == "AND":
    #       retrieve_ids = vector_ids.intersection(keyword_ids)
    #   else:
    #       retrieve_ids = vector_ids.union(keyword_ids)

    #   retrieve_nodes = [combined_dict[rid] for rid in retrieve_ids]
      
    #   # Determine the number of nodes to keep based on the length of vector_nodes
    #   num_nodes_to_keep = ceil(len(vector_nodes) + (len(keyword_nodes) / 2))
      
    #   # Sort the nodes based on their score in descending order and take the top nodes
    #   top_nodes = sorted(retrieve_nodes, key=lambda x: x.score, reverse=True)[:num_nodes_to_keep]
      
      # return top_nodes
    #   return retrieve_nodes
