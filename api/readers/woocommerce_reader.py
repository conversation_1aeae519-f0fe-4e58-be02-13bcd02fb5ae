from typing import List, Dict, Union, Any
from llama_index.core.schema import Document
from llama_index.core.readers.base import BaseReader
import requests
import re
from google.cloud.firestore import Client


from models.tenant import WordPressConfig

def clean_html(raw_html: str) -> str:
    return re.sub('</?[^>]+(>|$)', '', raw_html).strip()

class WooCommerceReader(BaseReader):

    def __init__(self, config: WordPressConfig, database: Client, tenant_id: str):
        self.url = config.url
        self.wc_consumer_key = config.consumerKey
        self.wc_consumer_secret = config.consumerSecret
        self.database = database
        self.tenant_id = tenant_id
        
    def store_product_names_in_firestore(self, full_product_data: List[str]) -> None:
        """Store product names in the Firestore tenants collection."""
        tenants_ref = self.database.collection('tenants').document(self.tenant_id)
        tenants_ref.update({'productNames': full_product_data})

    def load_data(self, *args: Any, **load_kwargs: Any) -> List[Document]:
        products = self.get_all_products()
        results = []
        full_list_of_products = []
        all_product_names = []

        for product in products:
            if product.get('status') != 'publish':
                continue

            product_categories = [category['name'] for category in product.get('categories', [])]
            categories_string = ', '.join(product_categories)

            product_description = f"""
                Product name: {product.get('name', '')}
                Product description: {clean_html(product.get('description', ''))}
                Product categories: {categories_string}
                URL: {product.get('permalink', '')}
            """

            results.append(
                Document(
                    text = product_description,
                    doc_id=product.get('id', ''),
                    doc_hash=product.get('id', ''),
                    metadata =  {
                        'price': product.get('price', ''),
                        'regular_price': product.get('regular_price', ''),
                        'sale_price': product.get('sale_price', ''),
                        'sku': product.get('sku', ''),
                        'stock_status': product.get('stock_status', ''),
                        'url': product.get('permalink', ''),
                        'updated_at': product.get('date_modified', '')
                    }
                )
            )

            full_list_of_products.append({'id': product.get('id', ''), 'text': product_description})
            cleaned_product_name = product.get('name', '')
            all_product_names.append(cleaned_product_name)

        full_list_of_products_text = ', '.join([product['text'] for product in full_list_of_products])

        results.append(
            Document(
                doc_id='full_list_of_products',
                doc_hash='full_list_of_products',
                text = f'This is the complete list of products sold on the website: {full_list_of_products_text}',
                metadata = {}
            )
        )

        self.store_product_names_in_firestore(all_product_names)

        return results

    def get_all_products(self) -> List[Dict]:
        products = []
        next_page = 1

        while next_page:
            response, next_page = self.get_products_page(next_page)
            if response:
                products.extend(response)

        return products

    def get_products_page(self, current_page: int = 1) -> Union[List[Dict], int]:
        url = f"{self.url}/wp-json/wc/v3/products?consumer_key={self.wc_consumer_key}&consumer_secret={self.wc_consumer_secret}&per_page=100&page={current_page}"
        response = requests.get(url)
        data = response.json()
        num_pages = int(response.headers.get('X-WP-TotalPages', 1))
        next_page = current_page + 1 if num_pages > current_page else None

        return data, next_page
