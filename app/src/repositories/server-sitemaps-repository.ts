import { database } from "@/config/firebaseAdmin";
import { FeedbackCategory } from "@/models/feedbackCategory";
import { Sitemap } from "@/models/sitemap";
import { SitemapPage } from "@/models/sitemap-page";
import { getCategoryFromAI } from "@/services/openai/category.ai.tool";

export const getSitemaps = async (tenantId: string): Promise<Sitemap[]> => {
  const sitemapCollection = await database
  .collection("tenants")
  .doc(tenantId)
  .collection("sitemaps")
  .get();

  const sitemapData = sitemapCollection.docs.map((doc) => {
    return {
      id: doc.ref.id,
      ...doc.data()
    } as Sitemap;
    });

  return sitemapData;
};

export const getCategories = async (tenantId: string): Promise<FeedbackCategory[]> => {

  const sitemapCollection = await database
  .collection("tenants")
  .doc(tenantId)
  .collection("reportCategories")
  .get();

  const categories = sitemapCollection.docs.map((doc) => {
    return {
      id: doc.ref.id,
      ...doc.data()
    } as FeedbackCategory;
  });

  return categories;

}


export const syncSitemapPages = async (tenantId: string, pages: SitemapPage[]) => {
  try {
    const pagesRef = database.collection("tenants").doc(tenantId).collection("websitePages");
    const existingPagesSnapshot = await pagesRef.get();

    // Map existing pages by ID for easy lookup and track pages to delete
    const existingPagesMap: { [key: string]: SitemapPage } = {};
    const pagesToDelete: string[] = [];
    
    existingPagesSnapshot.forEach(doc => {
      const data = doc.data() as SitemapPage;
      existingPagesMap[doc.id] = data;
    });

    // Create a new batch for efficient Firestore writes
    const batch = database.batch();
    let pageCount = 0;

    // Helper function to clean up undefined values from the page object
    const cleanPageData = (page: SitemapPage) => {
      return Object.fromEntries(Object.entries(page).filter(([_, value]) => value !== undefined));
    };

    // Loop through each page from the sitemap
    for (const page of pages) {
      const existingPage = existingPagesMap[page.id];

      // Check if the page already exists and has the same `lastMod`
      if (existingPage) {
        if (existingPage.lastMod === page.lastMod) {
          console.log(`Skipping unchanged page - ${page.title}`);
          delete existingPagesMap[page.id]; // Remove it from the map, so it won't be deleted
          continue;
        }
        // Preserve `categoryId` if available
        if (existingPage.categoryId !== undefined) {
          page.categoryId = existingPage.categoryId;
        }
      }

      // Clean up undefined values before adding to Firestore
      const cleanPage = cleanPageData(page);
      
      // Queue page for creation or update
      const pageRef = pagesRef.doc(page.id);
      batch.set(pageRef, cleanPage);
      pageCount++;
      console.log(`Queued page ${pageCount} for import - ${page.title}`);

      // Remove from the existing pages map to avoid deletion
      delete existingPagesMap[page.id];
    }

    // Any remaining pages in existingPagesMap are no longer in the sitemap, so mark for deletion
    for (const pageId of Object.keys(existingPagesMap)) {
      batch.delete(pagesRef.doc(pageId));
      console.log(`Marked page for deletion - ${pageId}`);
    }

    // Commit all batch operations
    await batch.commit();
    console.log(`Reimport complete: ${pageCount} pages imported, ${Object.keys(existingPagesMap).length} pages deleted.`);

  } catch (error) {
    console.error("Failed to sync sitemap pages:", error);
  }
};
