import requests
from bs4 import <PERSON><PERSON>oup
from PIL import Image
import io
import base64
from cachetools import TTLCache



class OpenGraphFetcher:
    """Utility class for fetching and resizing OpenGraph images from URLs, returning them as base64 encoded strings."""


    cache = TTLCache(maxsize=100, ttl=86400)  # 86400 seconds = 24 hours

    @staticmethod
    def fetch_og_image(url):
        """Fetches the OpenGraph image URL from the given webpage URL and returns it as a base64 encoded string resized to 75x75, with caching."""
        # Check if the URL is already in the cache
        if url in OpenGraphFetcher.cache:
            return OpenGraphFetcher.cache[url]
        
        try:
            response = requests.get(url, headers={"User-Agent": "Mozilla/5.0"})
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                og_image = soup.find('meta', property='og:image')
                og_image_url = og_image.get('content') if og_image else None
                
                OpenGraphFetcher.cache[url] = og_image_url
                return og_image_url
                
                # if og_image and og_image.get('content'):
                #     # image_data = OpenGraphFetcher.resize_and_encode_image(og_image['content'])
                #     # Store the processed image in the cache
                #     OpenGraphFetcher.cache[url] = image_data
                #     return image_data
                # else:
                #     return ""
            else:
                return ""
        except requests.RequestException as e:
            return str(e)

    @staticmethod
    def resize_and_encode_image(image_url):
        """Resizes an image to a fixed height of 60 pixels while maintaining the aspect ratio, and encodes it as a base64 string."""
        response = requests.get(image_url)
        if response.status_code == 200:
            img = Image.open(io.BytesIO(response.content))
            # Fixed height, width is calculated to maintain aspect ratio
            fixed_height = 60
            original_width, original_height = img.size
            aspect_ratio = original_width / original_height
            new_width = int(fixed_height * aspect_ratio)

            img = img.resize((new_width, fixed_height), Image.BILINEAR)
            buffered = io.BytesIO()
            img.save(buffered, format="JPEG")
            img_base64 = base64.b64encode(buffered.getvalue())
            return img_base64.decode('utf-8')
        else:
            return ""
          

    @staticmethod
    def get_og_images(data):
        """Processes a list of URLs and names from JSON data to fetch their OpenGraph images, resized and encoded."""
        results = []
        for link in data['links']:
            image_base64 = OpenGraphFetcher.fetch_og_image(link['url'])
            results.append({
                'name': link['name'],
                'url': link['url'],
                'image': image_base64
            })
        return results
