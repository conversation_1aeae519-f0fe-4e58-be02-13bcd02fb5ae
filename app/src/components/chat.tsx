"use client";
import React, { Suspense, useEffect, useMemo, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Grid } from "react-loader-spinner";
import Bubble from "./chat/bubble";
import { welcomeMessage } from "@/lib/strings";
import { Message } from "@/models/message";
import "./_chat.scss";
import {
  collection,
  getDocs,
  onSnapshot,
  orderBy,
  query,
} from "firebase/firestore";
import { db } from "@/services/firebase.config";
import { ChatMessage } from "@/services/api-connector/types";
import { getFirebaseApiEndpoints } from "@/services/api-connector/api-connector";
import LoadingBubble from "./chat/loading-bubble";
import { fetchStream } from "@/lib/streamFetcher";
import LinkPreview from "./chat/link-preview";

import { Tenant } from "@/models/tenant";
import { orderMessagesByDate, useLastLinkMessage } from "@/lib/chatUtilities";
import { useTheme } from "next-themes";
import axios from "axios";

// Extend the global type of ReadableStream to make it async iterable.
declare global {
  interface ReadableStream<R = any> {
    [Symbol.asyncIterator](): AsyncIterableIterator<R>;
  }
}

export default function Chat({ tenantId, tenant }: { tenantId: string, tenant: Tenant }) {
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isParsingJSON, setIsParsingJSON] = useState(false);
  const [jsonBuffer, setJsonBuffer] = useState("");

  const [startDateTime, setStartDateTime] = useState<null | Date>(null);

  const [input, setInput] = useState("");
  const [chatSessionId, setChatSessionId] = useState("");
  const [createdSession, setCreatedSession] = useState(false);
  const [quickReplies, setQuickReplies] = useState<string[]>();
  const [generatedMessage, setGeneratedMessage] = useState("");

  const generatedMessageRef = useRef<string>();

  // Create a reference to the scroll area
  const scrollAreaRef = useRef<null | HTMLDivElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  const endpoints = useMemo(() => getFirebaseApiEndpoints(), []);

  const { setTheme } = useTheme()

  useEffect(() => {
    setTheme('light')
  }, [])

  useEffect(() => {
    let sessionid = sessionStorage.getItem("chatSessionId");

    if (sessionid) {
      setChatSessionId(sessionid);
    }

    const startTime = sessionStorage.getItem("chatStartDateTime");

    if (startTime) {
      setStartDateTime(new Date(startTime));
    } else {
      setStartDateTime(new Date());
      sessionStorage.setItem("chatStartDateTime", new Date().toString());
    }
  }, []);

  const ensureSession = async (initialQuestion: string) => {
    if (chatSessionId) {
      return chatSessionId;
    }

    const data = await endpoints.createSession({
      tenantId,
      initialQuestion,
    });

    setChatSessionId(data.sessionId);

    sessionStorage.setItem("chatSessionId", data.sessionId);

    setCreatedSession(true);

    return data.sessionId;
  };

  const [utterances, setUtterances] = useState([] as string[]);

  const sendMessage = async (
    text: string,
    isText: boolean = true,
    file: File | null = null
  ) => {
    if (text.trim() === "" && file == null) return;

    setQuickReplies([]);
    generatedMessageRef.current = "";

    setIsLoading(true);

    const sessionId = await ensureSession(text);

    const parentUrl = window.location.search.split("=")[1];

    const history = messages
      .filter(x => x.role !== "links")
      .map((message) => {
        return {
          role: message.role,
          message: message.message,
        } as ChatMessage;
      });

    setMessages([...messages.filter(x => x.role === "links")]);

    setInput("");

    // Add a temporary message to the UI

    setMessages([
      ...messages,
      {
        id: "temp",
        message: text,
        role: "user",
        createdAt: new Date(),
      },
    ]);

    let sttText = "";
    if (file && !isText) {
      const stt = await endpoints.speechToText(file);

      sttText = stt.corrected_text;
    }
    speechSynthesis.cancel();
    // const response = await fetchStream(
    //   endpoints.chatEndpoint,
    //   {
    //     method: "POST",
    //     headers: {
    //       "Content-Type": "application/json",
    //     },
    //     body: JSON.stringify({
    //       sessionId,
    //       history,
    //       tenantId,
    //       text: isText ? text : sttText,
    //     }),
    //   },
    //   (data: string) => {
    //     setIsLoading(false);
    //     setIsStreaming(true);
    //   },
    //   (json: any) => {
    //     if (json.quick_replies) {
    //       setQuickReplies(json.quick_replies);
    //     }
    //   },
    //   () => {
    //     setIsStreaming(false);

    //   },
    //   () => {
    //     setIsStreaming(false);
    //   },
    //   (error: string) => {
    //     // Add it to the messages
    //     const errorMessage = {
    //       id: "error",
    //       message: error,
    //       role: "assistant",
    //       createdAt: new Date(),
    //     } as Message;

    //     setMessages((prevMessages) => [...prevMessages, errorMessage]);
    //   }
    // );

    await axios.post(endpoints.chatEndpoint, {
      sessionId,
      history,
      tenantId,
      text: isText ? text : sttText,
    })

    setIsLoading(false);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    await sendMessage(input);
  };

  const quickReply = async (reply: string) => {
    // Using handleSubmit to send the quick reply
    setQuickReplies([]);
    await sendMessage(reply);
  };

  const [messages, setMessages] = React.useState([] as Message[]);
  const [preMessages, setPreMessages] = React.useState([] as Message[]);

  const [initialDocumentsLoaded, setInitialDocumentsLoaded] =
    React.useState(false);

  const loadDocuments = async () => {
    const unsubscribe = onSnapshot(
      query(
        collection(
          db,
          "tenants",
          tenantId,
          "chatSessions",
          chatSessionId,
          "messages"
        ),
        orderBy("createdAt", "asc")
      ),
      (querySnapshot) => {
        if (!initialDocumentsLoaded) {
          const messages = [] as any;
          querySnapshot.forEach((doc: any) => {
            messages.push({
              id: doc.id,
              ...doc.data(),
            });
          });
          setMessages(messages);

          setInitialDocumentsLoaded(true);
          return messages;
        }
      }
    );

    return [unsubscribe];
  };

  const handleWelcomeMessage = async () => {
    // 1. Check the local storage to see if we have already shown the welcome messages
    const sendMessageWithDelay = (message: Message, index: number) => {
      setTimeout(() => {
        setPreMessages((prevMessages) => [...prevMessages, message]);
      }, 2000 * index);
    };

    const welcomeMessagesShown = localStorage.getItem("welcomeMessagesShown");

    if (welcomeMessagesShown) {
      sendMessageWithDelay(
        {
          id: "1",
          message: welcomeMessage,
          role: "assistant",
          createdAt: new Date(),
        },
        0
      );
      return;
    }

    localStorage.setItem("welcomeMessagesShown", "true");

    const welcomeMessagesRef = await getDocs(
      query(collection(db, "tenants", tenantId, "welcomeMessages"))
    );

    // Now we add as them as normal messages from the bot, with a delay of 2 seconds between each message

    const welcomeMessages = welcomeMessagesRef.docs.map((doc) => {
      return {
        id: doc.id,
        description: doc.data().description,
        order: doc.data().order,
      };
    });

    setIsLoading(true);

    setTimeout(() => {
      setIsLoading(false);
    }, 2000 * welcomeMessages.length);

    welcomeMessages
      .sort((a, b) => {
        return parseInt(a.order) - parseInt(b.order);
      })
      .forEach(async (message, index) => {
        const actualMessage = {
          id: message.id,
          message: message.description,
          role: "assistant",
          createdAt: new Date(),
        } as Message;

        await sendMessageWithDelay(actualMessage, index);
      });
  };

  React.useEffect(() => {
    handleWelcomeMessage();
  }, [tenantId]);

  React.useEffect(() => {
    if (!tenantId || !chatSessionId) {
      return;
    }

    let unsubscribes = [] as any;

    const processEffect = async () => {
      if (tenantId && chatSessionId) {
        unsubscribes = await loadDocuments();
      }
    };

    processEffect();

    return () => {
      unsubscribes.forEach((unsubscribe: any) => {
        unsubscribe();
      });
    };
  }, [chatSessionId, tenantId]);


  const scrollToBottom = () => {
    // Scroll to the bottom when the messages change
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({
        top: scrollAreaRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  }



  const lastLinkMessage = useLastLinkMessage(messages);

  useEffect(() => {
    scrollToBottom();
  }, [messages, quickReplies, preMessages, lastLinkMessage]);


  const assistantName = useMemo(() => {
    return tenant && tenant.widget && tenant.widget?.assistantName || `${tenant && tenant.name} AI`;
  }, [tenant]);

  if (!assistantName) {
    return <div>Loading...</div>
  }


  return (
    <Suspense fallback={<div>Loading...</div>}>

      <Card className="w-full sm:w-[440px] max-w-full max-h-screen relative shadow-md">

        <CardHeader>
          <CardTitle className="text-lg">
            {tenant && tenant.widget && tenant.widget?.chatTitle || tenant && tenant.name}</CardTitle>
          <CardDescription className="leading-3 ">
            Powered by ChatNav
          </CardDescription>
        </CardHeader>
        <CardContent className="pb-[80px]">
          <ScrollArea
            ref={scrollAreaRef}
            className="h-screen sm:h-[450px] max-h-[calc(100vh_-_200px)] overflow-y-auto w-full spacy-y-4 pr-4"
          >
            {startDateTime &&
              orderMessagesByDate(preMessages).map((message, index) => (
                <Bubble
                  key={`message-${index}`}
                  message={message}
                  chatSessionId={chatSessionId}
                  tenantId={tenantId}
                  botName={tenant && tenant.widget && tenant.widget?.assistantName}
                  previousMessage={preMessages[index - 1]}
                  reporteable={false}
                />
              ))}

            {chatSessionId &&
              orderMessagesByDate(messages)
                .filter((message) => message.role !== "links")
                .map((message, index) => (
                  <Bubble
                    key={`message-${index}`}
                    message={message}
                    botName={assistantName}
                    chatSessionId={chatSessionId}
                    tenantId={tenantId}
                    previousMessage={messages[index - 1]}
                  />
                ))}

            {isLoading && !isStreaming && (
              <LoadingBubble author={assistantName} />
            )}

            {quickReplies && quickReplies.length > 0 && (
              <div className="flex flex-wrap w-full gap-2">
                {quickReplies.map((reply, index) => (
                  <Button
                    key={`reply-${index}`}
                    variant={"outline"}
                    className="text-xs"
                    onClick={() => quickReply(reply)}
                  >
                    {reply}
                  </Button>
                ))}
              </div>
            )}
            {lastLinkMessage &&
              <LinkPreview data={JSON.parse(lastLinkMessage)} />
            }
          </ScrollArea>

        </CardContent>
        <CardFooter className="absolute bottom-0 w-full bg-white flex flex-col">

          <form
            onSubmit={handleSubmit}
            className="flex items-center justify-center w-full space-x-2"
          >
            <Input
              placeholder="Type your message"
              value={input}
              maxLength={500}
              onChange={handleInputChange}
            />
            {/* <MicrophoneButton sendMessage={sendMessage} /> */}
            <Button disabled={isLoading} className="text-white">
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <Grid
                    height={12}
                    width={12}
                    radius={5}
                    ariaLabel="grid-loading"
                    color="#fff"
                    ms-visible={true}
                  />
                  {"Loading..."}
                </div>
              ) : (
                "Send"
              )}
            </Button>
          </form>
        </CardFooter>
      </Card>
    </Suspense>
  );
}
