"use client";
import { useState, useEffect } from "react";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  query,
  updateDoc,
} from "firebase/firestore";
import { db } from "@/services/firebase.config";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import useDashboardStore from "@/stores/dashboard.store";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { unknown } from "zod";
import ConfirmButton from "@/components/ui/confirm-button";
import DialogButton from "@/components/ui/dialog-button";
import { Input } from "@/components/ui/input";
import { FeedbackCategory } from "@/models/feedbackCategory";

function CategoryForm({
  category,
  setCategory,
}: {
  category: FeedbackCategory;
  setCategory: (category: FeedbackCategory) => void;
}) {
  return (
    <>
      <div className="flex flex-col gap-2">
        <label className="text-sm font-bold">Label</label>
        <Input
          value={category?.label}
          onChange={(e) =>
            setCategory({
              ...category,
              label: e.target.value,
            })
          }
        />
      </div>
      <div className="flex flex-col gap-2">
        <label className="text-sm font-bold">Description</label>
        <Textarea
          value={category?.description}
          onChange={(e) =>
            setCategory({
              ...category,
              description: e.target.value,
            })
          }
        />
      </div>
    </>
  );
}

export default function FeedbackCategories() {
  const clientName = useDashboardStore((state) => state.selectedClient)?.value;
  const [categories, setCategories] = useState([] as FeedbackCategory[]);

  const addCategory = async () => {
    const docsRef = collection(db, "tenants", clientName, "reportCategories");

    const categoryToAdd = {
      label: newCategory.label,
      description: newCategory.description,
    } as Partial<FeedbackCategory>;

    const docRef = await addDoc(docsRef, {
      ...categoryToAdd,
    });

    categoryToAdd.id = docRef.id;

    setCategories([...categories, categoryToAdd as FeedbackCategory]);
  };

  const updateCategory = async (category: FeedbackCategory) => {
    const docRef = doc(
      db,
      "tenants",
      clientName,
      "reportCategories",
      category.id
    );

    await updateDoc(docRef, {
      label: category.label,
      description: category.description,
    });

    setCategories(
      categories.map((c) => {
        if (c.id === category.id) {
          return category;
        }

        return c;
      })
    );
  };

  const deleteCategory = async (categoryId: string) => {
    const docRef = doc(
      db,
      "tenants",
      clientName,
      "reportCategories",
      categoryId
    );

    await deleteDoc(docRef);

    setCategories(categories.filter((category) => category.id !== categoryId));
  };

  useEffect(() => {
    const fetchReportCategories = async () => {
      const docsRef = query(
        collection(db, "tenants", clientName, "reportCategories")
      );
      const docs = await getDocs(docsRef);

      if (docs.empty) {
        return;
      }

      const categories = docs.docs.map((doc) => {
        return {
          id: doc.id,
          label: doc.data().label,
          description: doc.data().description,
        } as FeedbackCategory;
      });

      setCategories(categories);
    };

    fetchReportCategories();
  }, [clientName]);

  const [hoveredIndex, setHoveredIndex] = useState(-1);
  const [selectedCategory, setSelectedCategory] = useState(
    null as unknown as FeedbackCategory
  );
  const [newCategory, setNewCategory] = useState(
    null as unknown as FeedbackCategory
  );

  return (
    <>
      <Table className="mt-4">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[150px]">Label</TableHead>
            <TableHead className="">Description</TableHead>
            <TableHead className="w-[150px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {categories &&
            categories.map((category, index) => {
              return (
                <TableRow
                  className={cn(
                    index % 2 == 1 ? "bg-muted/20" : "bg-background",
                    "cursor-pointer relative"
                  )}
                  key={`${category.id}-${index}`}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(-1)}
                >
                  <TableCell className="font-bold">{category.label}</TableCell>

                  <TableCell className="text-xs">
                    {category.description}
                  </TableCell>

                  <TableCell className="flex gap-2 text-xs">
                    {hoveredIndex === index && (
                      <>
                        <DialogButton
                          fullButton={<div className="underline">Edit</div>}
                          headerText="Edit category"
                          closedToastTitle="Category updated!"
                          closedToastText="Your category has been updated."
                          variant={"outline"}
                          okayText="Save category"
                          className="mt-4"
                          onOpenChange={(open) => {
                            if (!open) {
                              setSelectedCategory(
                                null as unknown as FeedbackCategory
                              );
                            } else {
                              setSelectedCategory(category);
                            }
                          }}
                          callback={() => {
                            updateCategory(selectedCategory);
                            setSelectedCategory(
                              null as unknown as FeedbackCategory
                            );
                          }}
                        >
                          <div className="flex flex-col gap-4">
                            <p>
                              Edit the category that admin users can select when
                              reviewing feedback
                            </p>

                            <CategoryForm
                              category={selectedCategory}
                              setCategory={setSelectedCategory}
                            />
                          </div>
                        </DialogButton>

                        <ConfirmButton
                          callback={async () =>
                            await deleteCategory(category.id)
                          }
                          button={
                            <div className="px-1 rounded bg-destructive text-background">
                              Delete
                            </div>
                          }
                          title="Are you sure?"
                        >
                          Yes, delete this category.
                        </ConfirmButton>
                      </>
                    )}
                  </TableCell>
                </TableRow>
              );
            })}
        </TableBody>
      </Table>

      <DialogButton
        button={<>Add a new category</>}
        headerText="Add a new category"
        closedToastTitle="Category added!"
        closedToastText="Your new category has been added."
        variant={"outline"}
        okayText="Add category"
        className="mt-4"
        callback={() => {
          addCategory();
          setNewCategory(null as unknown as FeedbackCategory);
        }}
      >
        <div className="flex flex-col gap-4">
          <p>
            Add a new category to the list of categories that users can select
          </p>

          <CategoryForm category={newCategory} setCategory={setNewCategory} />
        </div>
      </DialogButton>
    </>
  );
}
