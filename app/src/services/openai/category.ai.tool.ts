// categoryTool.ts
import { FeedbackCategory } from '@/models/feedbackCategory';
import { createAIEngine } from './openai.base.service';

export async function getCategoryFromAI(content: string, categories: FeedbackCategory[]) {
    const { createCompletion } = createAIEngine();
    
    const categoryPromptString = `
    These are the available categories: 
    ${categories.map(c => `ID: "${c.id}", "Description: useful for ${c.description}"`).join('\n')}
    `;

    const systemPrompt = `You are a categorisation assistant. 
    ${categoryPromptString}
  
    ----------
    Given a piece of content, please categorize it by selecting the appropriate category ID. Only reply with the ID of the category you think is most appropriate. Nothing else.
    `

    const output = await createCompletion(content, systemPrompt); 

    // Check if the output is a valid category ID
    const categoryId = categories.find(c => c.id === output)?.id;
    if (!categoryId) {
        throw new Error(`Invalid category ID: ${output}`);
    }

    return categoryId;

}
