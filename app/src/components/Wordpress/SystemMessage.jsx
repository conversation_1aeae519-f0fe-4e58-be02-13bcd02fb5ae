export default function SystemMessage({ message, onChange, onRemove }) {
  return (
    <div className="flex items-center mb-2">
      <input type='text' name='message' placeholder='Type a system message' className="w-full mr-2 input input-bordered" value={message} onChange={onChange} required />
      <button className="btn btn-outline btn-accent" onClick={onRemove}>Remove</button>
    </div>
  );
}
