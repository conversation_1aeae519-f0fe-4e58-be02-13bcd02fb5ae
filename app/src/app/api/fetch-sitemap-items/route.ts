import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import * as xml2js from 'xml2js';
import * as cheerio from 'cheerio';
import { getFullDataFromSitemap } from '@/services/data-import/sitemap-helpers';
import { getAllSiteContent } from '@/services/data-import/import-sitemap';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const tenantId = searchParams.get('tenantId');

  if (!tenantId) {
    return NextResponse.json(
      {
        success: false,
        message: 'Tenant ID is required',
      },
      { status: 400 }
    );
  }

  try {
    // const outObj = await getFullDataFromSitemap(url, tenantId);
    const outObj = await getAllSiteContent(tenantId);

    return NextResponse.json(
      {
        success: true,
        message: '',
        data: outObj,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error fetching sitemap:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Error fetching sitemap',
      },
      { status: 500 }
    );
  }
}
