# import QueryBundle
from llama_index.core.schema import QueryBundle

# import NodeWithScore
from llama_index.core.schema import NodeWithScore

# Retrievers
from llama_index.core.retrievers import (
    BaseRetriever,
    VectorIndexRetriever,
    KeywordTableSimpleRetriever,
)

from typing import List
from math import ceil

class EmptyRetriever(BaseRetriever):
    """For system search."""

    def __init__(
        self,
        
    ) -> None:
        """Init params."""

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
      """Retrieve nodes given query."""

      empty_list = []
      return empty_list
