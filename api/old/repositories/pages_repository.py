from cachetools import cached, TTLCache
from typing import Any
from google.cloud.firestore import Client

# Define your cache outside of the class; this example uses a TTL (Time to Live) cache that stores up to 100 items for 1 hour (3600 seconds).
pages_cache = TTLCache(maxsize=100, ttl=3600)

class SingletonMeta(type):
    """
    This is a metaclass that creates a Singleton base class when called.
    """
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(SingletonMeta, cls).__call__(*args, **kwargs)
        return cls._instances[cls]

class PagesRepository(metaclass=SingletonMeta):

  def __init__(self, database: Client):
    # Initialize the database only if it has not been initialized before.
    if not hasattr(self, 'initialized'):
        self.database = database
        self.initialized = True

  @cached(cache=pages_cache)  # This decorator enables caching for this method.
  def get_pages(self, tenant_id: str):
  
    pages_ref = (self.database
                      .collection('tenants')
                      .document(tenant_id)
                      .collection("websitePages"))  
    
    pages = pages_ref.get()
    pages_data = {}
    for page in pages:
      page_data = page.to_dict()
      page_id = page.id
      pages_data[page_id] = {"id": page_id, "title": page_data["title"], "url": page_data["url"]}
      
    return pages_data
