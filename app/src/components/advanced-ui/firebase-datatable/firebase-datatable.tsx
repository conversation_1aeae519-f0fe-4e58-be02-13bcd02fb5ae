"use client";
import { useState, useEffect, useMemo } from "react";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  query,
  updateDoc,
} from "firebase/firestore";
import { db } from "@/services/firebase.config";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import useDashboardStore from "@/stores/dashboard.store";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { unknown } from "zod";
import ConfirmButton from "@/components/ui/confirm-button";
import DialogButton from "@/components/ui/dialog-button";
import { Input } from "@/components/ui/input";
import { DraggableTableRow } from "./draggable-tablerow";
import { DTOForm } from "./dto-form";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";

export type ConfigItem = {
  key: string;
  label: string;
  type: "text" | "textarea" | "select" | "switch";
  detailOnly?: boolean;
  defaultValue: string | boolean | number;
  selectValues?: string[];
};

export type FormConfig = {
  [key: string]: ConfigItem;
};

export type FirebaseDataTableProps = {
  firebasePath: string[];
  term: string;
  formConfig: FormConfig;
};

export interface IFirebaseDTO {
  id: string;
  order: string;
  [key: string]: string;
}

export default function FirebaseDataTable({
  firebasePath,
  term,
  formConfig,
}: FirebaseDataTableProps) {
  const clientId = useDashboardStore((state) => state.selectedClientId) || "";

  const [dtos, setDtos] = useState([] as IFirebaseDTO[]);

  const fullPath = useMemo(() => firebasePath.join("/"), [firebasePath]);

  const docsRef = useMemo(() => collection(db, fullPath), [fullPath]);

  const [hoveredIndex, setHoveredIndex] = useState(-1);

  const [selectedDTO, setSelectedDTO] = useState(
    null as unknown as IFirebaseDTO
  );
  const [newDTO, setNewDTO] = useState(null as unknown as IFirebaseDTO);

  const addDto = async () => {
    const dtoToAdd = {
      ...newDTO,
      order: dtos.length.toString(),
    } as Omit<IFirebaseDTO, "id">;

    const docRef = await addDoc(docsRef, {
      ...dtoToAdd,
    });

    const fullDTO = {
      ...dtoToAdd,
      id: docRef.id,
    } as IFirebaseDTO;

    setDtos([...dtos, fullDTO as IFirebaseDTO]);
  };

  const updateDto = async (dto: IFirebaseDTO) => {
    const docRef = doc(db, fullPath, dto.id);

    await updateDoc(docRef, {
      ...dto,
    });

    setDtos(
      dtos.map((c) => {
        if (c.id === dto.id) {
          return dto;
        }
        return c;
      })
    );
  };

  const deleteDto = async (id: string) => {
    const docRef = doc(db, fullPath, id);

    await deleteDoc(docRef);

    setDtos(dtos.filter((dto) => dto.id !== id));
  };

  useEffect(() => {
    const fetchDTOs = async () => {
      const docs = await getDocs(docsRef);

      if (docs.empty) {
        return;
      }

      const dtos = docs.docs.map((doc) => {
        return {
          id: doc.id,
          ...doc.data(),
        } as IFirebaseDTO;
      });

      setDtos(dtos);
    };

    fetchDTOs();
  }, [clientId]);

  const capitalise = (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  const configKeys = Object.keys(formConfig);

  return (
    <DndProvider backend={HTML5Backend}>
      <Table className="mt-4">
        <TableHeader>
          <TableRow>
            {configKeys.length == 1 && <TableHead className="w-0"></TableHead>}
            {formConfig &&
              configKeys.map((key, index) => {
                const configItem = formConfig[key];
                if (configItem.detailOnly) {
                  return null;
                }

                return (
                  <TableHead
                    className={
                      index == 0 && configKeys.length > 1 ? "w-[150px]" : ""
                    }
                    key={key}
                  >
                    {configItem.label}
                  </TableHead>
                );
              })}
            <TableHead className="w-[150px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {dtos &&
            dtos
              .sort((a, b) => {
                return parseInt(a.order) - parseInt(b.order);
              })
              .map((item, index) => {
                return (
                  <DraggableTableRow
                    key={`${item.id}-${index}`}
                    item={item}
                    index={index}
                    moveRow={(dragIndex, hoverIndex) => {
                      const dragRow = dtos[dragIndex];
                      const newDtos = [...dtos];
                      newDtos.splice(dragIndex, 1);
                      newDtos.splice(hoverIndex, 0, dragRow);
                      setDtos(newDtos);
                    }}
                    configKeys={configKeys}
                    formConfig={formConfig}
                    hoveredIndex={hoveredIndex}
                    setHoveredIndex={setHoveredIndex}
                    term={term}
                    setSelectedDTO={setSelectedDTO}
                    selectedDTO={selectedDTO}
                    updateDto={updateDto}
                    deleteDto={deleteDto}
                  />
                );
              })}
        </TableBody>
      </Table>

      <DialogButton
        button={<>Add a new {term}</>}
        headerText={`Add a new ${term}`}
        closedToastTitle={`${capitalise(term)} added!`}
        closedToastText={`Your ${term} has been added.`}
        variant={"outline"}
        okayText={`Save ${term}`}
        className="mt-4"
        callback={() => {
          addDto();
          setNewDTO({} as IFirebaseDTO);
        }}
      >
        <div className="flex flex-col gap-4">
          <p>
            Add a new {term} to the list of {term}s that users can select
          </p>
          <DTOForm dto={newDTO} setDTO={setNewDTO} config={formConfig} />
        </div>
      </DialogButton>
    </DndProvider>
  );
}
