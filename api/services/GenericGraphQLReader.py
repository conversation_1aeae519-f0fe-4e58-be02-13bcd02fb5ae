import requests
import hashlib
from typing import Any, List, Union

class GenericGraphQLReader():

    def __init__(self, graphql_url: str, query: str):
        """
        Initialize with user-provided GraphQL URL, headers, and query.
        """
        self.graphql_url = graphql_url
        self.headers = {
          'Content-Type': 'application/graphql'
        }
        self.payload = query
    
    def _execute_graphql_query(self):
        """
        Sends a GraphQL query to the URL and returns the result.
        """
        response = requests.post(self.graphql_url, headers=self.headers, data=self.payload)
        response.raise_for_status()  # Raise an error for failed requests
        return response.json()

    def _flatten_dict(self, data: Union[dict, list, str, int, float], indent_level: int = 0) -> str:
        """
        Recursively flatten a dictionary, list, or value into a formatted string.
        """
        indent = "  " * indent_level
        if isinstance(data, dict):
            return "\n".join(
                f"{indent}{key}: {self._flatten_dict(value, indent_level + 1)}"
                for key, value in data.items()
            )
        elif isinstance(data, list):
            return "\n".join(
                f"{indent}- {self._flatten_dict(item, indent_level + 1)}"
                for item in data
            )
        else:
            return str(data)

    def load_data(self) -> List[str]:
        """
        Loads data by executing a GraphQL query and returns a list of flattened strings,
        each representing one entry (node) from the GraphQL response.
        """
        results = self._execute_graphql_query()
        data = results.get('data', {})

        flattened_entries = []
        # Assuming we are dealing with a collection of "nodes" in the response
        for key, value in data.items():
            if isinstance(value, list):  # We expect nodes to be in list form
                for entry in value:
                    flattened_entry = self._flatten_dict(entry)
                    flattened_entries.append(flattened_entry)
            else:
                flattened_entries.append(self._flatten_dict(value))

        return flattened_entries
