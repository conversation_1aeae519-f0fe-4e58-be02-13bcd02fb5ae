import React, { useEffect, useMemo, useRef } from "react"

import {
  FaceIcon,
  RocketIcon,
  HeartIcon,
  PlusIcon,
  PlusCircledIcon,
  EnvelopeOpenIcon,
  Link1Icon,
  PaperPlaneIcon,
  ChatBubbleIcon,
  TimerIcon,
} from "@radix-ui/react-icons"

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import useDashboardStore from "@/stores/dashboard.store"
import { useFirestoreSync } from "@/lib/firebase-hooks"
import { FeedbackCategory } from "@/models/feedbackCategory"
import { Document } from "@/models/document"
import { GraphQLSource } from "@/models/graphql-source"

type SelectData = {
  type: string,
  label: string,
  input?: boolean,
  options?: { label: string, value: string }[],
  outputs?: string[]
}

type Item = {
  label: string,
  value: string,
  icon: JSX.Element,
  input?: boolean,
  options?: { label: string, value: string }[],
  outputs?: string[]
}

export default function ({
  onSelect,
}: {
  onSelect: (data: SelectData) => void
}) {

  const selectedClientId = useDashboardStore((state) => state.selectedClientId);

  const queryConstraints = useMemo(() => [], []);
  const {
    paginatedDocuments: categories,
  } = useFirestoreSync<FeedbackCategory>(`tenants/${selectedClientId}/reportCategories`,
    queryConstraints
  );

  const {
    paginatedDocuments: documents,
  } = useFirestoreSync<Document>(
    `tenants/${selectedClientId}/documents`,
    queryConstraints
  );

  const {
    paginatedDocuments: graphql_sources,
  } = useFirestoreSync<GraphQLSource>(
    `tenants/${selectedClientId}/graphql`,
    queryConstraints
  );

  const [isOpen, setIsOpen] = React.useState(false);

  const commandRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        commandRef.current && !commandRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const eventItems = [
    {
      label: "The user starts a conversation",
      value: "event_start",
      icon: <ChatBubbleIcon className="mr-2 h-4 w-4" />
    },
    {
      label: "The user sends a message",
      value: "event_message",
      icon: <PaperPlaneIcon className="mr-2 h-4 w-4" />
    },
    {
      label: "All triggers have been processed",
      value: "event_alltriggers",
      icon: <TimerIcon className="mr-2 h-4 w-4" />
    }
  ] as Item[];

  const triggerItems = [
    {
      label: "User engages in specific topic",
      value: "trigger_topic",
      input: true,
      icon: <FaceIcon className="mr-2 h-4 w-4" />
    },
    {
      label: "User has a specific intent",
      value: "trigger_intent",
      input: true,
      icon: <RocketIcon className="mr-2 h-4 w-4" />
    },
    {
      label: "User has a specific sentiment",
      value: "trigger_sentiment",
      input: true,
      icon: <HeartIcon className="mr-2 h-4 w-4" />
    },
  ] as Item[];

  const actionTriggerItems = [
    {
      label: "Enhance reply with data from all categories",
      value: "action_allcategories",
      icon: <PlusCircledIcon className="mr-2 h-4 w-4" />
    },
    {
      label: "Enhance reply with data from single category",
      value: "action_category",
      options: categories.map((category) => ({
        label: category.label,
        value: category.id,
      })),
      icon: <PlusIcon className="mr-2 h-4 w-4" />
    },
    {
      label: "Enhance reply with specific document from Knowledge Base",
      value: "action_singledata",
      options: documents.map((document) => ({
        label: document.title,
        value: document.id,
      })),
      icon: <Link1Icon className="mr-2 h-4 w-4" />
    },
    {
      label: "Enhance reply with data from GraphQL",
      value: "action_graphql",
      options: graphql_sources.map((source) => ({
        label: source.name,
        value: source.id,
      })),
      icon: <PlusIcon className="mr-2 h-4 w-4" />
    },
    {
      label: "Capture user's details",
      value: "action_capture",
      icon: <EnvelopeOpenIcon className="mr-2 h-4 w-4" />
    },
  ] as Item[];

  const outputItems = [
    {
      label: "Send an AI message to the chat",
      value: "output_message",
      icon: <PaperPlaneIcon className="mr-2 h-4 w-4" />
    },
    {
      label: "Send an email",
      value: "output_email",
      icon: <EnvelopeOpenIcon className="mr-2 h-4 w-4" />
    },
  ] as Item[];

  // New Programmatic items
  const programmaticItems = [
    {
      label: "If",
      value: "programmatic_if",
      input: true,
      icon: <FaceIcon className="mr-2 h-4 w-4" />,
      outputs: ["true", "false"]
    }
  ] as Item[];

  const handleSelect = (item: Item) => {
    onSelect({
      type: item.value,
      label: item.label,
      input: item.input,
      options: item.options,
      outputs: item.outputs
    } as SelectData);
    setIsOpen(false);
  };

  return (
    <div ref={commandRef}>
      <Command className="rounded-lg border shadow-md md:w-[300px] h-auto absolute z-50">
        <CommandInput
          placeholder={`Type a command or search...`}
          onFocus={() => setIsOpen(true)}
        />
        {isOpen && (
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup heading="Events">
              {eventItems.map((item) => (
                <CommandItem
                  key={item.value}
                  onSelect={() => handleSelect(item)}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup heading="Triggers">
              {triggerItems.map((item) => (
                <CommandItem
                  key={item.value}
                  onSelect={() => handleSelect(item)}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup heading="Actions">
              {actionTriggerItems.map((item) => (
                <CommandItem
                  key={item.value}
                  onSelect={() => handleSelect(item)}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup heading="Outputs">
              {outputItems.map((item) => (
                <CommandItem
                  key={item.value}
                  onSelect={() => handleSelect(item)}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup heading="Programmatic">
              {programmaticItems.map((item) => (
                <CommandItem
                  key={item.value}
                  onSelect={() => handleSelect(item)}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        )}
      </Command>
    </div>
  );
}
