import { Report } from "@/models/report";
import React from "react";

type Props = {
  report: Report | null;
};

export default function FeedbackTag({ report }: Props) {
  if (!report) return null;

  return (
    <>
      {report.positiveFeedback ? (
        <span className="flex items-center justify-center p-1 px-2 rounded text-background bg-cyan-700">
          Positive
        </span>
      ) : (
        <span className="flex items-center justify-center p-1 px-2 bg-red-500 rounded text-background">
          Negative
        </span>
      )}
    </>
  );
}
