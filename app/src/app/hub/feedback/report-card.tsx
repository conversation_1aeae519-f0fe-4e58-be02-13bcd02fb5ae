import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { prettyDate } from "@/lib/utils";
import { Report } from "@/models/report";
import React, { ReactNode, useEffect, useMemo } from "react";
import ReportBubble from "./report-bubble";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { FormItem, FormLabel } from "@/components/ui/form";
import FormControl from "@/components/ui/formcontrol";
import { MarkdownEditor } from "@remirror/react-editors/markdown";
import { useHelpers } from "@remirror/react";
import { QueryConstraint, deleteDoc, doc, updateDoc } from "firebase/firestore";
import { db } from "@/services/firebase.config";
import useDashboardStore from "@/stores/dashboard.store";
import { useFirestoreSync } from "@/lib/firebase-hooks";
import { ReportCategory } from "@/models/report-category";
import Switcher from "@/components/ui/switcher";
import FeedbackTag from "./components/feedback-tag";
import SmallLabel from "@/components/ui/small-label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import DialogButton from "@/components/ui/dialog-button";
import {
  getApiEndpoints,
  getFirebaseApiEndpoints,
} from "@/services/api-connector/api-connector";
import { toast } from "@/components/ui/use-toast";

type Props = {
  report: Report | null;
  onOpenChange?: (isOpen: boolean) => void;
};

function MarkdownManager({
  onChange,
}: {
  onChange: (markdown: string) => void;
}) {
  const { getMarkdown } = useHelpers(true);

  useEffect(() => {
    onChange(getMarkdown());
  }, [getMarkdown]);

  return <></>;
}

function ReportCard({ report, onOpenChange }: Props) {
  const [isOpen, setIsOpen] = React.useState(false);

  const [correctReply, setCorrectReply] = React.useState("");
  const [isEditing, setIsEditing] = React.useState(false);

  useEffect(() => {
    if (report) {
      setIsOpen(true);
      onOpenChange && onOpenChange(true);
      setCorrectReply(
        report.correctReply || report.positiveFeedback ? report.message : ""
      );
    }
  }, [report]);

  const selectedClient = useDashboardStore((state) => state.selectedClient);

  const [selectedCategory, setSelectedCategory] = React.useState("");

  const [selectedStatus, setSelectedStatus] = React.useState("");
  const queryContraints = useMemo(() => [] as QueryConstraint[], []);
  const { paginatedDocuments: categories, save: saveCategory, remove: removeCategory } =
    useFirestoreSync<ReportCategory>(
      `tenants/${selectedClient?.value}/reportCategories`,
      queryContraints
    );

  const saveReport = async () => {
    debugger;
    if (!selectedClient || !report) {
      return;
    }

    const docRef = doc(
      db,
      "tenants",
      selectedClient?.value,
      "reports",
      report?.id
    );

    if (docRef) {
      const data = {
        correctReply: correctReply,
        category: selectedCategory,
        status: selectedStatus,
      };

      updateDoc(docRef, data);
    }

    setIsEditing(false);
  };

  const saveReportField = async (id: string, field: string) => {
    if (!selectedClient || !report) {
      return;
    }

    const docRef = doc(
      db,
      "tenants",
      selectedClient?.value,
      "reports",
      report?.id
    );

    if (docRef) {
      const data = {
        [field]: id,
      };

      await updateDoc(docRef, data);
    }

    if (field == "status" && id == "added-to-ai") {
      const response = await getFirebaseApiEndpoints().trainReports(
        selectedClient?.value
      );
      if (response.status == "success") {
        toast({
          title: "Success!",
          description: "The report has been added to the AI training set.",
        });
      }
    }

    setIsEditing(false);
  };

  const deleteReport = async () => {
    if (!selectedClient || !report) {
      return;
    }

    const docRef = doc(
      db,
      "tenants",
      selectedClient?.value,
      "reports",
      report?.id
    );

    if (docRef) {
      await deleteDoc(docRef);
    }
  };

  const reportStatuses = useDashboardStore((state) => state.reportStatuses);

  useEffect(() => {
    if (report && report.status) {
      setSelectedStatus(report.status);
    }
  }, [report]);

  useEffect(() => {
    if (report && report.category) {
      const category = categories.find((c) => c.id == report.category);
      setSelectedCategory(category?.id || "");
    }
  }, [categories]);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={() => {
        setIsOpen(!isOpen);
        onOpenChange && onOpenChange(!isOpen);
      }}
    >
      <DialogContent className="max-w-[800px]">
        {report && (
          <>
            <DialogTitle>
              {prettyDate(new Date(report.createdAt.seconds * 1000))}
            </DialogTitle>
            <DialogDescription>
              <div className="grid grid-cols-12">
                <div className="col-span-9">
                  <div className="max-w-[80px]">
                    <FeedbackTag report={report} />
                  </div>
                  <ReportBubble document={report} role="user" short={false} />
                  <ReportBubble
                    document={report}
                    role="assistant"
                    short={false}
                  />
                  {isEditing && (
                    <>
                      <SmallLabel>Correct reply</SmallLabel>

                      <Textarea
                        value={correctReply}
                        className="min-h-[150px]"
                        placeholder="Enter the correct reply..."
                        onChange={(event) =>
                          setCorrectReply(event.target.value)
                        }
                      />
                      <div className="my-6">
                        <Button
                          variant={"default"}
                          onClick={async () => {
                            report.correctReply = correctReply;
                            await saveReport();
                          }}
                        >
                          Save
                        </Button>
                        <Button
                          variant={"ghost"}
                          onClick={() => setIsEditing(false)}
                        >
                          Cancel
                        </Button>
                      </div>
                    </>
                  )}
                  {!isEditing && (
                    <>
                      {report.correctReply && (
                        <>
                          <SmallLabel>Correct reply</SmallLabel>
                          <div
                            className="pl-2 italic border-l-4 border-cyan-700"
                            dangerouslySetInnerHTML={{
                              __html: report.correctReply,
                            }}
                          />
                        </>
                      )}
                      <div className="my-6">
                        <Button
                          variant={"default"}
                          onClick={() => {
                            setCorrectReply(report.correctReply || "");
                            setIsEditing(true);
                          }}
                        >
                          Edit correct reply
                        </Button>
                      </div>
                    </>
                  )}
                </div>
                <div className="col-span-3">
                  <div className="mb-4">
                    <Label className="block mb-2">Status</Label>
                    <Select
                      onValueChange={async (value) => {
                        setSelectedStatus(value);
                        await saveReportField(value, "status");
                      }}
                      defaultValue={report.status}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {reportStatuses
                          .sort((a: any, b: any) => a.order - b.order)
                          .map((status: any) => (
                            <SelectItem value={status.id}>
                              {status.label}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="mb-4">
                    <Label className="block mb-2">Category</Label>
                    <Select
                      onValueChange={async (value) => {
                        setSelectedCategory(value);
                        await saveReportField(value, "category");
                      }}
                      defaultValue={report.category}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((status) => (
                          <SelectItem value={status.id}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="mb-4">
                    <DialogButton
                      variant={"destructive"}
                      button={<>Delete report</>}
                      callback={() => {
                        deleteReport();
                        setIsOpen(false);
                      }}
                      okayText="Yes, delete"
                      className="w-full"
                      headerText="Are you sure?"
                      closedToastTitle="Success!"
                      closedToastText="The report has been deleted."
                    >
                      <p>
                        Are you sure you want to delete this report? This action
                        cannot be undone.
                      </p>
                    </DialogButton>
                  </div>
                </div>
              </div>
            </DialogDescription>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}

export default ReportCard;
