import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import React, { useMemo } from 'react'

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export type NodeProps = {
  label: string
  type: string,
  useInput?: boolean
  options?: { label: string, value: string }[]
  data: string
}

type TriggerNodeProps = {
  label: string
  type: string,
  useInput?: boolean
  options?: { label: string, value: string }[]
  onDataChange: (data: NodeProps) => void
  data: string
}

export default function TriggerNode({
  label,
  type,
  options,
  useInput = false,
  onDataChange,
  data
}: TriggerNodeProps) {

  const [inputData, setInputData] = React.useState<string>(data)

  const typeName = useMemo(() => {
    if (type.startsWith("trigger")) {
      return "Trigger"
    } else if (type.startsWith("output")) {
      return "Output"
    } else if (type.startsWith("event")) {
      return "Event"
    } else if (type.startsWith("programmatic")) {
      return "Programmatic"
    } else {
      return "Action"
    }
  }, [type])



  return (
    <div className="p-4 text-center w-64">
      <div className="text-xs text-white bg-black p-1 uppercase mb-2">
        {typeName}
      </div>
      <div className="mb-2">
        {label}
      </div>
      {
        useInput &&
        <div className='text-left'>
          <Textarea
            placeholder="Enter value..."
            className='bg-white text-xs'
            value={inputData}
            onChange={(e) => {
              onDataChange({
                label,
                type,
                data: e.target.value,
                options: options || [],
                useInput: useInput || false
              })
              setInputData(e.target.value)
            }} />
        </div>
      }
      {
        options && options.length > 0 &&
        <Select
          value={data}
          onValueChange={(value) => onDataChange({
            label,
            type,
            data: value,
            options: options || [],
            useInput: useInput || false
          })}
        >
          <SelectTrigger className="bg-white py-2">
            <SelectValue placeholder="Select an option" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Select an option</SelectLabel>
              {
                options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))
              }
            </SelectGroup>
          </SelectContent>
        </Select>


      }
    </div>
  )
}