import os
from typing import Any, List, Dict

from llama_index.core.indices import VectorStoreIndex
from llama_index.core.indices.base_retriever import BaseRetriever
from llama_index.core.llms import LLM, ChatMessage
from llama_index.llms.openai import OpenAI
from llama_index.core.memory import BaseMemory, Chat<PERSON>emoryBuffer
from llama_index.core.prompts import PromptTemplate
from llama_index.core.schema import NodeWithScore
from llama_index.core.tools import BaseTool
from llama_index.core.chat_engine.context import ContextChatEngine
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.chat_engine.types import (
    AgentChatResponse,
    BaseChatEngine,
    StreamingAgentChatResponse,
)
from llama_index.core.storage import StorageContext
# import openai
import openai
import tiktoken

from services.engine_context import EngineContext
from models.tenant import Tenant
from readers.reader_loader import ReaderFactory

from repositories.tenant_repository import TenantRepository

from firebase_admin import firestore
from google.cloud.firestore import Client
import hashlib
from llama_index.core.schema import Document
from llama_index.core.settings import Settings
import logging

logging.basicConfig(level=logging.INFO)
from llama_index.core.callbacks.global_handlers import set_global_handler

set_global_handler("simple")
class ModelService: 
  
  # We need to say that a particular tenant needs retraining. This is because we don't want to retrain everything
  needs_retraining: Dict[str, bool] = {}
  
  @classmethod
  def set_needs_retraining(cls, tenant_id: str, needs_retraining: bool):
    
    ModelService.needs_retraining[tenant_id] = needs_retraining
  
  _instances: Dict[str, 'ModelService'] = {} # Class-level dictionary to store instances by tenant_id
  
  def __init__(self, 
      tenant_id: str, 
      database: Client, 
      tenant_repository: TenantRepository, 
      cert_path: str):
    """
    DO NOT USE THIS CONSTRUCTOR. Use the create method instead.
    """
    
    self.database: Client = database
    self.tenant_repository: TenantRepository = tenant_repository
    self.cert_path: str = cert_path
    
    self.tenant_id = tenant_id
    
    cwd = os.path.dirname(os.path.abspath(__file__))
    folder_path = os.path.join(cwd, f"indexes/{tenant_id}/")
    self.folder_name = folder_path
    
    self.indices: Dict[str, VectorStoreIndex] = {}
    

    self.storage_context: StorageContext = None
    self._engine_contexts: Dict[str, EngineContext] = {}
    
    self.storage_contexts: Dict[str, StorageContext] = {}
    
  @classmethod
  def create(cls, database: Client, tenant_id: str) -> 'ModelService':
    """
    Creates a new instance of the ModelService for the given tenant_id. Use this instead of the constructor.
    
    :param database: The firestore database to use for the model service.
    :type database: Client
    
    :param tenant_id: The tenant_id to create the model service for.
    :type tenant_id: str
    
    :return: The model service for the given tenant_id.
    :rtype: ModelService
    """
    if tenant_id in cls._instances:
      # Return existing instance if it exists
      return cls._instances[tenant_id]
    
    tenant_repository = TenantRepository(database)
    cwd = os.path.dirname(os.path.abspath(__file__))
    cert_path = os.path.join(cwd, "../credentials/firebase.json")

    
    model_service = ModelService(tenant_id, database, tenant_repository, cert_path)
    
    model_service.init_storage_context()
    model_service.init_storage_contexts()
    model_service.init_indices()
    
    cls._instances[tenant_id] = model_service
    
    return cls._instances[tenant_id]
  
  def init_storage_context(self):
    """
    Initializes the storage context for the model service. This will be used to store the index from
    the documents in the different data sources.
    """
    
    if self.storage_context is None:
      try:
        self.storage_context = StorageContext.from_defaults(persist_dir=self.folder_name)
      except Exception as e:
        logging.error(f"A storage context could not be created for tenant {self.tenant_id}. Creating one now.")
        self.trainEngine()
        
  def init_storage_contexts(self):
    """
    Initializes the storage contexts for the model service. This will be used to store the index from
    the documents in the different data sources.
    """
    
    if len(self.storage_contexts) == 0:
      try:
        
        categories_ref = (self.database.collection("tenants")
                          .document(self.tenant_id)
                          .collection("reportCategories"))
        
        categories_values = categories_ref.get()
        
        for category in categories_values:
          
          folder_name = self.folder_name + category.id + "/"
          
          # Ensure the folder exists
          if not os.path.exists(folder_name):
            os.mkdir(folder_name)
          
          storage_context = StorageContext.from_defaults(folder_name)
          
          self.storage_contexts[category.id] = storage_context
        
      except Exception as e:
        logging.error(f"Storage contexts could not be created for tenant {self.tenant_id}.")
        logging.error(e)
        
  def init_indices(self):
    """
    Initializes the indices for the model service. This will be used to store the index from
    the documents in the different data sources.
    """
    from llama_index.core.indices import load_index_from_storage
    
    if len(self.indices) == 0:
      try:
        
        for category_id, storage_context in self.storage_contexts.items():
          
          cwd = os.path.dirname(os.path.abspath(__file__))
          folder_path = os.path.join(cwd, f"indexes/{self.tenant_id}/{category_id}")
          
          storage = StorageContext.from_defaults(persist_dir=folder_path)
          
          self.indices[category_id] = load_index_from_storage(storage)
        
      except Exception as e:
        logging.error(f"Indices could not be created for tenant {self.tenant_id}.")
        logging.error(e)
        
  async def chat(self, text: str, chat_history: List[ChatMessage]) -> AgentChatResponse:
    
    
    from llama_index.core.agent import ReActAgent
    from llama_index.core.tools import QueryEngineTool, ToolMetadata
    
    tenant = self.tenant_repository.get_tenant(self.tenant_id)
    
    openai.api_key = tenant.openai.openAIKey
    
    llm = OpenAI(model="gpt-4o", temperature=tenant.openai.temperature, api_key=tenant.openai.openAIKey)
    import tiktoken

    Settings.llm = llm
    Settings.tokenizer = tiktoken.encoding_for_model("gpt-4o").encode
    
    self.init_indices()
    category_ref = (self.database
                      .collection("tenants")
                      .document(self.tenant_id)
                      .collection("reportCategories"))  
    
    categories_values = category_ref.get()

    query_tools = []
    for category in categories_values:
      category_values = category.to_dict()
      
      
      query_tools.append(QueryEngineTool(
          query_engine=self.indices[category.id].as_chat_engine(chat_mode="best", llm=llm),
          metadata=ToolMetadata(
            name=category_values.get("label", "Uncategorized"),
            description=category_values.get("description", "No description available.")
          )
        ))
      
    agent = ReActAgent.from_tools(query_tools, chat_history=chat_history)
    
    return await agent.achat(text)
            
        
    
  
  def getEngine(self, system_messages:List[ChatMessage]) -> BaseChatEngine:
    """
    Retrieves the chat engine for the given tenant_id. This will be used by chatbot requests.
    
    :param system_messages: The system messages to use as a prefix for the chat engine.
    :type system_messages: List[ChatMessage]

    :return: The chat engine for the given tenant_id.
    :rtype: BaseChatEngine
    """
    tenant = self.tenant_repository.get_tenant(self.tenant_id)
    
    needs_retraining = ModelService.needs_retraining.get(self.tenant_id, False)
    
    if "chatbot" not in self._engine_contexts or needs_retraining:
      self._engine_contexts["chatbot"] = EngineContext.from_defaults(tenant, False, system_messages)

      ModelService.set_needs_retraining(self.tenant_id, False)
    
    return self._engine_contexts["chatbot"].get_chat_engine()
  
  def getSystemChatEngine(self, system_messages:List[ChatMessage]) -> BaseChatEngine:
    """
    Gets the system chat engine for the given tenant. The system chat is used for 
    the app's internal processes, and uses GPT-4 for better results.
    
    :param system_messages: The system messages to use as a prefix for the system chat engine.
    :type system_messages: List[ChatMessage]
    
    :return: The system chat engine 
    :rtype: BaseChatEngine
    """
    
    tenant = self.tenant_repository.get_tenant(self.tenant_id)
    
    if "system" not in self._engine_contexts:
      self._engine_contexts["system"] = EngineContext.from_defaults(tenant, True, [], system_messages)
      
    return self._engine_contexts["system"].get_chat_engine()
  
  def getGenericChatEngine(self, system_messages:List[ChatMessage], is_system: bool = False) -> BaseChatEngine:
    tenant = self.tenant_repository.get_tenant(self.tenant_id)
    
    # Key is "generic_system" or "generic"
    key = "generic"
    if is_system:
      key = "generic_system"
    
    if key not in self._engine_contexts:
      self._engine_contexts[key] = EngineContext.from_defaults(tenant, is_system, [], system_messages)
      
    return self._engine_contexts[key].get_generic_chat_engine()

  def trainAndStoreDocuments(self, documents, id):
    folder_name = self.folder_name  + id + "/"
        
    # Ensure the folder exists
    if not os.path.exists(folder_name):
      os.mkdir(folder_name)
    
    storage_context = StorageContext.from_defaults()
    
    VectorStoreIndex.from_documents(
      documents=documents,
      storage_context=storage_context,
      show_progress=True,
    )
    
    storage_context.persist(persist_dir=folder_name)
    
    self.storage_contexts[id] = storage_context
  
  def trainEngine(self):
    """
    Trains the engine for the given tenant, using the tenant's openai model and temperature, 
    and all the data in the different data sources for that tenant.
    
    :param tenant_id: The tenant_id to train the engine for.
    :type tenant_id: str
    """
    try:
      tenant = self.tenant_repository.get_tenant(self.tenant_id)
      
      llm = OpenAI(tenant.openai.model, tenant.openai.temperature, api_key=tenant.openai.openAIKey)


      openai.api_key = tenant.openai.openAIKey

      Settings.llm = llm
      Settings.tokenizer = tiktoken.encoding_for_model("gpt-4o").encode

      categories_ref = (self.database.collection("tenants")
                          .document(self.tenant_id)
                          .collection("reportCategories"))
      
      categories_values = categories_ref.get()
      
      all_documents = []

      
      for category in categories_values:
        
        from readers.website_reader import WebsiteReader
        reader = WebsiteReader(self.database, self.tenant_id, category.id)
        
        documents = reader.load_data()
        
        all_documents.extend(documents)
        
        from readers.graphql_reader import CocurricularReader
        from readers.graphql_cardreader import CardDataReader
              
        if(self.tenant_id == 'pFxlF5QzbKrZqidg5Gdf' and category.id == '8pf2AC39M4o0fTl2ol3b'):
          cocurricular_reader = CocurricularReader()
          cocurricular_documents = cocurricular_reader.load_data()
          documents.extend(cocurricular_documents)
          
        if(self.tenant_id == 'pFxlF5QzbKrZqidg5Gdf' and category.id == 'dnc9BZZ0dXhiyDNTB1uk'):
          cardDataReader = CardDataReader()
          docs = cardDataReader.load_data()
          documents.extend(docs)
          
          
        
        self.trainAndStoreDocuments(documents, category.id)
        
      

        
        
      
      from readers.knowledgebase_reader import KBDocumentsReader

      kb_documents = KBDocumentsReader(self.database, self.tenant_id).load_data()
      
      all_documents.extend(kb_documents)
            
      self.trainAndStoreDocuments(all_documents, "all")
      
    except Exception as e:
      logging.error(f"An error occurred while training the engine for tenant {self.tenant_id}.")
      logging.error(e)
    
  @classmethod
  def trainGPT4(cls, tenant_id: str, database: Client):
    
    index_ref = database.collection(f'tenants/{tenant_id}/gpt4Index')
    
    from readers.feedback_reader import FeedbackReader
    
    feedback_documents = FeedbackReader(database, tenant_id).load_data()
    
    # Get all the messages from Firestore
    
    all_messages = []
    for doc in index_ref.stream():
        obj = doc.to_dict()
        all_messages.append(obj)
        
    # Group feedback by category
    grouped_data = {}
    for message in all_messages:
        category = message['category'] or "Uncategorized"
        correct_reply = message['message']
        question = message['question']

        text = f"""
        ----------------------------------------
        Category: {category}
        Question: {question}
        Answer: {correct_reply}
        
        ----------------------------------------
        """

        if category not in grouped_data:
            grouped_data[category] = []
        grouped_data[category].append(text)
        
    # Create one document per category
    for category, texts in grouped_data.items():
        merged_text = ("Category: " + category + "\n\n" 
                        + "\n\n".join(texts))
        category_id_hash = hashlib.md5(category.encode()).hexdigest()
        feedback_documents.append(
            Document(text=merged_text, doc_id=category_id_hash, doc_hash=category_id_hash)
        )
            
    
    cwd = os.path.dirname(os.path.abspath(__file__))
    folder_path = os.path.join(cwd, f"indexes/{tenant_id}_GPT4/")
    folder_name = folder_path
    
    doc_ref = database.collection('tenants').document(tenant_id).get()
    openai_key = doc_ref.to_dict()['openai']['openAIKey']
    
    # llm = OpenAI(model="gpt-3.5-turbo", temperature=0.1, api_key=openai_key)
    from llama_index.llms.groq import Groq
    llm = Groq(model="mixtral-8x7b-32768", api_key="********************************************************")
    import tiktoken

    Settings.llm = llm
    Settings.tokenizer = tiktoken.encoding_for_model("mixtral-8x7b-32768").encode
    
    # If folder doesn't exist make it
    if not os.path.exists(folder_name):
        os.mkdir(folder_name)      
        
    storage_context = StorageContext.from_defaults()
            
    VectorStoreIndex.from_documents(
        documents=feedback_documents,
        storage_context=storage_context,
        show_progress=True,
    )
    
    storage_context.persist(persist_dir=folder_name)
    
    
    
    ModelService.set_needs_retraining(tenant_id, True)