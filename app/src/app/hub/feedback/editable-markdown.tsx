import "@remirror/styles/all.css";

import React, { useEffect } from "react";
import { useHelpers } from "@remirror/react";
import { MarkdownEditor } from "@remirror/react-editors/markdown";
import EventHandler from "@/services/event-handler";

function ManageEvents() {
  const { getMarkdown } = useHelpers(true);

  const onGetMarkdown = () => {
    const mdValue = getMarkdown();

    EventHandler.dispatch("markdownChanged", mdValue);
  };

  useEffect(() => {
    const unsubscribe = EventHandler.subscribe("getMarkdown", onGetMarkdown);

    return () => {
      unsubscribe();
    };
  }, [getMarkdown]);

  return <></>;
}

const MdEditor = ({
  initialContent,
  placeholder,
}: {
  initialContent: string;
  placeholder: string;
}) => (
  <MarkdownEditor placeholder={placeholder} initialContent={initialContent}>
    <ManageEvents />
  </MarkdownEditor>
);

export default MdEditor;
