import { storage } from "firebase-admin";
import * as xml2js from 'xml2js';
import * as cheerio from 'cheerio';
import { getTenant } from "@/repositories/server-tenant-repository";
import { CheerioAPI } from 'cheerio';
import { Sitemap } from "@/models/sitemap";
import { SitemapPage } from "@/models/sitemap-page";
import { FeedbackCategory } from "@/models/feedbackCategory";
import OpenAI from "openai";

const standardHTMLTags: string[] = [
  'a', 'abbr', 'address', 'area', 'article', 'aside', 'audio', 'b', 'base', 'bdi', 'bdo',
  'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'cite', 'code', 'col',
  'colgroup', 'data', 'datalist', 'dd', 'del', 'details', 'dfn', 'dialog', 'div', 'dl',
  'dt', 'em', 'embed', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2',
  'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i',
  'input', 'ins', 'kbd', 'keygen', 'label', 'legend', 'li', 'link', 'main', 'map', 'mark',
  'menu', 'menuitem', 'meta', 'meter', 'nav', 'noscript', 'object', 'ol', 'optgroup',
  'option', 'output', 'p', 'param', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby',
  's', 'samp', 'section', 'select', 'small', 'source', 'span', 'strong',
  'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th',
  'thead', 'time', 'title', 'tr', 'track', 'u', 'ul', 'var', 'video', 'wbr'
];

const tagsToRemove: string[] = [
  'script', 'noscript', 'style', 'link', 'meta', 'base', 'head', 'html', 'body', 'div', 'section','main', 'article', 'aside', 'header', 'footer'
];

const tagsToRemoveCompletely = [
  'script', 'style', 'noscript', 'form', 'select', 'input', 'label', 'textarea'
];

const fetchWithRetry = async (url: string, retries = 3, timeout = 5000) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const controller = new AbortController();
      const id = setTimeout(() => controller.abort(), timeout);
      const response = await fetch(url, { signal: controller.signal });
      clearTimeout(id);
      if (!response.ok) throw new Error(`Fetch failed with status ${response.status}`);
      return response;
    } catch (error) {
      if (attempt === retries) throw error;
      console.log(`Attempt ${attempt} failed, retrying...`);
    }
  }
};



export const getFullDataFromSitemap = async (sitemap: Sitemap, tenantId: string) : Promise<SitemapPage[]> => {
  const tenant = await getTenant(tenantId);
  const selector = sitemap.domSelector || tenant?.website?.defaultSelector;

  if (!selector) {
    throw new Error('Default selector not found for the tenant');
  }

  const response = await fetch(sitemap.sitemap);
  const xmlData = await response.text();

  const parser = new xml2js.Parser();
  let parsedXml: any;

  parser.parseString(xmlData, (err: any, result: any) => {
    if (err) {
      throw err;
    }
    parsedXml = result;
  });

  const urls = parsedXml.urlset.url || [];

  const outObj = await Promise.all(
    urls.map(async (url: any) => {
      try { 

        const pageUrl = url.loc[0];
        const lastMod = url.lastmod[0];
        const pageResponse = await fetchWithRetry(pageUrl);
  
        const progressPercent = Math.round((urls.indexOf(url) / urls.length) * 100);
        const progressString = `Fetching page ${urls.indexOf(url) + 1} of ${urls.length} (${progressPercent}%) - Url: ${pageUrl}`;
  
        // console.log(progressString);
        
        if (!response || !response.ok) {
          console.error(`Error fetching page: ${pageUrl}`);
          return null;
        }
  
        const html = await pageResponse?.text() ?? "";
        const $ = cheerio.load(html);
        const title = $('title').text();
        const content = $(selector).html() ?? "";
  
        // Remove any non-standard HTML tags and their content
        const sanitizedContent = sanitizeHtml($, content);
  
        // Remaining sanitization steps
        const finalContent = sanitizedContent
          .replace(/\n|\t/g, '')
          .replace(/\s{2,}/g, ' ')
          .replace(/ >/g, '>')
          .replace(new RegExp(`<(${tagsToRemove.join('|')})[^>]*>`, 'g'), '')
          .replace(new RegExp(`</(${tagsToRemove.join('|')})>`, 'g'), '')
          .replace(/<!--[^>]*-->/g, '');
  
        const relativeUrl = pageUrl.replace(/(https?:\/\/[^/]+)?/, '') as string;
  
        // take the relative URL and generate a hex page ID that's unique to that URL. 10 characters long.
        const reverseUrl = relativeUrl.split('').reverse().join('');
        const pageId = Buffer.from(reverseUrl).toString('hex').slice(0, 10);
  
        return {
          id: pageId,
          title,
          url: pageUrl,
          lastMod,
          content: finalContent,
          sitemapId: sitemap.id
        } as SitemapPage;
      } catch (error) {
        console.error(`Error fetching page: ${url.loc[0]}`);
        console.error(error);
        return null;
      }
    })
  );

  return outObj;
}
function flattenDivsAndSections($: cheerio.CheerioAPI): void {
  let isFlattened = true; // Assume flattening will happen at least once
  while (isFlattened) { // Continue until no more flattening occurs
    isFlattened = false; // Reset flag
    $('div, section').each((index, elem) => {
      const children = $(elem).children();
      if (children.length === 1 && (children.is('div') || children.is('section'))) {
        $(elem).replaceWith(children);
        isFlattened = true; // Flattening occurred, check again
      }
    });
  }
}

function sanitizeHtml($: cheerio.CheerioAPI, 
  html: string): string {
  const temp$ = cheerio.load(html);

  temp$('body').find('*').each((index, elem) => {
    if (elem.type === 'tag') {
      // Remove all attributes except href
      const attributes = elem.attribs;
      const href = attributes.href; // Save href if it exists
      Object.keys(attributes).forEach(attr => {
        temp$(elem).removeAttr(attr); // Remove all attributes
      });
      if (href) temp$(elem).attr('href', href); // Re-add href if it was present

      // Remove tags not in the list of standard HTML tags
      if (!standardHTMLTags.includes(elem.tagName.toLowerCase())) {
        temp$(elem).remove();
      } else if (temp$(elem).text().trim() === '' && !elem.tagName.toLowerCase().match(/(br|hr|img)/)) {
        // Remove empty elements, exempting self-closing tags
        temp$(elem).remove();
      }
    }
  });

  tagsToRemoveCompletely.forEach(tag => {
    temp$(tag).remove();
  });

  flattenDivsAndSections(temp$);

  return temp$('body').html() ?? "";
}

