from typing import List, Dict, Any
from llama_index.core.readers.base import BaseReader
from llama_index.core.schema import Document
from firebase_admin.firestore import firestore
from google.cloud.firestore import Client

class KBDocument:
    def __init__(self, data: str, id: str):
        self.data = data
        self.id = id

class KBDocumentsReader(BaseReader):
    
    def __init__(self, db: Client, tenant_id: str):
        self.db = db
        self.tenant_id = tenant_id

    def load_data(self, *args: Any, **load_kwargs: Any) -> List[Document]:
        custom_documents = self.get_kb_documents()
        document_data = []

        for custom_document in custom_documents:
            document_data.append(Document(
                doc_id=custom_document.id,
                doc_hash=custom_document.id,
                text=custom_document.data
            ))

        return document_data

    def get_kb_documents(self) -> List[KBDocument]:
        documents_ref = (self.db.collection('tenants')
                         .document(self.tenant_id)
                         .collection('documents'))

        document_docs = documents_ref.get()
        custom_documents = []

        for doc in document_docs:
            document_data = KBDocument(data=doc.to_dict().get('data', ''), id=doc.id)  # Used .get() for safer access
            custom_documents.append(document_data)

        return custom_documents
