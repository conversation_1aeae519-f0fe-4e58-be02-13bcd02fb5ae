import { TableCell, TableRow } from "@/components/ui/table";
import { capitalise, cn } from "@/lib/utils";
import React from "react";
import { FormConfig, IFirebaseDTO } from "./firebase-datatable";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import DialogButton from "@/components/ui/dialog-button";
import { DTOForm } from "./dto-form";
import ConfirmButton from "@/components/ui/confirm-button";

type Props = {
  configKeys: string[];
  formConfig: FormConfig;
  item: IFirebaseDTO;
  index: number;
  moveRow: (dragIndex: number, hoverIndex: number) => void;
  hoveredIndex: number;
  setHoveredIndex: (index: number) => void;
  term: string;
  setSelectedDTO: (dto: IFirebaseDTO) => void;
  selectedDTO: IFirebaseDTO;
  updateDto: (dto: IFirebaseDTO) => void;
  deleteDto: (id: string) => void;
};

export const DraggableTableRow = ({
  item,
  index,
  moveRow,
  configKeys,
  formConfig,
  hoveredIndex,
  setHoveredIndex,
  term,
  setSelectedDTO,
  selectedDTO,
  updateDto,
  deleteDto,
}: Props) => {
  const ref = React.useRef(null);
  const [, refDrop] = useDrop({
    accept: "ROW",
    hover: (draggedItem: IFirebaseDTO) => {
      if (parseInt(draggedItem.index) !== index) {
        moveRow(parseInt(draggedItem.index), index);
        draggedItem.index = index.toString();
      }
    },
  });

  const [, refDrag] = useDrag({
    type: "ROW",
    item: { index },
  });

  refDrop(refDrag(ref));

  return (
    <TableRow
      className={cn(
        index % 2 == 1 ? "bg-muted/20" : "bg-background",
        "cursor-pointer relative"
      )}
      key={`${item.id}-${index}`}
      onMouseEnter={() => setHoveredIndex(index)}
      onMouseLeave={() => setHoveredIndex(-1)}
    >
      {configKeys.length == 1 && (
        <TableCell className="flex gap-2 text-xs"></TableCell>
      )}

      {configKeys.map((key, index) => {
        const configItem = formConfig[key];

        if (configItem.detailOnly) {
          return null;
        }

        return (
          <TableCell
            className={
              index == 0 && configKeys.length > 1 ? "font-bold" : "font-xs"
            }
            key={key}
          >
            {<>{item[configItem.key]}</>}
          </TableCell>
        );
      })}

      <TableCell className="flex gap-2 text-xs">
        {hoveredIndex === index && (
          <>
            <DialogButton
              fullButton={<div className="underline">Edit</div>}
              headerText={`Edit ${term}`}
              closedToastTitle={`${capitalise(term)} updated!`}
              closedToastText={`Your ${term} has been updated.`}
              variant={"outline"}
              okayText={`Save ${term}`}
              className="mt-4"
              onOpenChange={(open) => {
                if (!open) {
                  setSelectedDTO(null as unknown as IFirebaseDTO);
                } else {
                  setSelectedDTO(item);
                }
              }}
              callback={() => {
                updateDto(selectedDTO);
                setSelectedDTO(null as unknown as IFirebaseDTO);
              }}
            >
              <div className="flex flex-col gap-4">
                <p>Edit the {term.toLowerCase()}</p>

                <DTOForm
                  dto={selectedDTO}
                  setDTO={setSelectedDTO}
                  config={formConfig}
                />
              </div>
            </DialogButton>

            <ConfirmButton
              callback={async () => await deleteDto(item.id)}
              button={
                <div className="px-1 rounded bg-destructive text-background">
                  Delete
                </div>
              }
              title="Are you sure?"
            >
              Yes, delete this {term.toLowerCase()}.
            </ConfirmButton>
          </>
        )}
      </TableCell>
    </TableRow>
  );
};
