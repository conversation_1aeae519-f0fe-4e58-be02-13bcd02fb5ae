from typing import Any, List, Optional, Dict
from llama_index.core.llms import ChatMessage

class WordPressConfig:
    def __init__(self, url: str, username: str, password: str, consumerKey: str, consumerSecret: str):
        self.url = url
        self.username = username
        self.password = password
        self.consumerKey = consumerKey
        self.consumerSecret = consumerSecret

class OpenAIConfig:
    def __init__(self, model: str, openAIKey: str, temperature: int, topK: Optional[int] = None):
        self.model = model
        self.openAIKey = openAIKey
        self.temperature = temperature
        self.topK = topK

class WidgetConfig:
    def __init__(self, buttonColour: str, 
                 buttonIcon: str, 
                 buttonIconColour: str, 
                 buttonPosition: str, 
                 shadow: str, 
                 chatTitle: str, 
                 assistantName: str):
        self.buttonColour = buttonColour
        self.buttonIcon = buttonIcon
        self.buttonIconColour = buttonIconColour
        self.buttonPosition = buttonPosition
        self.shadow = shadow
        self.chatTitle = chatTitle
        self.assistantName = assistantName
        

class Tenant:
    def __init__(self, id: str, name: str, description: str, openai: Optional[OpenAIConfig] = None, systemMessages: Optional[List[str]] = None, widget: Optional[WidgetConfig] = None, wordpressConfig: Optional[WordPressConfig] = None):
        self.name = name
        self.description = description
        self.openai = openai
        self.systemMessages = systemMessages
        self.widget = widget
        self.wordpressConfig = wordpressConfig
        self.id = id
        
    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'Tenant':
        name = obj.get("name", "")
        description = obj.get("description", "")
        openai = OpenAIConfig(**obj['openai']) if obj.get('openai') else None
        system_messages = obj.get("systemMessages", [])
        widget = WidgetConfig(**obj['widget']) if obj.get('widget') else None
        wordpress_config = WordPressConfig(**obj['wordpressConfig']) if obj.get('wordpressConfig') else None
        id = obj.get("id", "")

        return Tenant(
            id=id,
            name=name,
            description=description,
            openai=openai,
            systemMessages=system_messages,
            widget=widget,
            wordpressConfig=wordpress_config
        )
        
    def get_system_chat_messages(self):
        system_messages = []
        for x in self.systemMessages:
            system_messages.append(ChatMessage(role="system", content=x))
            
        system_messages.append(
            ChatMessage(
                role="system", 
                content="""Always use at least one tool, and don't reply things like 
                - "I cannot answer the question with the provided tools."
                - "I don't know."
                - "I am not sure."  
                - "[End of conversation]" 
                
                Instead, stay conversational and try to provide a helpful response.
                """)
        )
        
        return system_messages
    
        
      
      
