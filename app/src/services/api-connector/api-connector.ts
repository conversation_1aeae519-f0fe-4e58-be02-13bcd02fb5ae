import { httpsCallable } from "firebase/functions";
import { functions } from "../firebase.config";
import {
  CreateSessionRequest,
  CreateSessionResponse,
  DiscussRequest,
  DiscussResponse,
  IFunctionRequest,
  IFunctionResponse,
  ReportResponse,
  SpeechRequest,
  SystemChatRequest,
  SystemChatResponse,
  TrainRequest,
  TrainResponse,
} from "./types";
import { ReportRequest, TrainReportRequest } from "@/models/report";
import { SpeechToTextResponse } from "@/models/speech";

enum ApiEndpointsType {
  discuss = "chat",
  systemChat = "system_chat",
  train = "retrain",
  createSession = "create_session",
  report = "report",
  speechToText = "speech_to_text",
  trainReports = "train_reports",
}

export const firebaseFunctionsCaller = async (
  endpoint: ApiEndpointsType,
  request: IFunctionRequest
): Promise<IFunctionResponse> => {
  console.log(`Calling function ${endpoint} with request:`, { request });
  const result = await httpsCallable(functions, endpoint)(request);
  return result.data as IFunctionResponse;
};

export const httpsFunctionsCaller = async (
  endpoint: ApiEndpointsType,
  request: IFunctionRequest
): Promise<IFunctionResponse> => {
  const isDevMode = process.env.NODE_ENV === "development";

  const endpointUrl = isDevMode
    ? `http://127.0.0.1:5000/${endpoint}`
    : `https://api.chatnav.co.uk/${endpoint}`;

  console.log(`Calling function ${endpointUrl} with request:`, { request });

  const response = await fetch(endpointUrl, {
    method: "POST",
    body: JSON.stringify(request),
    headers: {
      "Content-Type": "application/json",
    },
  });

  const result = await response.json();
  return result as IFunctionResponse;
};

export const httpsSpeechToTextCaller = async (
  file: File,
  endpoint: string,
  data?: { name: string; value: string }[]
): Promise<SpeechToTextResponse | DiscussResponse> => {
  const isDevMode = process.env.NODE_ENV === "development";

  const endpointUrl = isDevMode
    ? `http://127.0.0.1:5000/${endpoint}`
    : `https://api.chatnav.co.uk/${endpoint}`;
  const formData = new FormData();
  formData.append("file", file);

  if (data) {
    data.forEach((item) => {
      formData.append(item.name, item.value);
    });
  }

  const response = await fetch(endpointUrl, {
    method: "POST",
    body: formData,
  });

  const result = await response.json();
  return result;
};

export const getApiEndpoints = (
  caller: (
    endpoint: ApiEndpointsType,
    request: IFunctionRequest
  ) => Promise<IFunctionResponse>
) => {
  const discuss = async (request: DiscussRequest): Promise<DiscussResponse> => {
    const result = await caller(ApiEndpointsType.discuss, request);
    return result as DiscussResponse;
  };

  const systemChat = async (
    request: SystemChatRequest
  ): Promise<SystemChatResponse> => {
    const result = await caller(ApiEndpointsType.systemChat, request);
    return result as SystemChatResponse;
  };

  const train = async (request: TrainRequest): Promise<TrainResponse> => {
    const result = await caller(ApiEndpointsType.train, request);
    return result as TrainResponse;
  };

  const createSession = async (
    request: CreateSessionRequest
  ): Promise<CreateSessionResponse> => {
    const result = await caller(ApiEndpointsType.createSession, request);
    return result as CreateSessionResponse;
  };

  const report = async (request: ReportRequest): Promise<ReportResponse> => {
    const result = await caller(ApiEndpointsType.report, request);

    return result as ReportResponse;
  };

  const trainReports = async (
    request: TrainReportRequest
  ): Promise<ReportResponse> => {
    const result = await caller(ApiEndpointsType.trainReports, request);

    return result as ReportResponse;
  };

  const speechToText = async (file: File): Promise<SpeechToTextResponse> => {
    const result = await httpsSpeechToTextCaller(file, "speech_to_text");

    return result as SpeechToTextResponse;
  };

  const sttChat = async (file: File, request: SpeechRequest) => {
    const data = [
      {
        name: "sessionId",
        value: request.sessionId,
      },
      {
        name: "tenantId",
        value: request.tenantId,
      },
    ];

    const result = await httpsSpeechToTextCaller(file, "sttchat", data);

    return result as DiscussResponse;
  };

  const isDevMode = process.env.NODE_ENV === "development";

  const chatEndpoint = isDevMode
    ? `http://127.0.0.1:5000/chat`
    : `https://api.chatnav.co.uk/chat`;

  return {
    discuss,
    systemChat,
    train,
    createSession,
    report,
    speechToText,
    sttChat,
    trainReports,
    chatEndpoint,
  };
};

export const getFirebaseApiEndpoints = () => {
  return getApiEndpoints(httpsFunctionsCaller);
};
