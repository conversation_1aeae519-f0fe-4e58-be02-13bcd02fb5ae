"use client";
import React from "react";

import { useEffect } from "react";
import DocumentTable from "@/components/Documents/DocumentTable";

import WandIcon from "@/components/icons/wand";
import { useRouter } from "next/navigation";

export default function DashboardPage() { 

  const router = useRouter();

  useEffect(() => {
    router.push('/hub/documents');
  }, []);

  return (
    <>
      
    </>
  );
}
